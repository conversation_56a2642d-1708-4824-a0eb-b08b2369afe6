(window.webpackJsonpvanillaforum=window.webpackJsonpvanillaforum||[]).push([[5],Array(503).concat([function(e,t,o){e.exports=o(735)()},function(e,t,o){"use strict";t.__esModule=!0,t.default=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t,o){"use strict";t.__esModule=!0;var n,r=o(596),i=(n=r)&&n.__esModule?n:{default:n};t.default=function(){function e(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),(0,i.default)(e,n.key,n)}}return function(t,o,n){return o&&e(t.prototype,o),n&&e(t,n),t}}()},function(e,t){var o=e.exports={version:"2.5.7"};"number"==typeof __e&&(__e=o)},function(e,t,o){e.exports={default:o(702),__esModule:!0}},function(e,t,o){"use strict";t.__esModule=!0;var n,r=o(597),i=(n=r)&&n.__esModule?n:{default:n};t.default=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":(0,i.default)(t))&&"function"!=typeof t?e:t}},function(e,t,o){"use strict";t.__esModule=!0;var n=a(o(725)),r=a(o(729)),i=a(o(597));function a(e){return e&&e.__esModule?e:{default:e}}t.default=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":(0,i.default)(t)));e.prototype=(0,r.default)(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(n.default?(0,n.default)(e,t):e.__proto__=t)}},function(e,t,o){"use strict";o.d(t,"a",function(){return g}),o.d(t,"b",function(){return j});var n=o(66),r=o(3),i=o(6),a=o(8),l=o(1),u=o(9),s=o(2),c=o(23),p=o(0),f=o.n(p),h=o(49),d=f.a.createContext({}),m=d.Consumer,g=d.Provider;var v=Object(h.b)(function(e,t){var o=t.editorID,n=t.quill,r=(t.legacyMode,e.editor.instances[o]),i=r.lastGoodSelection,a=i?n.getFormat(i):{};return Object(c.a)({},r,{activeFormats:a})});function j(e){var t=v(e),o=e.displayName||e.name||"Component",c=function(e){function o(){return Object(r.a)(this,o),Object(a.a)(this,Object(l.a)(o).apply(this,arguments))}return Object(u.a)(o,e),Object(i.a)(o,[{key:"render",value:function(){var e=this;return f.a.createElement(m,null,function(o){return f.a.createElement(t,Object(n.a)({},o,e.props))})}}]),o}(f.a.Component);return Object(s.a)(c,"displayName","withEditor(".concat(o,")")),c}},function(e,t){var o=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=o)},function(e,t,o){var n=o(560)("wks"),r=o(543),i=o(511).Symbol,a="function"==typeof i;(e.exports=function(e){return n[e]||(n[e]=a&&i[e]||(a?i:r)("Symbol."+e))}).store=n},function(e,t,o){var n=o(511),r=o(506),i=o(530),a=o(521),l=o(520),u=function(e,t,o){var s,c,p,f=e&u.F,h=e&u.G,d=e&u.S,m=e&u.P,g=e&u.B,v=e&u.W,j=h?r:r[t]||(r[t]={}),b=j.prototype,y=h?n:d?n[t]:(n[t]||{}).prototype;for(s in h&&(o=t),o)(c=!f&&y&&void 0!==y[s])&&l(j,s)||(p=c?y[s]:o[s],j[s]=h&&"function"!=typeof y[s]?o[s]:g&&c?i(p,n):v&&y[s]==p?function(e){var t=function(t,o,n){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,o)}return new e(t,o,n)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(p):m&&"function"==typeof p?i(Function.call,p):p,m&&((j.virtual||(j.virtual={}))[s]=p,e&u.R&&b&&!b[s]&&a(b,s,p)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},function(e,t,o){var n=o(518);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},function(e,t,o){"use strict";t.__esModule=!0;var n,r=o(606),i=(n=r)&&n.__esModule?n:{default:n};t.default=i.default||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e}},function(e,t,o){"use strict";o.d(t,"c",function(){return s}),o.d(t,"k",function(){return c}),o.d(t,"o",function(){return p}),o.d(t,"d",function(){return f}),o.d(t,"l",function(){return h}),o.d(t,"g",function(){return d}),o.d(t,"m",function(){return m}),o.d(t,"h",function(){return g}),o.d(t,"i",function(){return v}),o.d(t,"b",function(){return j}),o.d(t,"e",function(){return b}),o.d(t,"n",function(){return y}),o.d(t,"f",function(){return _}),o.d(t,"j",function(){return O}),o.d(t,"a",function(){return C});var n=o(0),r=o.n(n),i=o(22),a=o.n(i),l=o(5),u={fill:"currentColor"};function s(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Bold")),r.a.createElement("path",{d:"M6.511,18v-.62a4.173,4.173,0,0,0,.845-.093.885.885,0,0,0,.736-.79,5.039,5.039,0,0,0,.063-.884V8.452a6.585,6.585,0,0,0-.047-.876,1.116,1.116,0,0,0-.194-.527.726.726,0,0,0-.4-.263,3.658,3.658,0,0,0-.674-.1v-.62h4.975a7.106,7.106,0,0,1,3.6.752A2.369,2.369,0,0,1,16.68,8.964q0,1.843-2.651,2.6v.062a4.672,4.672,0,0,1,1.542.24,3.39,3.39,0,0,1,1.171.674,3.036,3.036,0,0,1,.744,1.023,3.125,3.125,0,0,1,.263,1.287,2.49,2.49,0,0,1-.38,1.379,3.05,3.05,0,0,1-1.092.992,7.794,7.794,0,0,1-3.8.775Zm6.076-.945q2.5,0,2.5-2.248a2.3,2.3,0,0,0-.9-2.015,3.073,3.073,0,0,0-1.2-.465,9.906,9.906,0,0,0-1.806-.139h-.744v3.1a1.664,1.664,0,0,0,.5,1.364A2.659,2.659,0,0,0,12.587,17.055Zm-1.24-5.8a4.892,4.892,0,0,0,1.21-.131,2.69,2.69,0,0,0,.868-.38,1.8,1.8,0,0,0,.743-1.6,2.107,2.107,0,0,0-.557-1.635,2.645,2.645,0,0,0-1.8-.5h-1.1q-.279,0-.279.264v3.983Z",style:u}))}function c(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Italic")),r.a.createElement("path",{d:"M11.472,15.4a4.381,4.381,0,0,0-.186,1.085.744.744,0,0,0,.333.713,2.323,2.323,0,0,0,1.077.186L12.51,18H7.566l.17-.62a3.8,3.8,0,0,0,.791-.07,1.282,1.282,0,0,0,.566-.271,1.62,1.62,0,0,0,.41-.558,5.534,5.534,0,0,0,.326-.93L11.642,8.7a5.332,5.332,0,0,0,.233-1.271.577.577,0,0,0-.349-.612,3.714,3.714,0,0,0-1.186-.132l.171-.62h5.038l-.171.62a3.058,3.058,0,0,0-.852.1,1.246,1.246,0,0,0-.59.38,2.578,2.578,0,0,0-.441.774,11.525,11.525,0,0,0-.4,1.287Z",style:u}))}function p(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Strikethrough")),r.a.createElement("path",{d:"M12.258,13H6V12h4.2l-.05-.03a4.621,4.621,0,0,1-1.038-.805,2.531,2.531,0,0,1-.55-.892A3.285,3.285,0,0,1,8.4,9.2a3.345,3.345,0,0,1,.256-1.318,3.066,3.066,0,0,1,.721-1.046,3.242,3.242,0,0,1,1.1-.682,3.921,3.921,0,0,1,1.4-.24,3.641,3.641,0,0,1,1.271.217,4.371,4.371,0,0,1,1.194.7l.4-.7h.357l.171,3.085h-.574A3.921,3.921,0,0,0,13.611,7.32a2.484,2.484,0,0,0-1.7-.619,2.269,2.269,0,0,0-1.5.465,1.548,1.548,0,0,0-.558,1.255,1.752,1.752,0,0,0,.124.674,1.716,1.716,0,0,0,.4.574,4.034,4.034,0,0,0,.729.542,9.854,9.854,0,0,0,1.116.566,20.49,20.49,0,0,1,1.906.953q.232.135.435.27h4.6v1H15.675a2.263,2.263,0,0,1,.3.544,3.023,3.023,0,0,1,.186,1.093,3.236,3.236,0,0,1-1.177,2.541,4.014,4.014,0,0,1-1.334.721,5.393,5.393,0,0,1-1.7.256,4.773,4.773,0,0,1-1.588-.248,4.885,4.885,0,0,1-1.434-.837l-.434.76H8.132L7.9,14.358h.573a3.886,3.886,0,0,0,.411,1.255A3.215,3.215,0,0,0,10.7,17.155a3.872,3.872,0,0,0,1.294.21,2.786,2.786,0,0,0,1.813-.543,1.8,1.8,0,0,0,.667-1.473,1.752,1.752,0,0,0-.573-1.34,4.04,4.04,0,0,0-.83-.6Q12.723,13.217,12.258,13Z",style:u}))}function f(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Paragraph Code Block")),r.a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M4.818,14.032q0-1.27-1.818-1.271V11.455a2.429,2.429,0,0,0,1.371-.311,1.075,1.075,0,0,0,.447-.947V8.071A1.763,1.763,0,0,1,5.56,6.5,4,4,0,0,1,7.785,6V7.244a1.949,1.949,0,0,0-1.131.318,1.019,1.019,0,0,0-.352.844v2.031q0,1.359-1.566,1.626v.083q1.566.246,1.566,1.62V15.81a1.034,1.034,0,0,0,.348.847,1.836,1.836,0,0,0,1.135.3v1.251a3.89,3.89,0,0,1-2.273-.537,1.982,1.982,0,0,1-.694-1.692ZM9.009,15.14a1.05,1.05,0,0,1,.26-.766,1,1,0,0,1,.752-.267.992.992,0,0,1,.758.277,1.063,1.063,0,0,1,.26.756,1.089,1.089,0,0,1-.263.769.98.98,0,0,1-.755.283.973.973,0,0,1-.752-.28A1.088,1.088,0,0,1,9.009,15.14Zm3.848,0a1.054,1.054,0,0,1,.26-.766,1.185,1.185,0,0,1,1.511.01,1.063,1.063,0,0,1,.26.756,1.089,1.089,0,0,1-.263.769,1.151,1.151,0,0,1-1.508,0A1.093,1.093,0,0,1,12.857,15.14Zm6.105.991a1.811,1.811,0,0,1-.68,1.565,3.788,3.788,0,0,1-2.178.513V16.958a1.765,1.765,0,0,0,1.012-.263,1.009,1.009,0,0,0,.363-.885V14.019a1.994,1.994,0,0,1,.362-1.279,1.879,1.879,0,0,1,1.2-.594v-.083q-1.566-.266-1.565-1.626V8.406a1.084,1.084,0,0,0-.312-.844A1.69,1.69,0,0,0,16.1,7.244V6a3.583,3.583,0,0,1,2.191.523,2.044,2.044,0,0,1,.667,1.712V10.2a1.077,1.077,0,0,0,.434.971,2.356,2.356,0,0,0,1.289.287v1.306a2.342,2.342,0,0,0-1.282.29,1.087,1.087,0,0,0-.441.981Z"}))}function h(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Link")),r.a.createElement("path",{d:"M13.575,10.4a3.824,3.824,0,0,1,.266,5.107c-.17.211-.076.106-2.318,2.348a3.821,3.821,0,0,1-5.4-5.4l1.731-1.731a.327.327,0,0,1,.559.223,4.735,4.735,0,0,0,.129,1.033.325.325,0,0,1-.087.3L7.2,13.53a2.293,2.293,0,0,0,3.243,3.242L12.5,14.72a2.293,2.293,0,0,0-.623-3.685.329.329,0,0,1-.179-.337,1.412,1.412,0,0,1,.4-.829l.12-.12a.324.324,0,0,1,.375-.062A3.792,3.792,0,0,1,13.575,10.4ZM17.853,6.12a3.819,3.819,0,0,0-5.4,0C10.207,8.362,10.3,8.257,10.131,8.468a3.82,3.82,0,0,0,1.25,5.818.325.325,0,0,0,.375-.063l.12-.119a1.412,1.412,0,0,0,.4-.83.329.329,0,0,0-.179-.337,2.291,2.291,0,0,1-.623-3.684L13.53,7.2a2.293,2.293,0,1,1,3.242,3.243l-1.251,1.251a.322.322,0,0,0-.087.3,4.726,4.726,0,0,1,.129,1.032.328.328,0,0,0,.559.224c.316-.315.836-.837,1.731-1.732A3.819,3.819,0,0,0,17.853,6.12Z",style:u}))}function d(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Emoji")),r.a.createElement("path",{fill:"currentColor",d:"M12,4a8,8,0,1,0,8,8A8,8,0,0,0,12,4Zm0,14.644A6.644,6.644,0,1,1,18.644,12,6.651,6.651,0,0,1,12,18.644ZM10.706,10.2a1.25,1.25,0,1,0-1.249,1.25A1.249,1.249,0,0,0,10.706,10.2Zm3.837-1.249a1.25,1.25,0,1,0,1.25,1.249A1.249,1.249,0,0,0,14.543,8.953Zm.2,5.237a.357.357,0,0,0-.493.1,2.825,2.825,0,0,1-4.494,0,.355.355,0,1,0-.593.392,3.532,3.532,0,0,0,5.68,0A.354.354,0,0,0,14.74,14.19Z"}))}function m(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Paragraph")),r.a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M15,6 L17,6 L17,18 L15,18 L15,6 Z M11,6 L13.0338983,6 L13.0338983,18 L11,18 L11,6 Z M11,13.8666667 C8.790861,13.8666667 7,12.1056533 7,9.93333333 C7,7.76101332 8.790861,6 11,6 C11,7.68571429 11,11.6190476 11,13.8666667 Z"}))}function g(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Subtitle")),r.a.createElement("path",{d:"M12.3,17H10.658V12.5H6.051V17H4.417V7.006H6.051v4.088h4.607V7.006H12.3Zm8,0H13.526V15.783L16.1,13.192a22.007,22.007,0,0,0,1.514-1.657,3.978,3.978,0,0,0,.543-.92,2.475,2.475,0,0,0,.171-.923,1.4,1.4,0,0,0-.407-1.066,1.557,1.557,0,0,0-1.124-.39,3,3,0,0,0-1.111.212,5.239,5.239,0,0,0-1.241.766l-.868-1.06a5.612,5.612,0,0,1,1.62-1,4.744,4.744,0,0,1,1.675-.294,3.294,3.294,0,0,1,2.235.728,2.46,2.46,0,0,1,.841,1.959,3.453,3.453,0,0,1-.242,1.285,5.212,5.212,0,0,1-.746,1.254,17.041,17.041,0,0,1-1.671,1.747l-1.736,1.682v.068H20.3Z",style:u}))}function v(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Sub Subtitle")),r.a.createElement("path",{d:"M10.658,7.006H12.3V17H10.658V12.5H6.051V17H4.417V7.006H6.051v4.088h4.607Zm8.93,5.533a3.016,3.016,0,0,0-1.806-.748v-.055a2.789,2.789,0,0,0,1.56-.851A2.315,2.315,0,0,0,19.9,9.3a2.131,2.131,0,0,0-.848-1.791,3.8,3.8,0,0,0-2.36-.65,5.251,5.251,0,0,0-3.2,1.012l.786,1.121a5.226,5.226,0,0,1,1.245-.625,3.76,3.76,0,0,1,1.1-.161,1.881,1.881,0,0,1,1.232.349,1.22,1.22,0,0,1,.417.991q0,1.654-2.4,1.654H14.99v1.306h.869a4.066,4.066,0,0,1,2,.376,1.267,1.267,0,0,1,.636,1.176,1.559,1.559,0,0,1-.574,1.333,2.89,2.89,0,0,1-1.738.43,5.794,5.794,0,0,1-1.369-.171,6.372,6.372,0,0,1-1.347-.485V16.6a6.532,6.532,0,0,0,2.8.54,4.676,4.676,0,0,0,2.9-.783,2.637,2.637,0,0,0,1.019-2.225A2.143,2.143,0,0,0,19.588,12.539Z",style:u}))}function j(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Quote")),r.a.createElement("path",{d:"M10.531,17.286V12.755H8.122a9.954,9.954,0,0,1,.1-1.408,4.22,4.22,0,0,1,.388-1.286,2.62,2.62,0,0,1,.735-.918A1.815,1.815,0,0,1,10.49,8.8V6.755a3.955,3.955,0,0,0-2,.49A4.164,4.164,0,0,0,7.082,8.551a5.84,5.84,0,0,0-.817,1.9A9.65,9.65,0,0,0,6,12.755v4.531Zm7.469,0V12.755H15.592a9.954,9.954,0,0,1,.1-1.408,4.166,4.166,0,0,1,.388-1.286,2.606,2.606,0,0,1,.734-.918A1.819,1.819,0,0,1,17.959,8.8V6.755a3.958,3.958,0,0,0-2,.49,4.174,4.174,0,0,0-1.408,1.306,5.86,5.86,0,0,0-.816,1.9,9.649,9.649,0,0,0-.266,2.306v4.531Z",style:u}))}function b(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Paragraph Code Block")),r.a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M9.11588626,16.5074223 L3.14440918,12.7070466 L3.14440918,11.6376386 L9.11588626,7.32465415 L9.11588626,9.04808032 L4.63575044,12.0883808 L9.11588626,14.7663199 L9.11588626,16.5074223 Z M14.48227,5.53936141 L11.1573124,18.4606386 L9.80043634,18.4606386 L13.131506,5.53936141 L14.48227,5.53936141 Z M15.1729321,14.7663199 L19.6530679,12.0883808 L15.1729321,9.04808032 L15.1729321,7.32465415 L21.1444092,11.6376386 L21.1444092,12.7070466 L15.1729321,16.5074223 L15.1729321,14.7663199 Z"}))}function y(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=a()(e);return r.a.createElement("svg",{className:t,viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Spoiler")),r.a.createElement("path",{d:"M8.138,16.569l.606-.606a6.677,6.677,0,0,0,1.108.562,5.952,5.952,0,0,0,2.674.393,7.935,7.935,0,0,0,1.008-.2,11.556,11.556,0,0,0,5.7-4.641.286.286,0,0,0-.02-.345c-.039-.05-.077-.123-.116-.173a14.572,14.572,0,0,0-2.917-3.035l.6-.6a15.062,15.062,0,0,1,2.857,3.028,1.62,1.62,0,0,0,.154.245,1.518,1.518,0,0,1,.02,1.5,12.245,12.245,0,0,1-6.065,4.911,6.307,6.307,0,0,1-1.106.22,4.518,4.518,0,0,1-.581.025,6.655,6.655,0,0,1-2.383-.466A8.023,8.023,0,0,1,8.138,16.569Zm-.824-.59a14.661,14.661,0,0,1-2.965-3.112,1.424,1.424,0,0,1,0-1.867A13.69,13.69,0,0,1,8.863,6.851a6.31,6.31,0,0,1,6.532.123c.191.112.381.231.568.356l-.621.621c-.092-.058-.184-.114-.277-.168a5.945,5.945,0,0,0-3.081-.909,6.007,6.007,0,0,0-2.868.786,13.127,13.127,0,0,0-4.263,3.929c-.214.271-.214.343,0,.639a13.845,13.845,0,0,0,3.059,3.153ZM13.9,9.4l-.618.618a2.542,2.542,0,0,0-3.475,3.475l-.61.61A3.381,3.381,0,0,1,12,8.822,3.4,3.4,0,0,1,13.9,9.4Zm.74.674a3.3,3.3,0,0,1,.748,2.138,3.382,3.382,0,0,1-5.515,2.629l.6-.6a2.542,2.542,0,0,0,3.559-3.559Zm-3.146,3.146L13.008,11.7a1.129,1.129,0,0,1-1.516,1.516Zm-.6-.811a1.061,1.061,0,0,1-.018-.2A1.129,1.129,0,0,1,12,11.079a1.164,1.164,0,0,1,.2.017Z",style:u}),r.a.createElement("polygon",{points:"19.146 4.146 19.854 4.854 4.854 19.854 4.146 19.146 19.146 4.146",style:u}))}function _(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Embed")),r.a.createElement("path",{fill:"currentColor",d:"M4.5,5.5a1,1,0,0,0-1,1v11a1,1,0,0,0,1,1h15a1,1,0,0,0,1-1V6.5a1,1,0,0,0-1-1ZM4.5,4h15A2.5,2.5,0,0,1,22,6.5v11A2.5,2.5,0,0,1,19.5,20H4.5A2.5,2.5,0,0,1,2,17.5V6.5A2.5,2.5,0,0,1,4.5,4Zm5.592,12.04-1.184.92-3.5-4.5v-.92l3.5-4.5,1.184.92L6.95,12Zm3.816,0L17.05,12,13.908,7.96l1.184-.92,3.5,4.5v.92l-3.5,4.5Z",style:u}))}function O(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Image")),r.a.createElement("path",{fill:"currentColor",fillRule:"nonzero",d:"M5,17V15l2.294-3.212a.3.3,0,0,1,.418-.07h0c.013.01.025.021.037.032L10,14l4.763-5.747a.3.3,0,0,1,.422-.041h0l.02.018L19,12v5ZM4.5,5.5a1,1,0,0,0-1,1v11a1,1,0,0,0,1,1h15a1,1,0,0,0,1-1V6.5a1,1,0,0,0-1-1ZM4.5,4h15A2.5,2.5,0,0,1,22,6.5v11A2.5,2.5,0,0,1,19.5,20H4.5A2.5,2.5,0,0,1,2,17.5V6.5A2.5,2.5,0,0,1,4.5,4Zm3,6.2A1.7,1.7,0,1,1,9.2,8.5h0A1.7,1.7,0,0,1,7.5,10.2Z"}))}function C(){return r.a.createElement("svg",{className:"richEditorButton-icon",viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(l.m)("Attachment")),r.a.createElement("path",{fill:"currentColor",d:"M17.25,9.045a.75.75,0,0,1,1.5,0v6.91A6.63,6.63,0,0,1,12,22.75a6.63,6.63,0,0,1-6.75-6.795V7.318A4.811,4.811,0,0,1,10.286,2.25a4.81,4.81,0,0,1,5.035,5.068v7.773c0,2.308-1.254,4.2-3.321,4.2s-3.321-1.9-3.321-4.2V9.045a.75.75,0,0,1,1.5,0v6.046c0,1.578.745,2.7,1.821,2.7s1.821-1.126,1.821-2.7V7.318A3.319,3.319,0,0,0,10.286,3.75,3.319,3.319,0,0,0,6.75,7.318v8.637A5.132,5.132,0,0,0,12,21.25a5.132,5.132,0,0,0,5.25-5.295Z",style:u}))}},function(e,t,o){var n=o(514),r=o(595),i=o(563),a=Object.defineProperty;t.f=o(519)?Object.defineProperty:function(e,t,o){if(n(e),t=i(t,!0),n(o),r)try{return a(e,t,o)}catch(e){}if("get"in o||"set"in o)throw TypeError("Accessors not supported!");return"value"in o&&(e[t]=o.value),e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,o){e.exports=!o(525)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t){var o={}.hasOwnProperty;e.exports=function(e,t){return o.call(e,t)}},function(e,t,o){var n=o(517),r=o(545);e.exports=o(519)?function(e,t,o){return n.f(e,t,r(1,o))}:function(e,t,o){return e[t]=o,e}},function(e,t,o){var n=o(601),r=o(558);e.exports=function(e){return n(r(e))}},function(e,t,o){var n=o(632),r=o(635);e.exports=function(e,t){var o=r(e,t);return n(o)?o:void 0}},function(e,t,o){"use strict";o.d(t,"a",function(){return g});var n=o(3),r=o(6),i=o(8),a=o(1),l=o(9),u=o(2),s=o(0),c=o.n(s),p=o(22),f=o.n(p),h=o(583),d=o(5),m=o(88),g=function(e){function t(){return Object(n.a)(this,t),Object(i.a)(this,Object(a.a)(t).apply(this,arguments))}return Object(l.a)(t,e),Object(r.a)(t,[{key:"render",value:function(){var e=this.props.title?this.props.title:Object(d.m)("Close"),t=f()("buttonClose",this.props.className);return c.a.createElement(m.b,{disabled:this.props.disabled,type:"button",className:t,title:e,onClick:this.props.onClick,baseClass:this.props.baseClass},Object(h.a)())}}]),t}(c.a.PureComponent);Object(u.a)(g,"defaultProps",{legacyMode:!1,baseClass:m.a.ICON})},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t){e.exports={}},function(e,t,o){var n;
/*!
  Copyright (c) 2016 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
/*!
  Copyright (c) 2016 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/
!function(){"use strict";var o={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n))e.push(r.apply(null,n));else if("object"===i)for(var a in n)o.call(n,a)&&n[a]&&e.push(a)}}return e.join(" ")}e.exports?e.exports=r:void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()},function(e,t,o){"use strict";o.d(t,"c",function(){return c}),o.d(t,"a",function(){return p}),o.d(t,"b",function(){return f});var n=o(3),r=o(8),i=o(1),a=o(9),l=o(2),u=o(541),s=o(586),c=function(e){function t(){return Object(n.a)(this,t),Object(r.a)(this,Object(i.a)(t).apply(this,arguments))}return Object(a.a)(t,e),t}(o(550).a);Object(l.a)(c,"blotName","blockquote-line"),Object(l.a)(c,"className","blockquote-line"),Object(l.a)(c,"tagName","p"),Object(l.a)(c,"parentName","blockquote-content");var p=function(e){function t(){return Object(n.a)(this,t),Object(r.a)(this,Object(i.a)(t).apply(this,arguments))}return Object(a.a)(t,e),t}(s.a);Object(l.a)(p,"className","blockquote-content"),Object(l.a)(p,"blotName","blockquote-content"),Object(l.a)(p,"parentName","blockquote");var f=function(e){function t(){return Object(n.a)(this,t),Object(r.a)(this,Object(i.a)(t).apply(this,arguments))}return Object(a.a)(t,e),t}(u.a);Object(l.a)(f,"className","blockquote"),Object(l.a)(f,"blotName","blockquote")},function(e,t){e.exports=!0},function(e,t,o){var n=o(544);e.exports=function(e,t,o){if(n(e),void 0===t)return e;switch(o){case 1:return function(o){return e.call(t,o)};case 2:return function(o,n){return e.call(t,o,n)};case 3:return function(o,n,r){return e.call(t,o,n,r)}}return function(){return e.apply(t,arguments)}}},function(e,t,o){var n=o(600),r=o(566);e.exports=Object.keys||function(e){return n(e,r)}},function(e,t){var o={}.toString;e.exports=function(e){return o.call(e).slice(8,-1)}},function(e,t,o){"use strict";var n=o(52),r=o(6),i=o(13),a=o(3),l=o(8),u=o(1),s=o(9),c=o(2),p=o(44),f=o.n(p),h=o(0),d=o.n(h),m=o(541),g=o(586),v=o(550),j=o(5),b=o(516),y=function(e){function t(){return Object(a.a)(this,t),Object(l.a)(this,Object(u.a)(t).apply(this,arguments))}return Object(s.a)(t,e),Object(r.a)(t,[{key:"render",value:function(){return d.a.createElement("div",{contentEditable:!1,className:"spoiler-buttonContainer"},d.a.createElement("button",{disabled:!0,className:"iconButton button-spoiler",type:"button"},d.a.createElement("span",{className:"spoiler-warning"},d.a.createElement("span",{className:"spoiler-warningMain"},Object(b.n)("spoiler-icon"),d.a.createElement("strong",{className:"spoiler-warningBefore"},Object(j.m)("Warning")),d.a.createElement("span",{className:"spoiler-warningAfter"},Object(j.m)("This is a spoiler"))))))}}]),t}(d.a.Component);o.d(t,"c",function(){return _}),o.d(t,"a",function(){return O}),o.d(t,"b",function(){return C});
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
var _=function(e){function t(){return Object(a.a)(this,t),Object(l.a)(this,Object(u.a)(t).apply(this,arguments))}return Object(s.a)(t,e),t}(v.a);Object(c.a)(_,"blotName","spoiler-line"),Object(c.a)(_,"className","spoiler-line"),Object(c.a)(_,"tagName","p"),Object(c.a)(_,"parentName","spoiler-content");var O=function(e){function t(){return Object(a.a)(this,t),Object(l.a)(this,Object(u.a)(t).apply(this,arguments))}return Object(s.a)(t,e),t}(g.a);Object(c.a)(O,"className","spoiler-content"),Object(c.a)(O,"blotName","spoiler-content"),Object(c.a)(O,"parentName","spoiler");var C=function(e){function t(e){var o;Object(a.a)(this,t),o=Object(l.a)(this,Object(u.a)(t).call(this,e));var n=document.createElement("div");return o.domNode.appendChild(n),f.a.render(d.a.createElement(y,null),n),o}return Object(s.a)(t,e),Object(r.a)(t,null,[{key:"create",value:function(e){var o=Object(i.a)(Object(u.a)(t),"create",this).call(this,e);return o.classList.add("isShowingSpoiler"),o}}]),Object(r.a)(t,[{key:"attach",value:function(){Object(i.a)(Object(u.a)(t.prototype),"attach",this).call(this)}},{key:"optimize",value:function(e){Object(i.a)(Object(u.a)(t.prototype),"optimize",this).call(this,e)}}]),t}(m.a);Object(c.a)(C,"className","spoiler"),Object(c.a)(C,"blotName","spoiler"),Object(c.a)(C,"allowedChildren",Object(n.a)(m.a.allowedChildren))},function(e,t,o){var n=o(622),r=o(623),i=o(624),a=o(625),l=o(626);function u(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=r,u.prototype.get=i,u.prototype.has=a,u.prototype.set=l,e.exports=u},function(e,t,o){var n=o(575);e.exports=function(e,t){for(var o=e.length;o--;)if(n(e[o][0],t))return o;return-1}},function(e,t,o){var n=o(523)(Object,"create");e.exports=n},function(e,t,o){var n=o(644);e.exports=function(e,t){var o=e.__data__;return n(t)?o["string"==typeof t?"string":"hash"]:o.map}},function(e,t,o){"use strict";o.d(t,"a",function(){return h});var n=o(3),r=o(8),i=o(1),a=o(13),l=o(6),u=o(9),s=o(2),c=o(45),p=o(57),f=o(5),h=function(e){function t(e){var o;return Object(n.a)(this,t),o=Object(r.a)(this,Object(i.a)(t).call(this,e)),e.setAttribute("aria-label",Object(f.m)("@mention a user")),e.setAttribute("aria-autocomplete","list"),o}return Object(u.a)(t,e),Object(l.a)(t,null,[{key:"formats",value:function(){return!0}}]),Object(l.a)(t,[{key:"attach",value:function(){Object(a.a)(Object(i.a)(t.prototype),"attach",this).call(this),this.statics.requiredContainer&&this.parent.statics.blotName!==this.statics.requiredContainer&&this.wrap(this.statics.requiredContainer)}},{key:"replaceWith",value:function(e,t){return this.moveChildren(this.parent,this.next),this.remove(),this.parent.replaceWith(e,t)}},{key:"finalize",value:function(e){this.replaceWith("mention",e),this.quill&&this.quill.update(c.a.sources.API)}},{key:"cancel",value:function(){this.replaceWith("inline",this.domNode.innerHTML),this.quill&&this.quill.update(c.a.sources.SILENT)}},{key:"injectAccessibilityAttributes",value:function(e){var t=this.domNode,o=this.parent.domNode;o.setAttribute("id",e.ID),o.setAttribute("aria-expanded",!!e.activeItemID),o.setAttribute("aria-owns",e.suggestionListID),t.setAttribute("aria-controls",e.suggestionListID),e.activeItemIsLoader?(t.setAttribute("aria-label",Object(f.m)("Loading new @mention suggestions")),t.removeAttribute("aria-activedescendant")):(t.setAttribute("aria-activedescendant",e.activeItemID),t.removeAttribute("aria-describeby"))}},{key:"username",get:function(){return(this.domNode.textContent||"").replace("@","").replace('"',"")}},{key:"quill",get:function(){return this.scroll&&this.scroll.domNode.parentNode?c.a.find(this.scroll.domNode.parentNode):null}}]),t}(p.a);Object(s.a)(h,"blotName","mention-autocomplete"),Object(s.a)(h,"className","atMentionAutoComplete"),Object(s.a)(h,"tagName","span"),Object(s.a)(h,"requiredContainer","mention-combobox")},function(e,t,o){"use strict";o.d(t,"a",function(){return s});var n=o(3),r=o(6),i=o(8),a=o(1),l=o(13),u=o(9),s=function(e){function t(){return Object(n.a)(this,t),Object(i.a)(this,Object(a.a)(t).apply(this,arguments))}return Object(u.a)(t,e),Object(r.a)(t,[{key:"format",value:function(e,o){if(e!==this.statics.blotName||!o)return Object(l.a)(Object(a.a)(t.prototype),"format",this).call(this,e,o);o=this.constructor.sanitize(o),this.domNode.setAttribute("href",o)}}],[{key:"create",value:function(e){var o=Object(l.a)(Object(a.a)(t),"create",this).call(this,e);return e=this.sanitize(e),o.setAttribute("href",e),o.setAttribute("target","_blank"),o}},{key:"formats",value:function(e){return e.getAttribute("href")}},{key:"sanitize",value:function(e){return function(e,t){var o=document.createElement("a");o.href=e;var n=o.href.slice(0,o.href.indexOf(":"));return t.indexOf(n)>-1}(e,this.PROTOCOL_WHITELIST)?e:this.SANITIZED_URL}}]),t}(o(57).a);s.blotName="link",s.tagName="A",s.SANITIZED_URL="about:blank",s.PROTOCOL_WHITELIST=["http","https","mailto","tel"]},function(e,t,o){"use strict";o.d(t,"a",function(){return l});var n=o(3),r=o(8),i=o(1),a=o(9),l=function(e){function t(e){var o;return Object(n.a)(this,t),o=Object(r.a)(this,Object(i.a)(t).call(this,e)),e.classList.add("code"),e.classList.add("isInline"),e.classList.add("codeInline"),e.setAttribute("spellcheck",!1),o}return Object(a.a)(t,e),t}(o(35).a)},function(e,t,o){"use strict";o.d(t,"a",function(){return h});var n=o(3),r=o(6),i=o(8),a=o(1),l=o(13),u=o(9),s=o(2),c=o(67),p=o(7),f=o.n(p),h=function(e){function t(){return Object(n.a)(this,t),Object(i.a)(this,Object(a.a)(t).apply(this,arguments))}return Object(u.a)(t,e),Object(r.a)(t,[{key:"optimize",value:function(e){Object(l.a)(Object(a.a)(t.prototype),"optimize",this).call(this,e);var o=this.next;o instanceof t&&o.prev===this&&o.statics.blotName===this.constructor.blotName&&o.domNode.tagName===this.domNode.tagName&&(o.moveChildren(this),o.remove())}}],[{key:"formats",value:function(){}},{key:"create",value:function(e){var o=Object(l.a)(Object(a.a)(t),"create",this).call(this,e);return this.className&&o.classList.add(this.className),o}}]),t}(c.a);Object(s.a)(h,"scope",f.a.Scope.BLOCK_BLOT),Object(s.a)(h,"tagName","div"),Object(s.a)(h,"allowedChildren",[h]),Object(s.a)(h,"className",void 0)},function(e,t,o){var n=o(558);e.exports=function(e){return Object(n(e))}},function(e,t){var o=0,n=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++o+n).toString(36))}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,o){"use strict";var n=o(708)(!0);o(598)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,o=this._i;return o>=t.length?{value:void 0,done:!0}:(e=n(t,o),this._i+=e.length,{value:e,done:!1})})},function(e,t,o){var n=o(517).f,r=o(520),i=o(512)("toStringTag");e.exports=function(e,t,o){e&&!r(e=o?e:e.prototype,i)&&n(e,i,{configurable:!0,value:t})}},function(e,t,o){o(713);for(var n=o(511),r=o(521),i=o(526),a=o(512)("toStringTag"),l="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),u=0;u<l.length;u++){var s=l[u],c=n[s],p=c&&c.prototype;p&&!p[a]&&r(p,a,s),i[s]=i.Array}},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,o){"use strict";var n=o(3),r=o(8),i=o(1),a=o(9),l=o(2),u=o(6),s=o(13),c=function(e){function t(e){var o;if(Object(n.a)(this,t),!(o=Object(r.a)(this,Object(i.a)(t).call(this,e))).statics.className)throw new Error("Attempted to initialize a ClassFormatBlot without setting the static className");return o}return Object(a.a)(t,e),Object(u.a)(t,null,[{key:"create",value:function(e){var o=Object(s.a)(Object(i.a)(t),"create",this).call(this,e);return this.className&&o.classList.add(this.className),o}},{key:"formats",value:function(e){var t=this.className&&e.classList.contains(this.className),o=e.tagName.toLowerCase()===this.tagName.toLowerCase();return this.className?t&&o:o}}]),Object(u.a)(t,[{key:"formats",value:function(){return Object(l.a)({},this.statics.blotName,this.statics.formats(this.domNode))}}]),t}(o(18).c),p=o(587),f=function(e){function t(){return Object(n.a)(this,t),Object(r.a)(this,Object(i.a)(t).apply(this,arguments))}return Object(a.a)(t,e),t}(c);t.a=Object(p.a)(f)},function(e,t,o){var n=o(523)(o(86),"Map");e.exports=n},function(e,t,o){var n=o(636),r=o(643),i=o(645),a=o(646),l=o(647);function u(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=r,u.prototype.get=i,u.prototype.has=a,u.prototype.set=l,e.exports=u},function(e,t,o){"use strict";o.d(t,"a",function(){return j});var n=o(3),r=o(6),i=o(2),a=o(45),l=o(539),u=o(554),s=o(584),c=o(585),p=o(65),f=o(540),h=o(87),d=o(528),m=o(533),g=o(555),v=o(92),j=function(){function e(t){var o=this;Object(n.a)(this,e),this.quill=t,Object(i.a)(this,"bold",function(e){o.handleInlineFormat(e,u.a.blotName)}),Object(i.a)(this,"italic",function(e){o.handleInlineFormat(e,s.a.blotName)}),Object(i.a)(this,"strike",function(e){o.handleInlineFormat(e,c.a.blotName)}),Object(i.a)(this,"codeInline",function(e){o.handleInlineFormat(e,f.a.blotName)}),Object(i.a)(this,"link",function(e,t){Object(p.m)(o.quill,l.a,e)?Object(p.b)(o.quill,l.a,e):o.quill.formatText(e.index,e.length,l.a.blotName,t,a.a.sources.USER)}),Object(i.a)(this,"paragraph",function(t){e.BLOCK_FORMAT_NAMES.forEach(function(e){o.quill.formatLine(t.index,t.length,e,!1,a.a.sources.API)}),o.quill.update(a.a.sources.USER)}),Object(i.a)(this,"h2",function(e){o.quill.formatLine(e.index,e.length,g.a.blotName,2,a.a.sources.USER)}),Object(i.a)(this,"h3",function(e){o.quill.formatLine(e.index,e.length,g.a.blotName,3,a.a.sources.USER)}),Object(i.a)(this,"codeBlock",function(t){var n=o.quill.getLines(t.index,t.length);0===n.length&&(n=[o.quill.getLine(t.index)[0]]);var r=n[0],i=n[n.length-1],l=r.offset(o.quill.scroll),u=i.offset(o.quill.scroll)+i.length()-l,s={index:l,length:u};e.INLINE_FORMAT_NAMES.forEach(function(e){o.quill.formatText(l,u,e,!1,a.a.sources.SILENT)});var c=o.replaceInlineEmbeds(s);o.quill.formatLine(l,u+c,h.a.blotName,!0,a.a.sources.USER)}),Object(i.a)(this,"blockquote",function(e){o.quill.formatLine(e.index,e.length,d.c.blotName,!0,a.a.sources.USER)}),Object(i.a)(this,"spoiler",function(e){o.quill.formatLine(e.index,e.length,m.c.blotName,!0,a.a.sources.USER)})}return Object(r.a)(e,[{key:"handleInlineFormat",value:function(e,t){var o=!0===this.quill.getFormat(e)[t];this.quill.formatText(e.index,e.length,t,!o,a.a.sources.USER),this.quill.update(a.a.sources.USER)}},{key:"replaceInlineEmbeds",value:function(e){var t=this,o=this.quill.scroll.descendants(v.a,e.index,e.length),n=0;return o.forEach(function(e){var o=e.domNode.innerText||"";o=o.replace(/[\u200B-\u200D\uFEFF]/g,""),n+=o.length-1,e.replaceWith("text",o),t.quill.update(a.a.sources.USER)}),n}}]),e}();Object(i.a)(j,"INLINE_FORMAT_NAMES",[u.a.blotName,s.a.blotName,c.a.blotName,f.a.blotName,l.a.blotName]),Object(i.a)(j,"BLOCK_FORMAT_NAMES",[h.a.blotName,d.c.blotName,m.c.blotName,g.a.blotName])},function(e,t,o){"use strict";var n=o(3),r=o(6),i=o(8),a=o(1),l=o(13),u=o(9),s=function(e){function t(){return Object(n.a)(this,t),Object(i.a)(this,Object(a.a)(t).apply(this,arguments))}return Object(u.a)(t,e),Object(r.a)(t,[{key:"optimize",value:function(e){Object(l.a)(Object(a.a)(t.prototype),"optimize",this).call(this,e),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return Object(l.a)(Object(a.a)(t),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),t}(o(57).a);s.blotName="bold",s.tagName=["STRONG","B"],t.a=s},function(e,t,o){"use strict";var n=o(3),r=o(6),i=o(8),a=o(1),l=o(9),u=function(e){function t(){return Object(n.a)(this,t),Object(i.a)(this,Object(a.a)(t).apply(this,arguments))}return Object(l.a)(t,e),Object(r.a)(t,null,[{key:"formats",value:function(e){return this.tagName.indexOf(e.tagName)+1}}]),t}(o(18).c);u.blotName="header",u.tagName=["H1","H2","H3","H4","H5","H6"],t.a=u},function(e,t,o){"use strict";
/*
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function n(e,t){if(!(arguments.length>2&&void 0!==arguments[2]&&arguments[2]))return{top:e?void 0:"100%",right:t?"0":void 0,bottom:e?"100%":void 0,left:t?void 0:"0",transform:e?"translateY(-100%)":void 0}}o.d(t,"a",function(){return n})},function(e,t,o){"use strict";o.d(t,"a",function(){return a});var n=o(3),r=o(6),i=o(2),a=function(){function e(t,o){var r=this;Object(n.a)(this,e),this.rootNode=t,this.changeHandler=o,Object(i.a)(this,"handleFocusOut",function(e){r.checkDomTreeHasFocus(e,r.changeHandler)}),Object(i.a)(this,"handleFocusIn",function(e){r.checkDomTreeHasFocus(e,r.changeHandler)}),Object(i.a)(this,"handleClick",function(e){var t=e.target;r.checkDomTreeWasClicked(t)||r.changeHandler(!1)})}return Object(r.a)(e,[{key:"start",value:function(){this.rootNode.addEventListener("focusout",this.handleFocusOut,!0),this.rootNode.addEventListener("focusin",this.handleFocusIn,!0),document.addEventListener("click",this.handleClick)}},{key:"stop",value:function(){this.rootNode.removeEventListener("focusout",this.handleFocusOut,!0),this.rootNode.removeEventListener("focusin",this.handleFocusIn,!0),document.removeEventListener("click",this.handleClick)}},{key:"checkDomTreeWasClicked",value:function(e){return this.rootNode&&e&&(this.rootNode.contains(e)||this.rootNode===e)}},{key:"checkDomTreeHasFocus",value:function(e,t){var o=this;setTimeout(function(){for(var n=[document.activeElement,e.relatedTarget,e.explicitOriginalTarget],r=null,i=0;i<n.length;i++){var a=n[i];if(a&&a!==document.body){r=a;break}}if(null!==r){var l=o.rootNode&&r&&(r===o.rootNode||o.rootNode.contains(r));t(!!l)}},0)}}]),e}()},function(e,t){e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,o){var n=o(560)("keys"),r=o(543);e.exports=function(e){return n[e]||(n[e]=r(e))}},function(e,t,o){var n=o(506),r=o(511),i=r["__core-js_shared__"]||(r["__core-js_shared__"]={});(e.exports=function(e,t){return i[e]||(i[e]=void 0!==t?t:{})})("versions",[]).push({version:n.version,mode:o(529)?"pure":"global",copyright:"© 2018 Denis Pushkarev (zloirock.ru)"})},function(e,t,o){var n=o(513),r=o(506),i=o(525);e.exports=function(e,t){var o=(r.Object||{})[e]||Object[e],a={};a[e]=t(o),n(n.S+n.F*i(function(){o(1)}),"Object",a)}},function(e,t,o){var n=o(518),r=o(511).document,i=n(r)&&n(r.createElement);e.exports=function(e){return i?r.createElement(e):{}}},function(e,t,o){var n=o(518);e.exports=function(e,t){if(!n(e))return e;var o,r;if(t&&"function"==typeof(o=e.toString)&&!n(r=o.call(e)))return r;if("function"==typeof(o=e.valueOf)&&!n(r=o.call(e)))return r;if(!t&&"function"==typeof(o=e.toString)&&!n(r=o.call(e)))return r;throw TypeError("Can't convert object to primitive value")}},function(e,t){var o=Math.ceil,n=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?n:o)(e)}},function(e,t,o){var n=o(514),r=o(710),i=o(566),a=o(559)("IE_PROTO"),l=function(){},u=function(){var e,t=o(562)("iframe"),n=i.length;for(t.style.display="none",o(603).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;n--;)delete u.prototype[i[n]];return u()};e.exports=Object.create||function(e,t){var o;return null!==e?(l.prototype=n(e),o=new l,l.prototype=null,o[a]=e):o=u(),void 0===t?o:r(o,t)}},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,o){t.f=o(512)},function(e,t,o){var n=o(511),r=o(506),i=o(529),a=o(567),l=o(517).f;e.exports=function(e){var t=r.Symbol||(r.Symbol=i?{}:n.Symbol||{});"_"==e.charAt(0)||e in t||l(t,e,{value:a.f(e)})}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,o){var n=o(549),r=o(545),i=o(522),a=o(563),l=o(520),u=o(595),s=Object.getOwnPropertyDescriptor;t.f=o(519)?s:function(e,t){if(e=i(e),t=a(t,!0),u)try{return s(e,t)}catch(e){}if(l(e,t))return r(!n.f.call(e,t),e[t])}},function(e,t,o){"use strict";t.__esModule=!0,t.default=function(e,t){var o={};for(var n in e)t.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(e,n)&&(o[n]=e[n]);return o}},function(e,t,o){e.exports={default:o(737),__esModule:!0}},function(e,t,o){var n=o(532),r=o(512)("toStringTag"),i="Arguments"==n(function(){return arguments}());e.exports=function(e){var t,o,a;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(o=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),r))?o:i?n(t):"Object"==(a=n(t))&&"function"==typeof t.callee?"Arguments":a}},function(e,t,o){"use strict";var n=o(544);function r(e){var t,o;this.promise=new e(function(e,n){if(void 0!==t||void 0!==o)throw TypeError("Bad Promise constructor");t=e,o=n}),this.resolve=n(t),this.reject=n(o)}e.exports.f=function(e){return new r(e)}},function(e,t){e.exports=function(e,t){return e===t||e!=e&&t!=t}},function(e,t,o){var n=o(249),r=o(91),i="[object AsyncFunction]",a="[object Function]",l="[object GeneratorFunction]",u="[object Proxy]";e.exports=function(e){if(!r(e))return!1;var t=n(e);return t==a||t==l||t==i||t==u}},function(e,t){var o=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return o.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},function(e,t,o){var n=o(648),r=o(651),i=o(652),a=1,l=2;e.exports=function(e,t,o,u,s,c){var p=o&a,f=e.length,h=t.length;if(f!=h&&!(p&&h>f))return!1;var d=c.get(e);if(d&&c.get(t))return d==t;var m=-1,g=!0,v=o&l?new n:void 0;for(c.set(e,t),c.set(t,e);++m<f;){var j=e[m],b=t[m];if(u)var y=p?u(b,j,m,t,e,c):u(j,b,m,e,t,c);if(void 0!==y){if(y)continue;g=!1;break}if(v){if(!r(t,function(e,t){if(!i(v,t)&&(j===e||s(j,e,o,u,c)))return v.push(t)})){g=!1;break}}else if(j!==b&&!s(j,b,o,u,c)){g=!1;break}}return c.delete(e),c.delete(t),g}},function(e,t,o){(function(e){var n=o(86),r=o(669),i=t&&!t.nodeType&&t,a=i&&"object"==typeof e&&e&&!e.nodeType&&e,l=a&&a.exports===i?n.Buffer:void 0,u=(l?l.isBuffer:void 0)||r;e.exports=u}).call(this,o(252)(e))},function(e,t,o){var n=o(671),r=o(672),i=o(673),a=i&&i.isTypedArray,l=a?r(a):n;e.exports=l},function(e,t){var o=9007199254740991;e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=o}},function(e,t,o){"use strict";var n=o(70),r=o(3),i=o(6),a=o(8),l=o(1),u=o(9),s=o(10),c=o(2),p=o(0),f=o.n(p),h=o(51),d=o.n(h),m=o(510),g=function(e){function t(e){var o;return Object(r.a)(this,t),o=Object(a.a)(this,Object(l.a)(t).call(this,e)),Object(c.a)(Object(s.a)(Object(s.a)(o)),"quill",void 0),Object(c.a)(Object(s.a)(Object(s.a)(o)),"windowResizeListener",function(){d()(function(){o.setState({quillWidth:o.quill.root.offsetWidth})},200)()}),o.quill=e.quill,o.state={quillWidth:o.quill.root.offsetWidth},o}return Object(u.a)(t,e),Object(i.a)(t,[{key:"shouldComponentUpdate",value:function(e,t){var o=this.extractValuesFromProps(this.props),n=this.extractValuesFromProps(e),r=!1;e.isActive&&o.selectionIndex!==n.selectionIndex&&(r=!0),e.isActive&&o.selectionLength!==n.selectionLength&&(r=!0);for(var i=Object.keys(o.otherProps),a=0;a<i.length;a++){var l=i[a];o.otherProps[l]!==n.otherProps[l]&&(r=!0)}return this.state.quillWidth!==t.quillWidth&&(r=!0),r}},{key:"render",value:function(){var e=this.props.isActive?{x:this.getXCoordinates(),y:this.getYCoordinates()}:{x:null,y:null};return this.props.children(e)}},{key:"componentDidMount",value:function(){window.addEventListener("resize",this.windowResizeListener)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.windowResizeListener)}},{key:"extractValuesFromProps",value:function(e){return{selectionIndex:e.selectionIndex,selectionLength:e.selectionLength,otherProps:Object(n.a)(e,["selectionIndex","selectionLength"])}}},{key:"getBounds",value:function(){var e=this.props,t=e.selectionIndex,o=e.selectionLength;if(null===t||null===o)return null;var n,r=this.quill.getLines(t,o);if(r.length<=1)n=this.quill.getBounds(t,o);else{var i=r[r.length-1],a=this.quill.getIndex(i),l=Math.min(i.length()-1,t+o-a);n=this.quill.getBounds(a,l)}return n}},{key:"getXCoordinates",value:function(){var e=this.getBounds();if(!e||!this.props.flyoutWidth)return null;var t=this.state.quillWidth,o=this.props.flyoutWidth,n=e.left,r=e.right;if("center"===(this.props.horizontalAlignment||"center")){var i=o/2,a=i+-6,l=t-i- -6,u=Math.round((n+r)/2),s=Math.max(a,Math.min(l,u))-i;return{position:s,nubPosition:u-s}}var c=n,p=t-o-6,f=Math.min(p,Math.max(c,n));return{position:f,nubPosition:f}}},{key:"getYCoordinates",value:function(){var e=this.getBounds();if(null==e||null==this.props.flyoutHeight)return null;var t=this.props,o=t.flyoutHeight,n=t.nubHeight,r=t.verticalAlignment,i=e.top-o-0,a=o,l=!0;return(e.top<30||"below"===r)&&(i=e.bottom+0,n&&(a=0-n/2,l=!1)),{position:i,nubPosition:a,nubPointsDown:l}}}]),t}(f.a.Component);t.a=Object(m.b)(g)},function(e,t,o){"use strict";o.d(t,"a",function(){return u}),o.d(t,"b",function(){return s});var n=o(0),r=o.n(n),i=o(5),a=o(22),l=o.n(a);function u(e){var t=Object(i.m)("Close");return r.a.createElement("svg",{className:l()("icon","icon-close",e),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 9.5 9.5","aria-hidden":"true"},r.a.createElement("title",null,t),r.a.createElement("path",{fill:"currentColor",d:"M3.38,4.75l-3-3A1,1,0,0,1,.26.33s0,0,0,0a1.05,1.05,0,0,1,1.45,0h0l3,3,3-3A1,1,0,0,1,9.18.3h0a1,1,0,0,1,0,1.4h0l-3,3,3,3a1,1,0,0,1,.06,1.41,1,1,0,0,1-1.38.09l-3-3-3,3a1.05,1.05,0,0,1-1.47.07A1,1,0,0,1,.29,7.8h0Z",transform:"translate(-0.01 -0.01)"}))}function s(e){return r.a.createElement("svg",{className:l()("icon","icon-dropDownMenu",e),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24","aria-hidden":"true"},r.a.createElement("title",null,"…"),r.a.createElement("circle",{cx:"5.7",cy:"12",r:"2",fill:"currentColor"}),r.a.createElement("circle",{cx:"18.3",cy:"12",r:"2",fill:"currentColor"}),r.a.createElement("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor"}))}},function(e,t,o){"use strict";var n=o(3),r=o(8),i=o(1),a=o(9),l=function(e){function t(){return Object(n.a)(this,t),Object(r.a)(this,Object(i.a)(t).apply(this,arguments))}return Object(a.a)(t,e),t}(o(554).a);l.blotName="italic",l.tagName=["EM","I"],t.a=l},function(e,t,o){"use strict";var n=o(3),r=o(8),i=o(1),a=o(9),l=function(e){function t(){return Object(n.a)(this,t),Object(r.a)(this,Object(i.a)(t).apply(this,arguments))}return Object(a.a)(t,e),t}(o(57).a);l.blotName="strike",l.tagName="S",t.a=l},function(e,t,o){"use strict";var n=o(541),r=o(587),i=o(550),a=Object(r.a)(n.a);a.allowedChildren=[i.a],t.a=a},function(e,t,o){"use strict";o.d(t,"a",function(){return d});var n=o(3),r=o(6),i=o(8),a=o(1),l=o(13),u=o(9),s=o(10),c=o(2),p=o(541),f=o(7),h=o.n(f);
/**
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function d(e){return function(e){function t(e){var o;if(Object(n.a)(this,t),o=Object(i.a)(this,Object(a.a)(t).call(this,e)),Object(c.a)(Object(s.a)(Object(s.a)(o)),"parent",void 0),!o.statics.parentName)throw new Error("Attempted to instantiate wrapped Blot without setting static value parentName");return o}return Object(u.a)(t,e),Object(r.a)(t,[{key:"attach",value:function(){if(Object(l.a)(Object(a.a)(t.prototype),"attach",this).call(this),this.parent.statics.blotName!==this.statics.parentName){var e=h.a.create(this.statics.parentName);if(!(e instanceof p.a))throw new Error("The provided static parentName did not instantiate an instance of a WrapperBlot.");this.wrap(e)}}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():Object(l.a)(Object(a.a)(t.prototype),"remove",this).call(this)}},{key:"optimize",value:function(e){Object(l.a)(Object(a.a)(t.prototype),"optimize",this).call(this,e),0===this.children.length&&this.remove()}},{key:"getWrapper",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.parent;return e&&t.getWrapper?t.getWrapper(!0):t}},{key:"replaceWith",value:function(e,t){var o=this,n=this.getWrapper(!0);this.parent.children.forEach(function(r){r===o?r.replaceWithIntoScroll(e,t,n):r.insertInto(o.scroll,n)}),n.remove()}},{key:"replaceWithIntoScroll",value:function(e,t,o){var n=h.a.create(e,t);this.moveChildren(n),n.insertInto(this.scroll,o)}}]),t}(e)}},function(e,t,o){"use strict";o.d(t,"a",function(){return i});var n=o(3),r=o(2),i=function e(){var t=this;Object(n.a)(this,e),Object(r.a)(this,"listeners",[]),Object(r.a)(this,"emit",function(e){t.listeners.forEach(function(t){t(e)})}),Object(r.a)(this,"addEventListener",function(e){t.listeners.push(e)}),Object(r.a)(this,"removeEventListener",function(e){t.listeners=t.listeners.filter(function(t){return e!==t})})}},function(e,t,o){"use strict";o.d(t,"a",function(){return s});var n=o(3),r=o(6),i=o(2),a=o(686),l=o.n(a),u=o(17),s=function(){function e(){var t=this,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.documentElement,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];Object(n.a)(this,e),this.excludedElements=r,this.excludedRoots=a,Object(i.a)(this,"tabbableElements",void 0),Object(i.a)(this,"createExcludeFilterWithExemption",function(e){return function(o){if(!(e===o||o.contains(e))){if(t.excludedElements.includes(o))return!1;var n=!0,r=!1,i=void 0;try{for(var a,l=t.excludedRoots[Symbol.iterator]();!(n=(a=l.next()).done);n=!0){var u=a.value;if(u!==o&&u.contains(o))return!1}}catch(e){r=!0,i=e}finally{try{n||null==l.return||l.return()}finally{if(r)throw i}}}return!0}}),Object(i.a)(this,"filterAllExcluded",function(e){if(t.excludedElements.includes(e))return!1;var o=!0,n=!1,r=void 0;try{for(var i,a=t.excludedRoots[Symbol.iterator]();!(o=(i=a.next()).done);o=!0){var l=i.value;if(l!==e&&l.contains(e))return!1}}catch(e){n=!0,r=e}finally{try{o||null==a.return||a.return()}finally{if(n)throw r}}return!0}),this.tabbableElements=l()(o)}return Object(r.a)(e,[{key:"getNext",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.activeElement,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!(e instanceof HTMLElement))return Object(u.g)("Unable to tab to next element, `fromElement` given is not valid: ",e),null;var n=this.tabbableElements.filter(this.createExcludeFilterWithExemption(e)),r=n.indexOf(e);if(r<0)return null;var i=t?r-1:r+1;return o&&(i<0?i=n.length-1:i>=n.length&&(i=0)),n[i]||null}},{key:"getInitial",value:function(){var e=this.tabbableElements.filter(this.filterAllExcluded);return e.length>0?e[0]:null}},{key:"getLast",value:function(){var e=this.tabbableElements.filter(this.filterAllExcluded);return e.length>0?e[e.length-1]:null}}]),e}()},function(e,t,o){"use strict";o.d(t,"a",function(){return h});var n=o(33),r=o(3),i=o(6),a=o(8),l=o(1),u=o(13),s=o(9),c=o(2),p=o(555),f=o(17),h=function(e){function t(){return Object(r.a)(this,t),Object(a.a)(this,Object(l.a)(t).apply(this,arguments))}return Object(s.a)(t,e),Object(i.a)(t,[{key:"setGeneratedID",value:function(){var e=t.calcUniqueID(this.domNode.textContent||""),o=null;t.headerCounts[e]?(o=t.headerCounts[e],t.headerCounts[e]++):t.headerCounts[e]=1,null!==o&&(e+="-"+o),this.domNode.setAttribute("data-id",e)}}],[{key:"create",value:function(e){var o;o="number"==typeof e?e:e.level;var r=Object(u.a)(Object(l.a)(t),"create",this).call(this,o);return"object"===Object(n.a)(e)&&r.setAttribute("data-id",e.ref),r}},{key:"calcUniqueID",value:function(e){return encodeURIComponent(Object(f.l)(e))}},{key:"formats",value:function(e){return{level:Object(u.a)(Object(l.a)(t),"formats",this).call(this,e),ref:e.getAttribute("data-id")||""}}},{key:"resetCounters",value:function(){this.headerCounts={}}}]),t}(p.a);Object(c.a)(h,"headerCounts",{})},function(e,t,o){"use strict";o.d(t,"a",function(){return _});var n=o(33),r=o(3),i=o(6),a=o(8),l=o(1),u=o(9),s=o(10),c=o(2),p=o(0),f=o.n(p),h=o(616),d=o(5),m=o(516),g=o(22),v=o.n(g),j=o(87),b=o(528),y=o(533),_=function(e){function t(){var e,o;Object(r.a)(this,t);for(var n=arguments.length,i=new Array(n),u=0;u<n;u++)i[u]=arguments[u];return o=Object(a.a)(this,(e=Object(l.a)(t)).call.apply(e,[this].concat(i))),Object(c.a)(Object(s.a)(Object(s.a)(o)),"formatParagraph",function(e){e.preventDefault(),o.props.formatter.paragraph(o.props.lastGoodSelection),o.props.afterClickHandler&&o.props.afterClickHandler()}),Object(c.a)(Object(s.a)(Object(s.a)(o)),"formatH2",function(e){e.preventDefault(),o.props.formatter.h2(o.props.lastGoodSelection),o.props.afterClickHandler&&o.props.afterClickHandler()}),Object(c.a)(Object(s.a)(Object(s.a)(o)),"formatH3",function(e){e.preventDefault(),o.props.formatter.h3(o.props.lastGoodSelection),o.props.afterClickHandler&&o.props.afterClickHandler()}),Object(c.a)(Object(s.a)(Object(s.a)(o)),"formatBlockquote",function(e){e.preventDefault(),o.props.formatter.blockquote(o.props.lastGoodSelection),o.props.afterClickHandler&&o.props.afterClickHandler()}),Object(c.a)(Object(s.a)(Object(s.a)(o)),"formatCodeBlock",function(e){e.preventDefault(),o.props.formatter.codeBlock(o.props.lastGoodSelection),o.props.afterClickHandler&&o.props.afterClickHandler()}),Object(c.a)(Object(s.a)(Object(s.a)(o)),"formatSpoiler",function(e){e.preventDefault(),o.props.formatter.spoiler(o.props.lastGoodSelection),o.props.afterClickHandler&&o.props.afterClickHandler()}),o}return Object(u.a)(t,e),Object(i.a)(t,[{key:"render",value:function(){return f.a.createElement(h.a,{itemRole:"menuitemradio",ref:this.props.menuRef,menuItemData:this.menuItemData,renderAbove:this.props.renderAbove,renderLeft:this.props.renderLeft,onKeyDown:this.props.onKeyDown,className:v()(this.props.className)})}},{key:"menuItemData",get:function(){var e=this.props.activeFormats,t=!0;["header",b.c.blotName,j.a.blotName,y.c.blotName].forEach(function(o){o in e&&(t=!1)});var o="object"===Object(n.a)(e.header)?e.header.level:null;return[{icon:Object(m.m)(),label:Object(d.m)("Format as Paragraph"),onClick:this.formatParagraph,isActive:t,isDisabled:t},{icon:Object(m.h)(),label:Object(d.m)("Format as Title"),onClick:this.formatH2,isActive:2===e.header||2===o,isDisabled:2===e.header||2===o},{icon:Object(m.i)(),label:Object(d.m)("Format as Subtitle"),onClick:this.formatH3,isActive:3===e.header||3===o,isDisabled:3===e.header||3===o},{icon:Object(m.b)(),label:Object(d.m)("Format as blockquote"),onClick:this.formatBlockquote,isActive:!0===e[b.c.blotName],isDisabled:!0===e[b.c.blotName]},{icon:Object(m.e)(),label:Object(d.m)("Format as code block"),onClick:this.formatCodeBlock,isActive:!0===e[j.a.blotName],isDisabled:!0===e[j.a.blotName]},{icon:Object(m.n)("richEditorButton-icon"),label:Object(d.m)("Format as spoiler"),onClick:this.formatSpoiler,isActive:!0===e[y.c.blotName],isDisabled:!0===e[y.c.blotName]}]}}]),t}(f.a.PureComponent)},function(e,t,o){"use strict";o.d(t,"a",function(){return g});var n=o(33),r=o(3),i=o(6),a=o(8),l=o(1),u=o(9),s=o(0),c=o.n(s),p=o(555),f=o(516),h=o(528),d=o(87),m=o(533),g=function(e){function t(){return Object(r.a)(this,t),Object(a.a)(this,Object(l.a)(t).apply(this,arguments))}return Object(u.a)(t,e),Object(i.a)(t,[{key:"render",value:function(){var e=this.props.activeFormats,t=e[p.a.blotName];if("object"===Object(n.a)(t)){if(2===t.level)return Object(f.h)();if(3===t.level)return Object(f.i)()}return 2===t?Object(f.h)():3===t?Object(f.i)():!0===e[h.c.blotName]?Object(f.b)():!0===e[d.a.blotName]?Object(f.e)():!0===e[m.c.blotName]?Object(f.n)("richEditorButton-icon"):Object(f.m)()}}]),t}(c.a.Component)},function(e,t,o){"use strict";
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */var n;!function(e){e.FULL_SCREEN="full screen",e.MODAL_AS_SIDE_PANEL="render as full screen side panel",e.MODAL_AS_DROP_DOWN="render as drop down",e.LARGE="large",e.MEDIUM="medium",e.SMALL="small"}(n||(n={})),t.a=n},function(e,t,o){var n=o(520),r=o(542),i=o(559)("IE_PROTO"),a=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=r(e),n(e,i)?e[i]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?a:null}},function(e,t,o){e.exports=!o(519)&&!o(525)(function(){return 7!=Object.defineProperty(o(562)("div"),"a",{get:function(){return 7}}).a})},function(e,t,o){e.exports={default:o(704),__esModule:!0}},function(e,t,o){"use strict";t.__esModule=!0;var n=a(o(706)),r=a(o(716)),i="function"==typeof r.default&&"symbol"==typeof n.default?function(e){return typeof e}:function(e){return e&&"function"==typeof r.default&&e.constructor===r.default&&e!==r.default.prototype?"symbol":typeof e};function a(e){return e&&e.__esModule?e:{default:e}}t.default="function"==typeof r.default&&"symbol"===i(n.default)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof r.default&&e.constructor===r.default&&e!==r.default.prototype?"symbol":void 0===e?"undefined":i(e)}},function(e,t,o){"use strict";var n=o(529),r=o(513),i=o(599),a=o(521),l=o(526),u=o(709),s=o(547),c=o(594),p=o(512)("iterator"),f=!([].keys&&"next"in[].keys()),h=function(){return this};e.exports=function(e,t,o,d,m,g,v){u(o,t,d);var j,b,y,_=function(e){if(!f&&e in w)return w[e];switch(e){case"keys":case"values":return function(){return new o(this,e)}}return function(){return new o(this,e)}},O=t+" Iterator",C="values"==m,S=!1,w=e.prototype,x=w[p]||w["@@iterator"]||m&&w[m],k=x||_(m),E=m?C?_("entries"):k:void 0,I="Array"==t&&w.entries||x;if(I&&(y=c(I.call(new e)))!==Object.prototype&&y.next&&(s(y,O,!0),n||"function"==typeof y[p]||a(y,p,h)),C&&x&&"values"!==x.name&&(S=!0,k=function(){return x.call(this)}),n&&!v||!f&&!S&&w[p]||a(w,p,k),l[t]=k,l[O]=h,m)if(j={values:C?k:_("values"),keys:g?k:_("keys"),entries:E},v)for(b in j)b in w||i(w,b,j[b]);else r(r.P+r.F*(f||S),t,j);return j}},function(e,t,o){e.exports=o(521)},function(e,t,o){var n=o(520),r=o(522),i=o(711)(!1),a=o(559)("IE_PROTO");e.exports=function(e,t){var o,l=r(e),u=0,s=[];for(o in l)o!=a&&n(l,o)&&s.push(o);for(;t.length>u;)n(l,o=t[u++])&&(~i(s,o)||s.push(o));return s}},function(e,t,o){var n=o(532);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},function(e,t,o){var n=o(564),r=Math.min;e.exports=function(e){return e>0?r(n(e),9007199254740991):0}},function(e,t,o){var n=o(511).document;e.exports=n&&n.documentElement},function(e,t,o){var n=o(600),r=o(566).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,r)}},function(e,t){},function(e,t,o){e.exports={default:o(732),__esModule:!0}},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if((!a&&0!==a||e)&&i.default){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),a=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return a};var n,r=o(739),i=(n=r)&&n.__esModule?n:{default:n};var a=void 0;e.exports=t.default},function(e,t,o){var n=o(573),r=o(512)("iterator"),i=o(526);e.exports=o(506).getIteratorMethod=function(e){if(null!=e)return e[r]||e["@@iterator"]||i[n(e)]}},function(e,t,o){var n=o(514),r=o(544),i=o(512)("species");e.exports=function(e,t){var o,a=n(e).constructor;return void 0===a||null==(o=n(a)[i])?t:r(o)}},function(e,t,o){var n,r,i,a=o(530),l=o(747),u=o(603),s=o(562),c=o(511),p=c.process,f=c.setImmediate,h=c.clearImmediate,d=c.MessageChannel,m=c.Dispatch,g=0,v={},j=function(){var e=+this;if(v.hasOwnProperty(e)){var t=v[e];delete v[e],t()}},b=function(e){j.call(e.data)};f&&h||(f=function(e){for(var t=[],o=1;arguments.length>o;)t.push(arguments[o++]);return v[++g]=function(){l("function"==typeof e?e:Function(e),t)},n(g),g},h=function(e){delete v[e]},"process"==o(532)(p)?n=function(e){p.nextTick(a(j,e,1))}:m&&m.now?n=function(e){m.now(a(j,e,1))}:d?(i=(r=new d).port2,r.port1.onmessage=b,n=a(i.postMessage,i,1)):c.addEventListener&&"function"==typeof postMessage&&!c.importScripts?(n=function(e){c.postMessage(e+"","*")},c.addEventListener("message",b,!1)):n="onreadystatechange"in s("script")?function(e){u.appendChild(s("script")).onreadystatechange=function(){u.removeChild(this),j.call(e)}}:function(e){setTimeout(a(j,e,1),0)}),e.exports={set:f,clear:h}},function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},function(e,t,o){var n=o(514),r=o(518),i=o(574);e.exports=function(e,t){if(n(e),r(t)&&t.constructor===e)return t;var o=i.f(e);return(0,o.resolve)(t),o.promise}},function(e,t,o){"use strict";(function(e){function n(t){var o;o="undefined"!=typeof window?window:"undefined"!=typeof self?self:e;var n,r,i="undefined"!=typeof document&&document.attachEvent;if(!i){var a=(r=o.requestAnimationFrame||o.mozRequestAnimationFrame||o.webkitRequestAnimationFrame||function(e){return o.setTimeout(e,20)},function(e){return r(e)}),l=(n=o.cancelAnimationFrame||o.mozCancelAnimationFrame||o.webkitCancelAnimationFrame||o.clearTimeout,function(e){return n(e)}),u=function(e){var t=e.__resizeTriggers__,o=t.firstElementChild,n=t.lastElementChild,r=o.firstElementChild;n.scrollLeft=n.scrollWidth,n.scrollTop=n.scrollHeight,r.style.width=o.offsetWidth+1+"px",r.style.height=o.offsetHeight+1+"px",o.scrollLeft=o.scrollWidth,o.scrollTop=o.scrollHeight},s=function(e){if(!(e.target.className&&"function"==typeof e.target.className.indexOf&&e.target.className.indexOf("contract-trigger")<0&&e.target.className.indexOf("expand-trigger")<0)){var t=this;u(this),this.__resizeRAF__&&l(this.__resizeRAF__),this.__resizeRAF__=a(function(){(function(e){return e.offsetWidth!=e.__resizeLast__.width||e.offsetHeight!=e.__resizeLast__.height})(t)&&(t.__resizeLast__.width=t.offsetWidth,t.__resizeLast__.height=t.offsetHeight,t.__resizeListeners__.forEach(function(o){o.call(t,e)}))})}},c=!1,p="",f="animationstart",h="Webkit Moz O ms".split(" "),d="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),m=document.createElement("fakeelement");if(void 0!==m.style.animationName&&(c=!0),!1===c)for(var g=0;g<h.length;g++)if(void 0!==m.style[h[g]+"AnimationName"]){p="-"+h[g].toLowerCase()+"-",f=d[g],c=!0;break}var v="resizeanim",j="@"+p+"keyframes "+v+" { from { opacity: 0; } to { opacity: 0; } } ",b=p+"animation: 1ms "+v+"; "}return{addResizeListener:function(e,n){if(i)e.attachEvent("onresize",n);else{if(!e.__resizeTriggers__){var r=e.ownerDocument,a=o.getComputedStyle(e);a&&"static"==a.position&&(e.style.position="relative"),function(e){if(!e.getElementById("detectElementResize")){var o=(j||"")+".resize-triggers { "+(b||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',n=e.head||e.getElementsByTagName("head")[0],r=e.createElement("style");r.id="detectElementResize",r.type="text/css",null!=t&&r.setAttribute("nonce",t),r.styleSheet?r.styleSheet.cssText=o:r.appendChild(e.createTextNode(o)),n.appendChild(r)}}(r),e.__resizeLast__={},e.__resizeListeners__=[],(e.__resizeTriggers__=r.createElement("div")).className="resize-triggers",e.__resizeTriggers__.innerHTML='<div class="expand-trigger"><div></div></div><div class="contract-trigger"></div>',e.appendChild(e.__resizeTriggers__),u(e),e.addEventListener("scroll",s,!0),f&&(e.__resizeTriggers__.__animationListener__=function(t){t.animationName==v&&u(e)},e.__resizeTriggers__.addEventListener(f,e.__resizeTriggers__.__animationListener__))}e.__resizeListeners__.push(n)}},removeResizeListener:function(e,t){if(i)e.detachEvent("onresize",t);else if(e.__resizeListeners__.splice(e.__resizeListeners__.indexOf(t),1),!e.__resizeListeners__.length){e.removeEventListener("scroll",s,!0),e.__resizeTriggers__.__animationListener__&&(e.__resizeTriggers__.removeEventListener(f,e.__resizeTriggers__.__animationListener__),e.__resizeTriggers__.__animationListener__=null);try{e.__resizeTriggers__=!e.removeChild(e.__resizeTriggers__)}catch(e){}}}}}o.d(t,"a",function(){return n})}).call(this,o(48))},function(e,t,o){"use strict";var n=o(3),r=o(8),i=o(6),a=o(9),l=o(10),u=o(1),s=o(13),c=o(2),p=o(0),f=o.n(p),h=o(44),d=o.n(h),m=o(90),g=o(50),v=o.n(g),j=o(19),b=o(22),y=o.n(b),_=o(5);var O=o(524),C=function(e){function t(){var e,o;Object(n.a)(this,t);for(var i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];return o=Object(r.a)(this,(e=Object(u.a)(t)).call.apply(e,[this].concat(a))),Object(c.a)(Object(l.a)(Object(l.a)(o)),"handleDismissClick",function(e){e.preventDefault(),e.stopPropagation(),o.props.onDismissClick()}),o}return Object(a.a)(t,e),Object(i.a)(t,[{key:"render",value:function(){var e,t,o=this.props.id+"-description";return f.a.createElement("div",{className:y()("embedLoader-error",j.a),"aria-describedby":o,"aria-label":Object(_.m)("Error"),role:"alert","aria-live":"assertive"},(e="embedLoader-icon embedLoader-warningIcon",t=Object(_.m)("Warning"),f.a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 40 40",className:y()("icon","icon-userWaning",e),"aria-hidden":"true"},f.a.createElement("title",null,t),f.a.createElement("rect",{width:"40",height:"40",fill:"#d0021b",opacity:"0.31"}),f.a.createElement("path",{d:"M32.707,25.862a2.167,2.167,0,0,1-1.876,3.249H9.169a2.168,2.168,0,0,1-1.877-3.249L18.123,7.083a2.168,2.168,0,0,1,3.754,0Z",fill:"#d0021b"}),f.a.createElement("path",{d:"M20,20.979a2.077,2.077,0,1,0,2.076,2.077A2.076,2.076,0,0,0,20,20.979Zm-1.971-7.463.335,6.139a.541.541,0,0,0,.54.512H21.1a.543.543,0,0,0,.541-.512l.334-6.139a.542.542,0,0,0-.54-.572H18.569A.542.542,0,0,0,18.029,13.516Z",fill:"#fff"}))),f.a.createElement("span",{id:o,className:"embedLoader-errorMessage"},this.props.children),f.a.createElement(O.a,{title:Object(_.m)("Remove"),onClick:this.handleDismissClick}))}}]),t}(f.a.Component),S=o(109),w=o(95),x=o(110);
/**
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */var k,E=o(56),I=o(29),L=function(e){function t(){var e,o;Object(n.a)(this,t);for(var i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];return o=Object(r.a)(this,(e=Object(u.a)(t)).call.apply(e,[this].concat(a))),Object(c.a)(Object(l.a)(Object(l.a)(o)),"descrID",Object(I.c)("attachmentError")),o}return Object(a.a)(t,e),Object(i.a)(t,[{key:"render",value:function(){var e,t=this.props,o=t.title,n=t.name,r=o||n,i=this.props.message.split("\n"),a=(e=i,Object(S.a)(e)||Object(w.a)(e)||Object(x.a)()),l=a[0],u=a.slice(1),s=r&&0===u.length;return p.createElement("div",{className:y()("attachment","hasError",this.props.className,j.a),tabIndex:0,"aria-describedby":this.descrID,"aria-label":Object(_.m)("Error"),role:"alert","aria-live":"assertive"},p.createElement("div",{className:"attachment-box"},p.createElement("div",{className:"attachment-format"},Object(E.f)()),p.createElement("div",{className:"attachment-main"},p.createElement("div",{id:this.descrID,className:"attachment-title"},l),p.createElement("div",{className:"attachment-body"},u.map(function(e,t){return p.createElement(p.Fragment,null,e,t!==u.length-1?p.createElement("br",null):null)})),s&&p.createElement("div",{className:"attachment-metas metas"},p.createElement("span",{className:"meta"},r))),p.createElement(O.a,{title:Object(_.m)("Cancel"),className:"attachment-close",onClick:this.props.deleteAttachment})))}}]),t}(p.Component);o.d(t,"a",function(){return k}),o.d(t,"b",function(){return R}),function(e){e.FILE="file",e.STANDARD="standard"}(k||(k={}));var R=function(e){function t(e,o){var i;Object(n.a)(this,t),i=Object(r.a)(this,Object(u.a)(t).call(this,e)),Object(c.a)(Object(l.a)(Object(l.a)(i)),"data",void 0),Object(c.a)(Object(l.a)(Object(l.a)(i)),"handleRemoveClick",function(){return i.remove()}),i.data=o;var a=v()("embedLoader");if(!o.error)return Object(r.a)(i);if(o.type===k.FILE){var s=new Date,p=o.file;e.classList.remove(j.a),d.a.render(f.a.createElement(L,{message:o.error.message,name:p.name,dateUploaded:s.toISOString(),deleteAttachment:i.handleRemoveClick}),e)}else d.a.render(f.a.createElement(C,{onDismissClick:i.handleRemoveClick,id:a},o.error.message),e);return i}return Object(a.a)(t,e),Object(i.a)(t,[{key:"attach",value:function(){Object(s.a)(Object(u.a)(t.prototype),"attach",this).call(this),this.data.error||(this.remove(),this.quill&&this.quill.update())}}],[{key:"create",value:function(e){var o=Object(s.a)(Object(u.a)(t),"create",this).call(this,e);return o.classList.add("js-embed"),o}}]),t}(m.a);Object(c.a)(R,"blotName","embed-error"),Object(c.a)(R,"className","embed-error"),Object(c.a)(R,"tagName","div")},function(e,t,o){"use strict";var n=o(3),r=o(6),i=o(8),a=o(1),l=o(13),u=o(9),s=o(2),c=o(0),p=o.n(c),f=o(44),h=o.n(f),d=o(5),m=o(19),g=o(24),v=o(90),j=o(10),b=o(22),y=o.n(b),_=o(524),O=o(28),C=o(588),S=function(e){function t(){var e,o;Object(n.a)(this,t);for(var r=arguments.length,l=new Array(r),u=0;u<r;u++)l[u]=arguments[u];return o=Object(i.a)(this,(e=Object(a.a)(t)).call.apply(e,[this].concat(l))),Object(s.a)(Object(j.a)(Object(j.a)(o)),"state",{progress:0}),Object(s.a)(Object(j.a)(Object(j.a)(o)),"onProgressEvent",function(e){var t=e.loaded/e.total,n=Math.round(100*t);o.setState({progress:n})}),o}return Object(u.a)(t,e),Object(r.a)(t,[{key:"render",value:function(){var e=this.props,t=e.title,o=e.name,n=e.type,r=t||o;return c.createElement("div",{className:y()("attachment","isLoading",this.props.className,m.a),tabIndex:0,"aria-label":Object(d.m)("Uploading...")},c.createElement("div",{className:"attachment-box attachment-loadingContent"},c.createElement("div",{className:"attachment-format"},Object(O.b)(n)),c.createElement("div",{className:"attachment-main"},c.createElement("div",{className:"attachment-title"},r),c.createElement("div",{className:"attachment-metas metas"},c.createElement("span",{className:"meta"},Object(d.m)("Uploading...")))),c.createElement(_.a,{title:Object(d.m)("Cancel"),className:"attachment-close",onClick:this.props.deleteAttachment})),c.createElement("div",{className:"attachment-loadingProgress",style:{width:"".concat(Math.min(this.state.progress,100),"%")}}))}},{key:"componentDidMount",value:function(){var e=this.props.progressEventEmitter;e instanceof C.a&&e.addEventListener(this.onProgressEvent)}},{key:"componentWillUnmount",value:function(){var e=this.props.progressEventEmitter;e instanceof C.a&&e.removeEventListener(this.onProgressEvent)}}]),t}(c.Component);o.d(t,"a",function(){return w});
/**
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
var w=function(e){function t(){return Object(n.a)(this,t),Object(i.a)(this,Object(a.a)(t).apply(this,arguments))}return Object(u.a)(t,e),Object(r.a)(t,null,[{key:"create",value:function(e){var t;switch(e.loaderData.type){case"link":t=this.createLinkLoader(e.loaderData.link||"");break;case"image":t=this.createImageLoader();break;case"file":t=this.createFileLoader(e);break;default:throw new Error("Could not determine loader type for embed blot.")}return Object(g.l)(t,"loadingDataKey",e),t}},{key:"value",value:function(e){var t=Object(g.f)(e,"loadingDataKey",null);if(!t)throw new Error("A loading blot should have data set");return t}},{key:"createFileLoader",value:function(e){var o=Object(l.a)(Object(a.a)(t),"create",this).call(this),n=e.loaderData.file,r=Object(O.c)(n.type),i=new Date;return h.a.render(p.a.createElement(S,{type:r,size:n.size,dateUploaded:i.toISOString(),name:n.name,progressEventEmitter:e.loaderData.progressEventEmitter}),o,function(){return o.classList.remove(m.a)}),o}},{key:"createImageLoader",value:function(){var e=Object(l.a)(Object(a.a)(t),"create",this).call(this);return e.classList.remove(m.a),e.classList.add("js-embed"),e.classList.add("embedLinkLoader"),e.innerHTML="<div class='embedLoader'>\n                            <div class='embedLoader-box ".concat(m.a,"' aria-label='").concat(Object(d.m)("Loading..."),"'><div class='embedLoader-loader'></div>\n                            </div>\n                        </div>"),e}},{key:"createLinkLoader",value:function(e){var o=Object(l.a)(Object(a.a)(t),"create",this).call(this);o.classList.remove(m.a),o.classList.add("js-embed"),o.classList.add("embedLinkLoader");var n=Object(g.e)(e);return o.innerHTML='<a href="#" class="embedLinkLoader-link '.concat(m.a,'">').concat(n,"&nbsp;<span aria-hidden=\"true\" class='embedLinkLoader-loader'></span></a>"),o}}]),t}(v.a);Object(s.a)(w,"blotName","embed-loading"),Object(s.a)(w,"className","js-embedLoader"),Object(s.a)(w,"tagName","div")},function(e,t,o){"use strict";var n=o(66),r=o(3),i=o(6),a=o(8),l=o(1),u=o(9),s=o(10),c=o(2),p=o(0),f=o.n(p),h=o(22),d=o.n(h),m=function(e){function t(){var e,o;Object(r.a)(this,t);for(var n=arguments.length,i=new Array(n),u=0;u<n;u++)i[u]=arguments[u];return o=Object(a.a)(this,(e=Object(l.a)(t)).call.apply(e,[this].concat(i))),Object(c.a)(Object(s.a)(Object(s.a)(o)),"buttonRef",f.a.createRef()),Object(c.a)(Object(s.a)(Object(s.a)(o)),"onClick",function(e){o.props.isDisabled?e.preventDefault():o.props.onClick(e)}),Object(c.a)(Object(s.a)(Object(s.a)(o)),"handleKeyPress",function(e){switch(e.key){case"ArrowRight":case"ArrowDown":e.stopPropagation(),e.preventDefault(),o.props.focusNextItem();break;case"ArrowUp":case"ArrowLeft":e.stopPropagation(),e.preventDefault(),o.props.focusPrevItem()}}),o}return Object(u.a)(t,e),Object(i.a)(t,[{key:"render",value:function(){var e=this.props,t=e.label,o=(e.isDisabled,e.isActive),r=(e.onClick,e.icon),i=e.role,a=d()("richEditor-button","richEditor-formatButton","richEditor-menuItem",{isActive:o}),l={role:i,"aria-label":t};return"menuitem"===i?l["aria-pressed"]=o:l["aria-checked"]=o,f.a.createElement("button",Object(n.a)({},l,{className:a,type:"button",onClick:this.onClick,onKeyDown:this.handleKeyPress,ref:this.buttonRef}),r)}},{key:"focus",value:function(){this.buttonRef.current&&this.buttonRef.current.focus()}}]),t}(f.a.PureComponent),g=o(556);o.d(t,"a",function(){return v});
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
var v=function(e){function t(){var e,o;Object(r.a)(this,t);for(var n=arguments.length,i=new Array(n),u=0;u<n;u++)i[u]=arguments[u];return o=Object(a.a)(this,(e=Object(l.a)(t)).call.apply(e,[this].concat(i))),Object(c.a)(Object(s.a)(Object(s.a)(o)),"menuItemRefs",[]),o}return Object(u.a)(t,e),Object(i.a)(t,[{key:"render",value:function(){var e=this,t=this.props.menuItemData,o=t.length-1;return f.a.createElement("div",{className:d()("richEditor-menu",this.props.className),role:"menu",style:Object(g.a)(!!this.props.renderAbove,!!this.props.renderLeft,!!this.props.legacyMode),"aria-orientation":this.props.orientation,onKeyDown:this.props.onKeyDown},f.a.createElement("div",{className:"richEditor-menuItems"},this.props.menuItemData.map(function(t,r){var i=0===r?o:r-1,a=r===o?0:r+1;return f.a.createElement(m,Object(n.a)({},t,{role:e.props.itemRole,key:r,focusNextItem:function(){var t=e.menuItemRefs[a];t&&t.focus()},focusPrevItem:function(){var t=e.menuItemRefs[i];t&&t.focus()},ref:function(t){return e.menuItemRefs.push(t)}}))})))}},{key:"focusFirstItem",value:function(){var e=this.menuItemRefs[0];e&&e.focus()}},{key:"focusLastItem",value:function(){var e=this.menuItemRefs.length-1,t=this.menuItemRefs[e];t&&t.focus()}}]),t}(f.a.Component);Object(c.a)(v,"defaultProps",{itemRole:"menuitem",orientation:"horizontal"})},function(e,t,o){"use strict";(function(e){var n=o(3),r=o(6),i=o(8),a=o(1),l=o(9),u=o(10),s=o(2),c=o(0),p=o.n(c),f=o(5),h=o(93),d=o(17),m=o(24),g=o(771),v=o(770),j=o(510),b=o(684),y=o(49),_=o(61),O=o(65),C=o(45),S=o(766),w=o(769),x=o(696),k=o(22),E=o.n(k),I=o(590),L=o(697),R=o(698),T=o(699),M=o.n(T),A=o(768),z=o(234),N=o.n(z),P=function(e){function t(){var e,o;Object(n.a)(this,t);for(var r=arguments.length,l=new Array(r),c=0;c<r;c++)l[c]=arguments[c];return o=Object(i.a)(this,(e=Object(a.a)(t)).call.apply(e,[this].concat(l))),Object(s.a)(Object(u.a)(Object(u.a)(o)),"quillMountRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"embedBarRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"scrollContainerRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"domID",Object(x.uniqueId)("editor-")),Object(s.a)(Object(u.a)(Object(u.a)(o)),"store",Object(h.a)()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"skipCallback",!1),Object(s.a)(Object(u.a)(Object(u.a)(o)),"quillID",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"quill",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"quoteHandler",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"onQuillUpdate",M()(function(e,t,n,r){if(o.skipCallback)o.skipCallback=!1;else{o.props.onChange&&e===C.a.events.TEXT_CHANGE&&r!==C.a.sources.SILENT&&o.props.onChange(o.getEditorOperations());var i=!1;e===C.a.events.SELECTION_CHANGE?i=!0:r!==C.a.sources.SILENT&&(i=!0),i&&o.store.dispatch(_.d.setSelection(o.quillID,o.quill.getSelection(),o.quill))}},1e3/60)),Object(s.a)(Object(u.a)(Object(u.a)(o)),"quoteButtonClickHandler",function(e,t){e.preventDefault();var n=o.quill.getModule("embed/insertion"),r=t.getAttribute("data-scrape-url")||"";n.scrapeMedia(r)}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"handleGlobalSelectionUpdate",function(){window.requestAnimationFrame(function(){o.onQuillUpdate(C.a.events.SELECTION_CHANGE,null,null,C.a.sources.USER)})}),o}return Object(l.a)(t,e),Object(r.a)(t,[{key:"render",value:function(){return this.props.legacyMode?this.renderLegacy():this.renderModern()}},{key:"renderModern",value:function(){var e=this.props.className;return p.a.createElement("div",{className:E()("richEditor",e,{isDisabled:this.props.isLoading}),"aria-label":Object(f.m)("Type your message."),"aria-describedby":this.descriptionID,role:"textbox","aria-multiline":!0,id:this.domID},this.renderContexts(p.a.createElement(p.a.Fragment,null,this.renderEmbedBar(),p.a.createElement("div",{className:"richEditor-scrollFrame"},p.a.createElement("div",{className:"richEditor-scrollContainer",ref:this.scrollContainerRef},p.a.createElement("div",{className:E()("richEditor-frame InputBox isMenuInset"),id:"testScroll"},this.renderMountPoint(),this.renderInlineToolbars()),this.renderParagraphToolbar())))))}},{key:"renderLegacy",value:function(){return this.renderContexts(p.a.createElement("div",{className:E()("richEditor-frame","InputBox"),id:"testScroll"},this.renderMountPoint(),this.renderParagraphToolbar(),this.renderInlineToolbars(),this.renderEmbedBar()))}},{key:"renderMountPoint",value:function(){return p.a.createElement("div",{className:"richEditor-textWrap",ref:this.quillMountRef},p.a.createElement("div",{className:"ql-editor richEditor-text userContent","data-gramm":"false",contentEditable:this.props.isLoading,"data-placeholder":"Create a new post...",tabIndex:0}))}},{key:"renderEmbedBar",value:function(){return this.quill&&p.a.createElement(A.a,{isLoading:!!this.props.isLoading,isMobile:this.isMobile,legacyMode:this.props.legacyMode,barRef:this.embedBarRef})}},{key:"renderParagraphToolbar",value:function(){return this.quill&&!this.props.isLoading&&!this.isMobile&&p.a.createElement(R.a,null)}},{key:"renderInlineToolbars",value:function(){return this.quill&&!this.props.isLoading&&p.a.createElement(p.a.Fragment,null,p.a.createElement(v.a,null),p.a.createElement(g.a,null))}},{key:"renderContexts",value:function(e){var t=this.props,o=t.isLoading,n=t.legacyMode;return p.a.createElement(y.a,{store:this.store},p.a.createElement(j.a,{value:{quill:this.quill,editorID:this.quillID,legacyMode:n,isLoading:!!o}},p.a.createElement(b.a,{id:this.descriptionID}),e))}},{key:"componentDidMount",value:function(){document.body.classList.add("hasFullHeight"),Object(w.a)();var e={theme:"vanilla",modules:{syntax:{highlight:function(e){return N.a.highlightAuto(e).value}}},scrollingContainer:this.scrollContainerRef.current||document.documentElement};this.quill=new C.a(this.quillMountRef.current,e),this.props.initialValue&&this.quill.setContents(this.props.initialValue),this.props.isLoading&&this.quill.disable(),window.quill=this.quill,this.quillID=Object(O.e)(this.quill),this.setupLegacyTextAreaSync(),this.setupDebugPasteListener(),this.store.dispatch(_.d.createInstance(this.quillID)),this.quill.on(C.a.events.EDITOR_CHANGE,this.onQuillUpdate),this.addGlobalSelectionHandler(),this.addQuoteHandler(),this.forceUpdate()}},{key:"componentWillUnmount",value:function(){this.removeGlobalSelectionHandler(),this.removeQuoteHandler(),this.quill.off(C.a.events.EDITOR_CHANGE,this.onQuillUpdate),this.store.dispatch(_.d.deleteInstance(this.quillID))}},{key:"componentDidUpdate",value:function(e){e.isLoading&&!this.props.isLoading?this.quill.enable():!e.isLoading&&this.props.isLoading&&this.quill.disable(),!e.reinitialize&&this.props.reinitialize&&this.props.initialValue&&(this.skipCallback=!0,this.setEditorContent(this.props.initialValue))}},{key:"getEditorOperations",value:function(){return this.ensureUniqueHeaderIDs(),this.quill.getContents().ops}},{key:"ensureUniqueHeaderIDs",value:function(){I.a.resetCounters(),this.quill.scroll.descendants(function(e){return e instanceof I.a},0,this.quill.scroll.length()).forEach(function(e){return e.setGeneratedID()}),this.quill.update(C.a.sources.API)}},{key:"getEditorText",value:function(){return this.quill.getText()}},{key:"setEditorContent",value:function(e){Object(d.f)("Setting existing content as contents of editor"),this.quill.setContents(e),this.quill.getModule("history").clear()}},{key:"setupLegacyTextAreaSync",value:function(){var e=this;if(this.props.legacyMode){var t=this.props.legacyTextArea;if(t){var o=t.value;o&&this.setEditorContent(JSON.parse(o)),this.quill.on("text-change",function(){t.value=JSON.stringify(e.quill.getContents().ops)});var n=this.quill.container.closest("form");n&&n.addEventListener("X-ClearCommentForm",function(){e.quill.setContents([]),e.quill.setSelection(null,C.a.sources.USER)})}}}},{key:"addQuoteHandler",value:function(){this.quoteHandler=Object(m.b)("click",".js-quoteButton",this.quoteButtonClickHandler)}},{key:"removeQuoteHandler",value:function(){this.quoteHandler&&Object(m.k)(this.quoteHandler)}},{key:"addGlobalSelectionHandler",value:function(){document.addEventListener(O.a,this.handleGlobalSelectionUpdate)}},{key:"removeGlobalSelectionHandler",value:function(){document.removeEventListener(O.a,this.handleGlobalSelectionUpdate)}},{key:"setupDebugPasteListener",value:function(){var e=this;if(this.props.legacyMode){var t=this.props.legacyTextArea;Object(d.b)()&&t&&t.addEventListener("paste",function(t){t.stopPropagation(),t.preventDefault();var o=(t.clipboardData||window.clipboardData).getData("Text"),n=JSON.parse(o);e.quill.setContents(n)})}}},{key:"isMobile",get:function(){var e=!1;return!this.props.legacyMode&&this.props.device&&(e=this.props.device===L.a.MOBILE),e}},{key:"descriptionID",get:function(){return this.domID+"-description"}}]),t}(p.a.Component);t.a=Object(S.hot)(e)(P)}).call(this,o(253)(e))},function(e,t,o){var n=o(619);e.exports=function(e,t){return n(e,t)}},function(e,t,o){var n=o(620),r=o(250);e.exports=function e(t,o,i,a,l){return t===o||(null==t||null==o||!r(t)&&!r(o)?t!=t&&o!=o:n(t,o,i,a,e,l))}},function(e,t,o){var n=o(621),r=o(578),i=o(653),a=o(657),l=o(679),u=o(248),s=o(579),c=o(580),p=1,f="[object Arguments]",h="[object Array]",d="[object Object]",m=Object.prototype.hasOwnProperty;e.exports=function(e,t,o,g,v,j){var b=u(e),y=u(t),_=b?h:l(e),O=y?h:l(t),C=(_=_==f?d:_)==d,S=(O=O==f?d:O)==d,w=_==O;if(w&&s(e)){if(!s(t))return!1;b=!0,C=!1}if(w&&!C)return j||(j=new n),b||c(e)?r(e,t,o,g,v,j):i(e,t,_,o,g,v,j);if(!(o&p)){var x=C&&m.call(e,"__wrapped__"),k=S&&m.call(t,"__wrapped__");if(x||k){var E=x?e.value():e,I=k?t.value():t;return j||(j=new n),v(E,I,o,g,j)}}return!!w&&(j||(j=new n),a(e,t,o,g,v,j))}},function(e,t,o){var n=o(534),r=o(627),i=o(628),a=o(629),l=o(630),u=o(631);function s(e){var t=this.__data__=new n(e);this.size=t.size}s.prototype.clear=r,s.prototype.delete=i,s.prototype.get=a,s.prototype.has=l,s.prototype.set=u,e.exports=s},function(e,t){e.exports=function(){this.__data__=[],this.size=0}},function(e,t,o){var n=o(535),r=Array.prototype.splice;e.exports=function(e){var t=this.__data__,o=n(t,e);return!(o<0||(o==t.length-1?t.pop():r.call(t,o,1),--this.size,0))}},function(e,t,o){var n=o(535);e.exports=function(e){var t=this.__data__,o=n(t,e);return o<0?void 0:t[o][1]}},function(e,t,o){var n=o(535);e.exports=function(e){return n(this.__data__,e)>-1}},function(e,t,o){var n=o(535);e.exports=function(e,t){var o=this.__data__,r=n(o,e);return r<0?(++this.size,o.push([e,t])):o[r][1]=t,this}},function(e,t,o){var n=o(534);e.exports=function(){this.__data__=new n,this.size=0}},function(e,t){e.exports=function(e){var t=this.__data__,o=t.delete(e);return this.size=t.size,o}},function(e,t){e.exports=function(e){return this.__data__.get(e)}},function(e,t){e.exports=function(e){return this.__data__.has(e)}},function(e,t,o){var n=o(534),r=o(551),i=o(552),a=200;e.exports=function(e,t){var o=this.__data__;if(o instanceof n){var l=o.__data__;if(!r||l.length<a-1)return l.push([e,t]),this.size=++o.size,this;o=this.__data__=new i(l)}return o.set(e,t),this.size=o.size,this}},function(e,t,o){var n=o(576),r=o(633),i=o(91),a=o(577),l=/^\[object .+?Constructor\]$/,u=Function.prototype,s=Object.prototype,c=u.toString,p=s.hasOwnProperty,f=RegExp("^"+c.call(p).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||r(e))&&(n(e)?f:l).test(a(e))}},function(e,t,o){var n,r=o(634),i=(n=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";e.exports=function(e){return!!i&&i in e}},function(e,t,o){var n=o(86)["__core-js_shared__"];e.exports=n},function(e,t){e.exports=function(e,t){return null==e?void 0:e[t]}},function(e,t,o){var n=o(637),r=o(534),i=o(551);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||r),string:new n}}},function(e,t,o){var n=o(638),r=o(639),i=o(640),a=o(641),l=o(642);function u(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=r,u.prototype.get=i,u.prototype.has=a,u.prototype.set=l,e.exports=u},function(e,t,o){var n=o(536);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},function(e,t){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},function(e,t,o){var n=o(536),r="__lodash_hash_undefined__",i=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var o=t[e];return o===r?void 0:o}return i.call(t,e)?t[e]:void 0}},function(e,t,o){var n=o(536),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:r.call(t,e)}},function(e,t,o){var n=o(536),r="__lodash_hash_undefined__";e.exports=function(e,t){var o=this.__data__;return this.size+=this.has(e)?0:1,o[e]=n&&void 0===t?r:t,this}},function(e,t,o){var n=o(537);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},function(e,t){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},function(e,t,o){var n=o(537);e.exports=function(e){return n(this,e).get(e)}},function(e,t,o){var n=o(537);e.exports=function(e){return n(this,e).has(e)}},function(e,t,o){var n=o(537);e.exports=function(e,t){var o=n(this,e),r=o.size;return o.set(e,t),this.size+=o.size==r?0:1,this}},function(e,t,o){var n=o(552),r=o(649),i=o(650);function a(e){var t=-1,o=null==e?0:e.length;for(this.__data__=new n;++t<o;)this.add(e[t])}a.prototype.add=a.prototype.push=r,a.prototype.has=i,e.exports=a},function(e,t){var o="__lodash_hash_undefined__";e.exports=function(e){return this.__data__.set(e,o),this}},function(e,t){e.exports=function(e){return this.__data__.has(e)}},function(e,t){e.exports=function(e,t){for(var o=-1,n=null==e?0:e.length;++o<n;)if(t(e[o],o,e))return!0;return!1}},function(e,t){e.exports=function(e,t){return e.has(t)}},function(e,t,o){var n=o(77),r=o(654),i=o(575),a=o(578),l=o(655),u=o(656),s=1,c=2,p="[object Boolean]",f="[object Date]",h="[object Error]",d="[object Map]",m="[object Number]",g="[object RegExp]",v="[object Set]",j="[object String]",b="[object Symbol]",y="[object ArrayBuffer]",_="[object DataView]",O=n?n.prototype:void 0,C=O?O.valueOf:void 0;e.exports=function(e,t,o,n,O,S,w){switch(o){case _:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case y:return!(e.byteLength!=t.byteLength||!S(new r(e),new r(t)));case p:case f:case m:return i(+e,+t);case h:return e.name==t.name&&e.message==t.message;case g:case j:return e==t+"";case d:var x=l;case v:var k=n&s;if(x||(x=u),e.size!=t.size&&!k)return!1;var E=w.get(e);if(E)return E==t;n|=c,w.set(e,t);var I=a(x(e),x(t),n,O,S,w);return w.delete(e),I;case b:if(C)return C.call(e)==C.call(t)}return!1}},function(e,t,o){var n=o(86).Uint8Array;e.exports=n},function(e,t){e.exports=function(e){var t=-1,o=Array(e.size);return e.forEach(function(e,n){o[++t]=[n,e]}),o}},function(e,t){e.exports=function(e){var t=-1,o=Array(e.size);return e.forEach(function(e){o[++t]=e}),o}},function(e,t,o){var n=o(658),r=1,i=Object.prototype.hasOwnProperty;e.exports=function(e,t,o,a,l,u){var s=o&r,c=n(e),p=c.length;if(p!=n(t).length&&!s)return!1;for(var f=p;f--;){var h=c[f];if(!(s?h in t:i.call(t,h)))return!1}var d=u.get(e);if(d&&u.get(t))return d==t;var m=!0;u.set(e,t),u.set(t,e);for(var g=s;++f<p;){var v=e[h=c[f]],j=t[h];if(a)var b=s?a(j,v,h,t,e,u):a(v,j,h,e,t,u);if(!(void 0===b?v===j||l(v,j,o,a,u):b)){m=!1;break}g||(g="constructor"==h)}if(m&&!g){var y=e.constructor,_=t.constructor;y!=_&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof _&&_ instanceof _)&&(m=!1)}return u.delete(e),u.delete(t),m}},function(e,t,o){var n=o(659),r=o(661),i=o(664);e.exports=function(e){return n(e,i,r)}},function(e,t,o){var n=o(660),r=o(248);e.exports=function(e,t,o){var i=t(e);return r(e)?i:n(i,o(e))}},function(e,t){e.exports=function(e,t){for(var o=-1,n=t.length,r=e.length;++o<n;)e[r+o]=t[o];return e}},function(e,t,o){var n=o(662),r=o(663),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,l=a?function(e){return null==e?[]:(e=Object(e),n(a(e),function(t){return i.call(e,t)}))}:r;e.exports=l},function(e,t){e.exports=function(e,t){for(var o=-1,n=null==e?0:e.length,r=0,i=[];++o<n;){var a=e[o];t(a,o,e)&&(i[r++]=a)}return i}},function(e,t){e.exports=function(){return[]}},function(e,t,o){var n=o(665),r=o(674),i=o(678);e.exports=function(e){return i(e)?n(e):r(e)}},function(e,t,o){var n=o(666),r=o(667),i=o(248),a=o(579),l=o(670),u=o(580),s=Object.prototype.hasOwnProperty;e.exports=function(e,t){var o=i(e),c=!o&&r(e),p=!o&&!c&&a(e),f=!o&&!c&&!p&&u(e),h=o||c||p||f,d=h?n(e.length,String):[],m=d.length;for(var g in e)!t&&!s.call(e,g)||h&&("length"==g||p&&("offset"==g||"parent"==g)||f&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||l(g,m))||d.push(g);return d}},function(e,t){e.exports=function(e,t){for(var o=-1,n=Array(e);++o<e;)n[o]=t(o);return n}},function(e,t,o){var n=o(668),r=o(250),i=Object.prototype,a=i.hasOwnProperty,l=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(e){return r(e)&&a.call(e,"callee")&&!l.call(e,"callee")};e.exports=u},function(e,t,o){var n=o(249),r=o(250),i="[object Arguments]";e.exports=function(e){return r(e)&&n(e)==i}},function(e,t){e.exports=function(){return!1}},function(e,t){var o=9007199254740991,n=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var r=typeof e;return!!(t=null==t?o:t)&&("number"==r||"symbol"!=r&&n.test(e))&&e>-1&&e%1==0&&e<t}},function(e,t,o){var n=o(249),r=o(581),i=o(250),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&r(e.length)&&!!a[n(e)]}},function(e,t){e.exports=function(e){return function(t){return e(t)}}},function(e,t,o){(function(e){var n=o(254),r=t&&!t.nodeType&&t,i=r&&"object"==typeof e&&e&&!e.nodeType&&e,a=i&&i.exports===r&&n.process,l=function(){try{var e=i&&i.require&&i.require("util").types;return e||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=l}).call(this,o(252)(e))},function(e,t,o){var n=o(675),r=o(676),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return r(e);var t=[];for(var o in Object(e))i.call(e,o)&&"constructor"!=o&&t.push(o);return t}},function(e,t){var o=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||o)}},function(e,t,o){var n=o(677)(Object.keys,Object);e.exports=n},function(e,t){e.exports=function(e,t){return function(o){return e(t(o))}}},function(e,t,o){var n=o(576),r=o(581);e.exports=function(e){return null!=e&&r(e.length)&&!n(e)}},function(e,t,o){var n=o(680),r=o(551),i=o(681),a=o(682),l=o(683),u=o(249),s=o(577),c=s(n),p=s(r),f=s(i),h=s(a),d=s(l),m=u;(n&&"[object DataView]"!=m(new n(new ArrayBuffer(1)))||r&&"[object Map]"!=m(new r)||i&&"[object Promise]"!=m(i.resolve())||a&&"[object Set]"!=m(new a)||l&&"[object WeakMap]"!=m(new l))&&(m=function(e){var t=u(e),o="[object Object]"==t?e.constructor:void 0,n=o?s(o):"";if(n)switch(n){case c:return"[object DataView]";case p:return"[object Map]";case f:return"[object Promise]";case h:return"[object Set]";case d:return"[object WeakMap]"}return t}),e.exports=m},function(e,t,o){var n=o(523)(o(86),"DataView");e.exports=n},function(e,t,o){var n=o(523)(o(86),"Promise");e.exports=n},function(e,t,o){var n=o(523)(o(86),"Set");e.exports=n},function(e,t,o){var n=o(523)(o(86),"WeakMap");e.exports=n},function(e,t,o){"use strict";o.d(t,"a",function(){return a});var n=o(0),r=o.n(n),i=o(5);
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function a(e){return r.a.createElement("p",{id:e.id,className:"sr-only"},Object(i.m)("richEditor.description.title"),Object(i.m)("richEditor.description.paragraphMenu"),Object(i.m)("richEditor.description.inlineMenu"),Object(i.m)("richEditor.description.embed"))}},function(e,t,o){"use strict";(function(e){o.d(t,"a",function(){return b});var n=o(3),r=o(8),i=o(1),a=o(6),l=o(9),u=o(2),s=o(24),c=o(50),p=o.n(c),f=o(19),h=o(5),d=o(17),m=o(90),g=o(614),v=o(615),j=o(65),b=function(t){function o(e,t){var a,l=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return Object(n.a)(this,o),a=Object(r.a)(this,Object(i.a)(o).call(this,e)),l&&a.replaceLoaderWithFinalForm(t),a}return Object(l.a)(o,t),Object(a.a)(o,null,[{key:"create",value:function(e){return v.a.create(e)}},{key:"value",value:function(e){if(e.classList.contains(v.a.className))return v.a.value(e);var t=Object(s.f)(e,"__embed-data__",!1);return t}},{key:"createEmbedWarningFallback",value:function(e){var t=document.createElement("div");t.classList.add("embedExternal"),t.classList.add("embedLinkLoader"),t.classList.add("embedLinkLoader-error");var o,n=Object(s.e)(e),r=Object(h.m)("This embed could not be loaded in your browser.");return t.innerHTML='<a href="#" class="embedLinkLoader-link '.concat(f.a,'" tabindex="-1">').concat(n,"&nbsp;").concat('\n<svg class="embedLinkLoader-failIcon" title="'.concat(o=r,'" aria-label="').concat(o,'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">\n    <title>').concat(o,'</title>\n    <circle cx="8" cy="8" r="8" style="fill: #f5af15"/>\n    <circle cx="8" cy="8" r="7.5" style="fill: none;stroke: #000;stroke-opacity: 0.122"/>\n    <path d="M11,10.4V8h2v2.4L12.8,13H11.3Zm0,4h2v2H11Z" transform="translate(-4 -4)" style="fill: #fff"/>\n</svg>'),"</a>"),t}},{key:"createEmbedFromData",value:function(t,n){var r=m.a.create(t);r.classList.add("js-embed"),r.classList.add("embedResponsive"),r.classList.remove(f.a);var i=document.createElement("span");i.innerHTML=Object(h.m)("richEditor.externalEmbed.description"),i.classList.add("sr-only"),i.id=p()("richEditor-embed-description-");var a=document.createElement("div");a.classList.add("embedExternal"),a.classList.add("embed"+Object(d.a)(t.type));var l=document.createElement("div");return l.classList.add(f.a),l.setAttribute("aria-label","External embed content - "+t.type),l.setAttribute("aria-describedby",i.id),l.classList.add("embedExternal-content"),l.tabIndex=-1,n&&r.appendChild(n),r.appendChild(a),r.appendChild(i),a.appendChild(l),e(function(){Object(f.d)({root:a,content:l},t).then(function(){Object(j.c)(),n&&n.remove()}).catch(function(e){Object(j.c)(),Object(d.g)(e);var l=o.createEmbedWarningFallback(t.url);a.remove(),i.remove(),n&&n.remove(),r.appendChild(l)})}),r}}]),Object(a.a)(o,[{key:"replaceLoaderWithFinalForm",value:function(e){var t,n=this,r=this.resolveDataFromValue(e);if(!(r instanceof Promise)){var i=this.quill;return this.remove(),void(i&&i.update())}r.then(function(r){var i={data:r,loaderData:e.loaderData},a=n.domNode.querySelector(".embedLinkLoader"),l=o.createEmbedFromData(r,a);Object(s.l)(l,"__embed-data__",i),t=new o(l,i,!1),n.replaceWith(t)}).catch(function(t){Object(d.g)(t);var o={error:t,type:"file"===e.loaderData.type?g.a.FILE:g.a.STANDARD,file:e.loaderData.file};n.replaceWith(new g.b(g.b.create(o),o))})}},{key:"resolveDataFromValue",value:function(e){return"data"in e?Promise.resolve(e.data):e.dataPromise}}]),o}(m.a);Object(u.a)(b,"blotName","embed-external"),Object(u.a)(b,"className","embed-external"),Object(u.a)(b,"tagName","div"),Object(u.a)(b,"LOADING_VALUE",{loading:!0})}).call(this,o(251).setImmediate)},function(e,t){var o=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'],n=o.join(","),r="undefined"==typeof Element?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector;function i(e,t){t=t||{};var o,i,l,u=[],p=[],h=new f(e.ownerDocument||e),d=e.querySelectorAll(n);for(t.includeContainer&&r.call(e,n)&&(d=Array.prototype.slice.apply(d)).unshift(e),o=0;o<d.length;o++)a(i=d[o],h)&&(0===(l=s(i))?u.push(i):p.push({documentOrder:o,tabIndex:l,node:i}));return p.sort(c).map(function(e){return e.node}).concat(u)}function a(e,t){return!(!l(e,t)||function(e){return function(e){return p(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t=function(e){for(var t=0;t<e.length;t++)if(e[t].checked)return e[t]}(e.ownerDocument.querySelectorAll('input[type="radio"][name="'+e.name+'"]'));return!t||t===e}(e)}(e)||s(e)<0)}function l(e,t){return t=t||new f(e.ownerDocument||e),!(e.disabled||function(e){return p(e)&&"hidden"===e.type}(e)||t.isUntouchable(e))}i.isTabbable=function(e,t){if(!e)throw new Error("No node provided");return!1!==r.call(e,n)&&a(e,t)},i.isFocusable=function(e,t){if(!e)throw new Error("No node provided");return!1!==r.call(e,u)&&l(e,t)};var u=o.concat("iframe").join(",");function s(e){var t=parseInt(e.getAttribute("tabindex"),10);return isNaN(t)?function(e){return"true"===e.contentEditable}(e)?0:e.tabIndex:t}function c(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex}function p(e){return"INPUT"===e.tagName}function f(e){this.doc=e,this.cache=[]}f.prototype.hasDisplayNone=function(e,t){if(e===this.doc.documentElement)return!1;var o=function(e,t){for(var o=0,n=e.length;o<n;o++)if(t(e[o]))return e[o]}(this.cache,function(t){return t===e});if(o)return o[1];var n=!1;return"none"===(t=t||this.doc.defaultView.getComputedStyle(e)).display?n=!0:e.parentNode&&(n=this.hasDisplayNone(e.parentNode)),this.cache.push([e,n]),n},f.prototype.isUntouchable=function(e){if(e===this.doc.documentElement)return!1;var t=this.doc.defaultView.getComputedStyle(e);return!!this.hasDisplayNone(e,t)||"hidden"===t.visibility},e.exports=i},function(e,t,o){var n=o(688);e.exports=function(e,t,o){var r=null==e?void 0:n(e,t);return void 0===r?o:r}},function(e,t,o){var n=o(689),r=o(694);e.exports=function(e,t){for(var o=0,i=(t=n(t,e)).length;null!=e&&o<i;)e=e[r(t[o++])];return o&&o==i?e:void 0}},function(e,t,o){var n=o(248),r=o(690),i=o(691),a=o(255);e.exports=function(e,t){return n(e)?e:r(e,t)?[e]:i(a(e))}},function(e,t,o){var n=o(248),r=o(94),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var o=typeof e;return!("number"!=o&&"symbol"!=o&&"boolean"!=o&&null!=e&&!r(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},function(e,t,o){var n=o(692),r=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(r,function(e,o,n,r){t.push(n?r.replace(i,"$1"):o||e)}),t});e.exports=a},function(e,t,o){var n=o(693),r=500;e.exports=function(e){var t=n(e,function(e){return o.size===r&&o.clear(),e}),o=t.cache;return t}},function(e,t,o){var n=o(552),r="Expected a function";function i(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError(r);var o=function(){var n=arguments,r=t?t.apply(this,n):n[0],i=o.cache;if(i.has(r))return i.get(r);var a=e.apply(this,n);return o.cache=i.set(r,a)||i,a};return o.cache=new(i.Cache||n),o}i.Cache=n,e.exports=i},function(e,t,o){var n=o(94),r=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}},function(e,t,o){"use strict";(function(e){o.d(t,"a",function(){return j});var n=o(3),r=o(8),i=o(1),a=o(6),l=o(9),u=o(10),s=o(2),c=o(237),p=o(14),f=o.n(p),h=o(45),d=o(65),m=o(87),g=o(540),v=o(93),j=function(t){function o(e,t){var a;return Object(n.a)(this,o),a=Object(r.a)(this,Object(i.a)(o).call(this,e,t)),Object(s.a)(Object(u.a)(Object(u.a)(a)),"linkMatcher",function(e,t){var n=e.textContent;if(e.nodeType===Node.TEXT_NODE&&null!=n&&!a.inCodeFormat){var r=o.splitLinkOperationsOutOfText(n);r&&(t.ops=r)}return t}),a.addMatcher(Node.TEXT_NODE,a.linkMatcher),a}return Object(l.a)(o,t),Object(a.a)(o,null,[{key:"splitLinkOperationsOutOfText",value:function(e){var t=e.match(/https?:\/\/[^\s]+/g);if(t&&t.length>0){var o=[];return t.forEach(function(t){var n=e.split(t),r=n.shift();""!==r&&o.push({insert:r}),o.push({insert:t,attributes:{link:t}}),e=n.join(t)}),""!==e&&o.push({insert:e}),o}return null}}]),Object(a.a)(o,[{key:"onPaste",value:function(t){var o=this;if(!t.defaultPrevented&&this.quill.isEnabled()){var n=this.quill.getSelection(),r=(new f.a).retain(n.index),i=this.options.scrollingContainer,a=document.documentElement.scrollTop||document.body.scrollTop,l=i?i.scrollTop:0;this.container.focus(),this.quill.selection.update(h.a.sources.SILENT),e(function(){r=r.concat(o.convert()).delete(n.length),o.quill.updateContents(r,h.a.sources.USER),o.quill.setSelection(r.length()-n.length,h.a.sources.SILENT),document.documentElement.scrollTop=document.body.scrollTop=a,i&&(i.scrollTop=l),o.quill.focus()})}}},{key:"inCodeFormat",get:function(){var e=Object(v.a)().getState().editor.instances[Object(d.e)(this.quill)];return!(!e||!e.lastGoodSelection)&&(Object(d.m)(this.quill,m.a,e.lastGoodSelection)||Object(d.m)(this.quill,g.a,e.lastGoodSelection))}}]),o}(c.a)}).call(this,o(251).setImmediate)},function(e,t,o){(function(e,n){var r;
/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright JS Foundation and other contributors <https://js.foundation/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */(function(){var i,a=200,l="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",s="__lodash_hash_undefined__",c=500,p="__lodash_placeholder__",f=1,h=2,d=4,m=1,g=2,v=1,j=2,b=4,y=8,_=16,O=32,C=64,S=128,w=256,x=512,k=30,E="...",I=800,L=16,R=1,T=2,M=1/0,A=9007199254740991,z=1.7976931348623157e308,N=NaN,P=**********,D=P-1,F=P>>>1,H=[["ary",S],["bind",v],["bindKey",j],["curry",y],["curryRight",_],["flip",x],["partial",O],["partialRight",C],["rearg",w]],W="[object Arguments]",G="[object Array]",q="[object AsyncFunction]",B="[object Boolean]",U="[object Date]",V="[object DOMException]",K="[object Error]",Z="[object Function]",$="[object GeneratorFunction]",Y="[object Map]",J="[object Number]",X="[object Null]",Q="[object Object]",ee="[object Proxy]",te="[object RegExp]",oe="[object Set]",ne="[object String]",re="[object Symbol]",ie="[object Undefined]",ae="[object WeakMap]",le="[object WeakSet]",ue="[object ArrayBuffer]",se="[object DataView]",ce="[object Float32Array]",pe="[object Float64Array]",fe="[object Int8Array]",he="[object Int16Array]",de="[object Int32Array]",me="[object Uint8Array]",ge="[object Uint8ClampedArray]",ve="[object Uint16Array]",je="[object Uint32Array]",be=/\b__p \+= '';/g,ye=/\b(__p \+=) '' \+/g,_e=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Oe=/&(?:amp|lt|gt|quot|#39);/g,Ce=/[&<>"']/g,Se=RegExp(Oe.source),we=RegExp(Ce.source),xe=/<%-([\s\S]+?)%>/g,ke=/<%([\s\S]+?)%>/g,Ee=/<%=([\s\S]+?)%>/g,Ie=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Le=/^\w*$/,Re=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Te=/[\\^$.*+?()[\]{}|]/g,Me=RegExp(Te.source),Ae=/^\s+|\s+$/g,ze=/^\s+/,Ne=/\s+$/,Pe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,De=/\{\n\/\* \[wrapped with (.+)\] \*/,Fe=/,? & /,He=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,We=/\\(\\)?/g,Ge=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,qe=/\w*$/,Be=/^[-+]0x[0-9a-f]+$/i,Ue=/^0b[01]+$/i,Ve=/^\[object .+?Constructor\]$/,Ke=/^0o[0-7]+$/i,Ze=/^(?:0|[1-9]\d*)$/,$e=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Ye=/($^)/,Je=/['\n\r\u2028\u2029\\]/g,Xe="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Qe="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",et="[\\ud800-\\udfff]",tt="["+Qe+"]",ot="["+Xe+"]",nt="\\d+",rt="[\\u2700-\\u27bf]",it="[a-z\\xdf-\\xf6\\xf8-\\xff]",at="[^\\ud800-\\udfff"+Qe+nt+"\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde]",lt="\\ud83c[\\udffb-\\udfff]",ut="[^\\ud800-\\udfff]",st="(?:\\ud83c[\\udde6-\\uddff]){2}",ct="[\\ud800-\\udbff][\\udc00-\\udfff]",pt="[A-Z\\xc0-\\xd6\\xd8-\\xde]",ft="(?:"+it+"|"+at+")",ht="(?:"+pt+"|"+at+")",dt="(?:"+ot+"|"+lt+")"+"?",mt="[\\ufe0e\\ufe0f]?"+dt+("(?:\\u200d(?:"+[ut,st,ct].join("|")+")[\\ufe0e\\ufe0f]?"+dt+")*"),gt="(?:"+[rt,st,ct].join("|")+")"+mt,vt="(?:"+[ut+ot+"?",ot,st,ct,et].join("|")+")",jt=RegExp("['’]","g"),bt=RegExp(ot,"g"),yt=RegExp(lt+"(?="+lt+")|"+vt+mt,"g"),_t=RegExp([pt+"?"+it+"+(?:['’](?:d|ll|m|re|s|t|ve))?(?="+[tt,pt,"$"].join("|")+")",ht+"+(?:['’](?:D|LL|M|RE|S|T|VE))?(?="+[tt,pt+ft,"$"].join("|")+")",pt+"?"+ft+"+(?:['’](?:d|ll|m|re|s|t|ve))?",pt+"+(?:['’](?:D|LL|M|RE|S|T|VE))?","\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",nt,gt].join("|"),"g"),Ot=RegExp("[\\u200d\\ud800-\\udfff"+Xe+"\\ufe0e\\ufe0f]"),Ct=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,St=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],wt=-1,xt={};xt[ce]=xt[pe]=xt[fe]=xt[he]=xt[de]=xt[me]=xt[ge]=xt[ve]=xt[je]=!0,xt[W]=xt[G]=xt[ue]=xt[B]=xt[se]=xt[U]=xt[K]=xt[Z]=xt[Y]=xt[J]=xt[Q]=xt[te]=xt[oe]=xt[ne]=xt[ae]=!1;var kt={};kt[W]=kt[G]=kt[ue]=kt[se]=kt[B]=kt[U]=kt[ce]=kt[pe]=kt[fe]=kt[he]=kt[de]=kt[Y]=kt[J]=kt[Q]=kt[te]=kt[oe]=kt[ne]=kt[re]=kt[me]=kt[ge]=kt[ve]=kt[je]=!0,kt[K]=kt[Z]=kt[ae]=!1;var Et={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},It=parseFloat,Lt=parseInt,Rt="object"==typeof e&&e&&e.Object===Object&&e,Tt="object"==typeof self&&self&&self.Object===Object&&self,Mt=Rt||Tt||Function("return this")(),At=t&&!t.nodeType&&t,zt=At&&"object"==typeof n&&n&&!n.nodeType&&n,Nt=zt&&zt.exports===At,Pt=Nt&&Rt.process,Dt=function(){try{var e=zt&&zt.require&&zt.require("util").types;return e||Pt&&Pt.binding&&Pt.binding("util")}catch(e){}}(),Ft=Dt&&Dt.isArrayBuffer,Ht=Dt&&Dt.isDate,Wt=Dt&&Dt.isMap,Gt=Dt&&Dt.isRegExp,qt=Dt&&Dt.isSet,Bt=Dt&&Dt.isTypedArray;function Ut(e,t,o){switch(o.length){case 0:return e.call(t);case 1:return e.call(t,o[0]);case 2:return e.call(t,o[0],o[1]);case 3:return e.call(t,o[0],o[1],o[2])}return e.apply(t,o)}function Vt(e,t,o,n){for(var r=-1,i=null==e?0:e.length;++r<i;){var a=e[r];t(n,a,o(a),e)}return n}function Kt(e,t){for(var o=-1,n=null==e?0:e.length;++o<n&&!1!==t(e[o],o,e););return e}function Zt(e,t){for(var o=null==e?0:e.length;o--&&!1!==t(e[o],o,e););return e}function $t(e,t){for(var o=-1,n=null==e?0:e.length;++o<n;)if(!t(e[o],o,e))return!1;return!0}function Yt(e,t){for(var o=-1,n=null==e?0:e.length,r=0,i=[];++o<n;){var a=e[o];t(a,o,e)&&(i[r++]=a)}return i}function Jt(e,t){return!!(null==e?0:e.length)&&lo(e,t,0)>-1}function Xt(e,t,o){for(var n=-1,r=null==e?0:e.length;++n<r;)if(o(t,e[n]))return!0;return!1}function Qt(e,t){for(var o=-1,n=null==e?0:e.length,r=Array(n);++o<n;)r[o]=t(e[o],o,e);return r}function eo(e,t){for(var o=-1,n=t.length,r=e.length;++o<n;)e[r+o]=t[o];return e}function to(e,t,o,n){var r=-1,i=null==e?0:e.length;for(n&&i&&(o=e[++r]);++r<i;)o=t(o,e[r],r,e);return o}function oo(e,t,o,n){var r=null==e?0:e.length;for(n&&r&&(o=e[--r]);r--;)o=t(o,e[r],r,e);return o}function no(e,t){for(var o=-1,n=null==e?0:e.length;++o<n;)if(t(e[o],o,e))return!0;return!1}var ro=po("length");function io(e,t,o){var n;return o(e,function(e,o,r){if(t(e,o,r))return n=o,!1}),n}function ao(e,t,o,n){for(var r=e.length,i=o+(n?1:-1);n?i--:++i<r;)if(t(e[i],i,e))return i;return-1}function lo(e,t,o){return t==t?function(e,t,o){var n=o-1,r=e.length;for(;++n<r;)if(e[n]===t)return n;return-1}(e,t,o):ao(e,so,o)}function uo(e,t,o,n){for(var r=o-1,i=e.length;++r<i;)if(n(e[r],t))return r;return-1}function so(e){return e!=e}function co(e,t){var o=null==e?0:e.length;return o?mo(e,t)/o:N}function po(e){return function(t){return null==t?i:t[e]}}function fo(e){return function(t){return null==e?i:e[t]}}function ho(e,t,o,n,r){return r(e,function(e,r,i){o=n?(n=!1,e):t(o,e,r,i)}),o}function mo(e,t){for(var o,n=-1,r=e.length;++n<r;){var a=t(e[n]);a!==i&&(o=o===i?a:o+a)}return o}function go(e,t){for(var o=-1,n=Array(e);++o<e;)n[o]=t(o);return n}function vo(e){return function(t){return e(t)}}function jo(e,t){return Qt(t,function(t){return e[t]})}function bo(e,t){return e.has(t)}function yo(e,t){for(var o=-1,n=e.length;++o<n&&lo(t,e[o],0)>-1;);return o}function _o(e,t){for(var o=e.length;o--&&lo(t,e[o],0)>-1;);return o}var Oo=fo({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),Co=fo({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function So(e){return"\\"+Et[e]}function wo(e){return Ot.test(e)}function xo(e){var t=-1,o=Array(e.size);return e.forEach(function(e,n){o[++t]=[n,e]}),o}function ko(e,t){return function(o){return e(t(o))}}function Eo(e,t){for(var o=-1,n=e.length,r=0,i=[];++o<n;){var a=e[o];a!==t&&a!==p||(e[o]=p,i[r++]=o)}return i}function Io(e){var t=-1,o=Array(e.size);return e.forEach(function(e){o[++t]=e}),o}function Lo(e){var t=-1,o=Array(e.size);return e.forEach(function(e){o[++t]=[e,e]}),o}function Ro(e){return wo(e)?function(e){var t=yt.lastIndex=0;for(;yt.test(e);)++t;return t}(e):ro(e)}function To(e){return wo(e)?function(e){return e.match(yt)||[]}(e):function(e){return e.split("")}(e)}var Mo=fo({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});var Ao=function e(t){var o,n=(t=null==t?Mt:Ao.defaults(Mt.Object(),t,Ao.pick(Mt,St))).Array,r=t.Date,Xe=t.Error,Qe=t.Function,et=t.Math,tt=t.Object,ot=t.RegExp,nt=t.String,rt=t.TypeError,it=n.prototype,at=Qe.prototype,lt=tt.prototype,ut=t["__core-js_shared__"],st=at.toString,ct=lt.hasOwnProperty,pt=0,ft=(o=/[^.]+$/.exec(ut&&ut.keys&&ut.keys.IE_PROTO||""))?"Symbol(src)_1."+o:"",ht=lt.toString,dt=st.call(tt),mt=Mt._,gt=ot("^"+st.call(ct).replace(Te,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),vt=Nt?t.Buffer:i,yt=t.Symbol,Ot=t.Uint8Array,Et=vt?vt.allocUnsafe:i,Rt=ko(tt.getPrototypeOf,tt),Tt=tt.create,At=lt.propertyIsEnumerable,zt=it.splice,Pt=yt?yt.isConcatSpreadable:i,Dt=yt?yt.iterator:i,ro=yt?yt.toStringTag:i,fo=function(){try{var e=Fi(tt,"defineProperty");return e({},"",{}),e}catch(e){}}(),zo=t.clearTimeout!==Mt.clearTimeout&&t.clearTimeout,No=r&&r.now!==Mt.Date.now&&r.now,Po=t.setTimeout!==Mt.setTimeout&&t.setTimeout,Do=et.ceil,Fo=et.floor,Ho=tt.getOwnPropertySymbols,Wo=vt?vt.isBuffer:i,Go=t.isFinite,qo=it.join,Bo=ko(tt.keys,tt),Uo=et.max,Vo=et.min,Ko=r.now,Zo=t.parseInt,$o=et.random,Yo=it.reverse,Jo=Fi(t,"DataView"),Xo=Fi(t,"Map"),Qo=Fi(t,"Promise"),en=Fi(t,"Set"),tn=Fi(t,"WeakMap"),on=Fi(tt,"create"),nn=tn&&new tn,rn={},an=pa(Jo),ln=pa(Xo),un=pa(Qo),sn=pa(en),cn=pa(tn),pn=yt?yt.prototype:i,fn=pn?pn.valueOf:i,hn=pn?pn.toString:i;function dn(e){if(El(e)&&!vl(e)&&!(e instanceof jn)){if(e instanceof vn)return e;if(ct.call(e,"__wrapped__"))return fa(e)}return new vn(e)}var mn=function(){function e(){}return function(t){if(!kl(t))return{};if(Tt)return Tt(t);e.prototype=t;var o=new e;return e.prototype=i,o}}();function gn(){}function vn(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=i}function jn(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=P,this.__views__=[]}function bn(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}function yn(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}function _n(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}function On(e){var t=-1,o=null==e?0:e.length;for(this.__data__=new _n;++t<o;)this.add(e[t])}function Cn(e){var t=this.__data__=new yn(e);this.size=t.size}function Sn(e,t){var o=vl(e),n=!o&&gl(e),r=!o&&!n&&_l(e),i=!o&&!n&&!r&&Nl(e),a=o||n||r||i,l=a?go(e.length,nt):[],u=l.length;for(var s in e)!t&&!ct.call(e,s)||a&&("length"==s||r&&("offset"==s||"parent"==s)||i&&("buffer"==s||"byteLength"==s||"byteOffset"==s)||Vi(s,u))||l.push(s);return l}function wn(e){var t=e.length;return t?e[Or(0,t-1)]:i}function xn(e,t){return ua(ni(e),zn(t,0,e.length))}function kn(e){return ua(ni(e))}function En(e,t,o){(o===i||hl(e[t],o))&&(o!==i||t in e)||Mn(e,t,o)}function In(e,t,o){var n=e[t];ct.call(e,t)&&hl(n,o)&&(o!==i||t in e)||Mn(e,t,o)}function Ln(e,t){for(var o=e.length;o--;)if(hl(e[o][0],t))return o;return-1}function Rn(e,t,o,n){return Hn(e,function(e,r,i){t(n,e,o(e),i)}),n}function Tn(e,t){return e&&ri(t,ru(t),e)}function Mn(e,t,o){"__proto__"==t&&fo?fo(e,t,{configurable:!0,enumerable:!0,value:o,writable:!0}):e[t]=o}function An(e,t){for(var o=-1,r=t.length,a=n(r),l=null==e;++o<r;)a[o]=l?i:Ql(e,t[o]);return a}function zn(e,t,o){return e==e&&(o!==i&&(e=e<=o?e:o),t!==i&&(e=e>=t?e:t)),e}function Nn(e,t,o,n,r,a){var l,u=t&f,s=t&h,c=t&d;if(o&&(l=r?o(e,n,r,a):o(e)),l!==i)return l;if(!kl(e))return e;var p=vl(e);if(p){if(l=function(e){var t=e.length,o=new e.constructor(t);return t&&"string"==typeof e[0]&&ct.call(e,"index")&&(o.index=e.index,o.input=e.input),o}(e),!u)return ni(e,l)}else{var m=Gi(e),g=m==Z||m==$;if(_l(e))return Jr(e,u);if(m==Q||m==W||g&&!r){if(l=s||g?{}:Bi(e),!u)return s?function(e,t){return ri(e,Wi(e),t)}(e,function(e,t){return e&&ri(t,iu(t),e)}(l,e)):function(e,t){return ri(e,Hi(e),t)}(e,Tn(l,e))}else{if(!kt[m])return r?e:{};l=function(e,t,o){var n,r,i,a=e.constructor;switch(t){case ue:return Xr(e);case B:case U:return new a(+e);case se:return function(e,t){var o=t?Xr(e.buffer):e.buffer;return new e.constructor(o,e.byteOffset,e.byteLength)}(e,o);case ce:case pe:case fe:case he:case de:case me:case ge:case ve:case je:return Qr(e,o);case Y:return new a;case J:case ne:return new a(e);case te:return(i=new(r=e).constructor(r.source,qe.exec(r))).lastIndex=r.lastIndex,i;case oe:return new a;case re:return n=e,fn?tt(fn.call(n)):{}}}(e,m,u)}}a||(a=new Cn);var v=a.get(e);if(v)return v;if(a.set(e,l),Ml(e))return e.forEach(function(n){l.add(Nn(n,t,o,n,e,a))}),l;if(Il(e))return e.forEach(function(n,r){l.set(r,Nn(n,t,o,r,e,a))}),l;var j=p?i:(c?s?Ti:Ri:s?iu:ru)(e);return Kt(j||e,function(n,r){j&&(n=e[r=n]),In(l,r,Nn(n,t,o,r,e,a))}),l}function Pn(e,t,o){var n=o.length;if(null==e)return!n;for(e=tt(e);n--;){var r=o[n],a=t[r],l=e[r];if(l===i&&!(r in e)||!a(l))return!1}return!0}function Dn(e,t,o){if("function"!=typeof e)throw new rt(u);return ra(function(){e.apply(i,o)},t)}function Fn(e,t,o,n){var r=-1,i=Jt,l=!0,u=e.length,s=[],c=t.length;if(!u)return s;o&&(t=Qt(t,vo(o))),n?(i=Xt,l=!1):t.length>=a&&(i=bo,l=!1,t=new On(t));e:for(;++r<u;){var p=e[r],f=null==o?p:o(p);if(p=n||0!==p?p:0,l&&f==f){for(var h=c;h--;)if(t[h]===f)continue e;s.push(p)}else i(t,f,n)||s.push(p)}return s}dn.templateSettings={escape:xe,evaluate:ke,interpolate:Ee,variable:"",imports:{_:dn}},dn.prototype=gn.prototype,dn.prototype.constructor=dn,vn.prototype=mn(gn.prototype),vn.prototype.constructor=vn,jn.prototype=mn(gn.prototype),jn.prototype.constructor=jn,bn.prototype.clear=function(){this.__data__=on?on(null):{},this.size=0},bn.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},bn.prototype.get=function(e){var t=this.__data__;if(on){var o=t[e];return o===s?i:o}return ct.call(t,e)?t[e]:i},bn.prototype.has=function(e){var t=this.__data__;return on?t[e]!==i:ct.call(t,e)},bn.prototype.set=function(e,t){var o=this.__data__;return this.size+=this.has(e)?0:1,o[e]=on&&t===i?s:t,this},yn.prototype.clear=function(){this.__data__=[],this.size=0},yn.prototype.delete=function(e){var t=this.__data__,o=Ln(t,e);return!(o<0||(o==t.length-1?t.pop():zt.call(t,o,1),--this.size,0))},yn.prototype.get=function(e){var t=this.__data__,o=Ln(t,e);return o<0?i:t[o][1]},yn.prototype.has=function(e){return Ln(this.__data__,e)>-1},yn.prototype.set=function(e,t){var o=this.__data__,n=Ln(o,e);return n<0?(++this.size,o.push([e,t])):o[n][1]=t,this},_n.prototype.clear=function(){this.size=0,this.__data__={hash:new bn,map:new(Xo||yn),string:new bn}},_n.prototype.delete=function(e){var t=Pi(this,e).delete(e);return this.size-=t?1:0,t},_n.prototype.get=function(e){return Pi(this,e).get(e)},_n.prototype.has=function(e){return Pi(this,e).has(e)},_n.prototype.set=function(e,t){var o=Pi(this,e),n=o.size;return o.set(e,t),this.size+=o.size==n?0:1,this},On.prototype.add=On.prototype.push=function(e){return this.__data__.set(e,s),this},On.prototype.has=function(e){return this.__data__.has(e)},Cn.prototype.clear=function(){this.__data__=new yn,this.size=0},Cn.prototype.delete=function(e){var t=this.__data__,o=t.delete(e);return this.size=t.size,o},Cn.prototype.get=function(e){return this.__data__.get(e)},Cn.prototype.has=function(e){return this.__data__.has(e)},Cn.prototype.set=function(e,t){var o=this.__data__;if(o instanceof yn){var n=o.__data__;if(!Xo||n.length<a-1)return n.push([e,t]),this.size=++o.size,this;o=this.__data__=new _n(n)}return o.set(e,t),this.size=o.size,this};var Hn=li(Zn),Wn=li($n,!0);function Gn(e,t){var o=!0;return Hn(e,function(e,n,r){return o=!!t(e,n,r)}),o}function qn(e,t,o){for(var n=-1,r=e.length;++n<r;){var a=e[n],l=t(a);if(null!=l&&(u===i?l==l&&!zl(l):o(l,u)))var u=l,s=a}return s}function Bn(e,t){var o=[];return Hn(e,function(e,n,r){t(e,n,r)&&o.push(e)}),o}function Un(e,t,o,n,r){var i=-1,a=e.length;for(o||(o=Ui),r||(r=[]);++i<a;){var l=e[i];t>0&&o(l)?t>1?Un(l,t-1,o,n,r):eo(r,l):n||(r[r.length]=l)}return r}var Vn=ui(),Kn=ui(!0);function Zn(e,t){return e&&Vn(e,t,ru)}function $n(e,t){return e&&Kn(e,t,ru)}function Yn(e,t){return Yt(t,function(t){return Sl(e[t])})}function Jn(e,t){for(var o=0,n=(t=Kr(t,e)).length;null!=e&&o<n;)e=e[ca(t[o++])];return o&&o==n?e:i}function Xn(e,t,o){var n=t(e);return vl(e)?n:eo(n,o(e))}function Qn(e){return null==e?e===i?ie:X:ro&&ro in tt(e)?function(e){var t=ct.call(e,ro),o=e[ro];try{e[ro]=i;var n=!0}catch(e){}var r=ht.call(e);return n&&(t?e[ro]=o:delete e[ro]),r}(e):function(e){return ht.call(e)}(e)}function er(e,t){return e>t}function tr(e,t){return null!=e&&ct.call(e,t)}function or(e,t){return null!=e&&t in tt(e)}function nr(e,t,o){for(var r=o?Xt:Jt,a=e[0].length,l=e.length,u=l,s=n(l),c=1/0,p=[];u--;){var f=e[u];u&&t&&(f=Qt(f,vo(t))),c=Vo(f.length,c),s[u]=!o&&(t||a>=120&&f.length>=120)?new On(u&&f):i}f=e[0];var h=-1,d=s[0];e:for(;++h<a&&p.length<c;){var m=f[h],g=t?t(m):m;if(m=o||0!==m?m:0,!(d?bo(d,g):r(p,g,o))){for(u=l;--u;){var v=s[u];if(!(v?bo(v,g):r(e[u],g,o)))continue e}d&&d.push(g),p.push(m)}}return p}function rr(e,t,o){var n=null==(e=ta(e,t=Kr(t,e)))?e:e[ca(Ca(t))];return null==n?i:Ut(n,e,o)}function ir(e){return El(e)&&Qn(e)==W}function ar(e,t,o,n,r){return e===t||(null==e||null==t||!El(e)&&!El(t)?e!=e&&t!=t:function(e,t,o,n,r,a){var l=vl(e),u=vl(t),s=l?G:Gi(e),c=u?G:Gi(t),p=(s=s==W?Q:s)==Q,f=(c=c==W?Q:c)==Q,h=s==c;if(h&&_l(e)){if(!_l(t))return!1;l=!0,p=!1}if(h&&!p)return a||(a=new Cn),l||Nl(e)?Ii(e,t,o,n,r,a):function(e,t,o,n,r,i,a){switch(o){case se:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case ue:return!(e.byteLength!=t.byteLength||!i(new Ot(e),new Ot(t)));case B:case U:case J:return hl(+e,+t);case K:return e.name==t.name&&e.message==t.message;case te:case ne:return e==t+"";case Y:var l=xo;case oe:var u=n&m;if(l||(l=Io),e.size!=t.size&&!u)return!1;var s=a.get(e);if(s)return s==t;n|=g,a.set(e,t);var c=Ii(l(e),l(t),n,r,i,a);return a.delete(e),c;case re:if(fn)return fn.call(e)==fn.call(t)}return!1}(e,t,s,o,n,r,a);if(!(o&m)){var d=p&&ct.call(e,"__wrapped__"),v=f&&ct.call(t,"__wrapped__");if(d||v){var j=d?e.value():e,b=v?t.value():t;return a||(a=new Cn),r(j,b,o,n,a)}}return!!h&&(a||(a=new Cn),function(e,t,o,n,r,a){var l=o&m,u=Ri(e),s=u.length,c=Ri(t).length;if(s!=c&&!l)return!1;for(var p=s;p--;){var f=u[p];if(!(l?f in t:ct.call(t,f)))return!1}var h=a.get(e);if(h&&a.get(t))return h==t;var d=!0;a.set(e,t),a.set(t,e);for(var g=l;++p<s;){f=u[p];var v=e[f],j=t[f];if(n)var b=l?n(j,v,f,t,e,a):n(v,j,f,e,t,a);if(!(b===i?v===j||r(v,j,o,n,a):b)){d=!1;break}g||(g="constructor"==f)}if(d&&!g){var y=e.constructor,_=t.constructor;y!=_&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof _&&_ instanceof _)&&(d=!1)}return a.delete(e),a.delete(t),d}(e,t,o,n,r,a))}(e,t,o,n,ar,r))}function lr(e,t,o,n){var r=o.length,a=r,l=!n;if(null==e)return!a;for(e=tt(e);r--;){var u=o[r];if(l&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++r<a;){var s=(u=o[r])[0],c=e[s],p=u[1];if(l&&u[2]){if(c===i&&!(s in e))return!1}else{var f=new Cn;if(n)var h=n(c,p,s,e,t,f);if(!(h===i?ar(p,c,m|g,n,f):h))return!1}}return!0}function ur(e){return!(!kl(e)||(t=e,ft&&ft in t))&&(Sl(e)?gt:Ve).test(pa(e));var t}function sr(e){return"function"==typeof e?e:null==e?Lu:"object"==typeof e?vl(e)?mr(e[0],e[1]):dr(e):Fu(e)}function cr(e){if(!Ji(e))return Bo(e);var t=[];for(var o in tt(e))ct.call(e,o)&&"constructor"!=o&&t.push(o);return t}function pr(e){if(!kl(e))return function(e){var t=[];if(null!=e)for(var o in tt(e))t.push(o);return t}(e);var t=Ji(e),o=[];for(var n in e)("constructor"!=n||!t&&ct.call(e,n))&&o.push(n);return o}function fr(e,t){return e<t}function hr(e,t){var o=-1,r=bl(e)?n(e.length):[];return Hn(e,function(e,n,i){r[++o]=t(e,n,i)}),r}function dr(e){var t=Di(e);return 1==t.length&&t[0][2]?Qi(t[0][0],t[0][1]):function(o){return o===e||lr(o,e,t)}}function mr(e,t){return Zi(e)&&Xi(t)?Qi(ca(e),t):function(o){var n=Ql(o,e);return n===i&&n===t?eu(o,e):ar(t,n,m|g)}}function gr(e,t,o,n,r){e!==t&&Vn(t,function(a,l){if(kl(a))r||(r=new Cn),function(e,t,o,n,r,a,l){var u=oa(e,o),s=oa(t,o),c=l.get(s);if(c)En(e,o,c);else{var p=a?a(u,s,o+"",e,t,l):i,f=p===i;if(f){var h=vl(s),d=!h&&_l(s),m=!h&&!d&&Nl(s);p=s,h||d||m?vl(u)?p=u:yl(u)?p=ni(u):d?(f=!1,p=Jr(s,!0)):m?(f=!1,p=Qr(s,!0)):p=[]:Rl(s)||gl(s)?(p=u,gl(u)?p=Bl(u):kl(u)&&!Sl(u)||(p=Bi(s))):f=!1}f&&(l.set(s,p),r(p,s,n,a,l),l.delete(s)),En(e,o,p)}}(e,t,l,o,gr,n,r);else{var u=n?n(oa(e,l),a,l+"",e,t,r):i;u===i&&(u=a),En(e,l,u)}},iu)}function vr(e,t){var o=e.length;if(o)return Vi(t+=t<0?o:0,o)?e[t]:i}function jr(e,t,o){var n=-1;return t=Qt(t.length?t:[Lu],vo(Ni())),function(e,t){var o=e.length;for(e.sort(t);o--;)e[o]=e[o].value;return e}(hr(e,function(e,o,r){return{criteria:Qt(t,function(t){return t(e)}),index:++n,value:e}}),function(e,t){return function(e,t,o){for(var n=-1,r=e.criteria,i=t.criteria,a=r.length,l=o.length;++n<a;){var u=ei(r[n],i[n]);if(u){if(n>=l)return u;var s=o[n];return u*("desc"==s?-1:1)}}return e.index-t.index}(e,t,o)})}function br(e,t,o){for(var n=-1,r=t.length,i={};++n<r;){var a=t[n],l=Jn(e,a);o(l,a)&&kr(i,Kr(a,e),l)}return i}function yr(e,t,o,n){var r=n?uo:lo,i=-1,a=t.length,l=e;for(e===t&&(t=ni(t)),o&&(l=Qt(e,vo(o)));++i<a;)for(var u=0,s=t[i],c=o?o(s):s;(u=r(l,c,u,n))>-1;)l!==e&&zt.call(l,u,1),zt.call(e,u,1);return e}function _r(e,t){for(var o=e?t.length:0,n=o-1;o--;){var r=t[o];if(o==n||r!==i){var i=r;Vi(r)?zt.call(e,r,1):Fr(e,r)}}return e}function Or(e,t){return e+Fo($o()*(t-e+1))}function Cr(e,t){var o="";if(!e||t<1||t>A)return o;do{t%2&&(o+=e),(t=Fo(t/2))&&(e+=e)}while(t);return o}function Sr(e,t){return ia(ea(e,t,Lu),e+"")}function wr(e){return wn(hu(e))}function xr(e,t){var o=hu(e);return ua(o,zn(t,0,o.length))}function kr(e,t,o,n){if(!kl(e))return e;for(var r=-1,a=(t=Kr(t,e)).length,l=a-1,u=e;null!=u&&++r<a;){var s=ca(t[r]),c=o;if(r!=l){var p=u[s];(c=n?n(p,s,u):i)===i&&(c=kl(p)?p:Vi(t[r+1])?[]:{})}In(u,s,c),u=u[s]}return e}var Er=nn?function(e,t){return nn.set(e,t),e}:Lu,Ir=fo?function(e,t){return fo(e,"toString",{configurable:!0,enumerable:!1,value:ku(t),writable:!0})}:Lu;function Lr(e){return ua(hu(e))}function Rr(e,t,o){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(o=o>i?i:o)<0&&(o+=i),i=t>o?0:o-t>>>0,t>>>=0;for(var a=n(i);++r<i;)a[r]=e[r+t];return a}function Tr(e,t){var o;return Hn(e,function(e,n,r){return!(o=t(e,n,r))}),!!o}function Mr(e,t,o){var n=0,r=null==e?n:e.length;if("number"==typeof t&&t==t&&r<=F){for(;n<r;){var i=n+r>>>1,a=e[i];null!==a&&!zl(a)&&(o?a<=t:a<t)?n=i+1:r=i}return r}return Ar(e,t,Lu,o)}function Ar(e,t,o,n){t=o(t);for(var r=0,a=null==e?0:e.length,l=t!=t,u=null===t,s=zl(t),c=t===i;r<a;){var p=Fo((r+a)/2),f=o(e[p]),h=f!==i,d=null===f,m=f==f,g=zl(f);if(l)var v=n||m;else v=c?m&&(n||h):u?m&&h&&(n||!d):s?m&&h&&!d&&(n||!g):!d&&!g&&(n?f<=t:f<t);v?r=p+1:a=p}return Vo(a,D)}function zr(e,t){for(var o=-1,n=e.length,r=0,i=[];++o<n;){var a=e[o],l=t?t(a):a;if(!o||!hl(l,u)){var u=l;i[r++]=0===a?0:a}}return i}function Nr(e){return"number"==typeof e?e:zl(e)?N:+e}function Pr(e){if("string"==typeof e)return e;if(vl(e))return Qt(e,Pr)+"";if(zl(e))return hn?hn.call(e):"";var t=e+"";return"0"==t&&1/e==-M?"-0":t}function Dr(e,t,o){var n=-1,r=Jt,i=e.length,l=!0,u=[],s=u;if(o)l=!1,r=Xt;else if(i>=a){var c=t?null:Ci(e);if(c)return Io(c);l=!1,r=bo,s=new On}else s=t?[]:u;e:for(;++n<i;){var p=e[n],f=t?t(p):p;if(p=o||0!==p?p:0,l&&f==f){for(var h=s.length;h--;)if(s[h]===f)continue e;t&&s.push(f),u.push(p)}else r(s,f,o)||(s!==u&&s.push(f),u.push(p))}return u}function Fr(e,t){return null==(e=ta(e,t=Kr(t,e)))||delete e[ca(Ca(t))]}function Hr(e,t,o,n){return kr(e,t,o(Jn(e,t)),n)}function Wr(e,t,o,n){for(var r=e.length,i=n?r:-1;(n?i--:++i<r)&&t(e[i],i,e););return o?Rr(e,n?0:i,n?i+1:r):Rr(e,n?i+1:0,n?r:i)}function Gr(e,t){var o=e;return o instanceof jn&&(o=o.value()),to(t,function(e,t){return t.func.apply(t.thisArg,eo([e],t.args))},o)}function qr(e,t,o){var r=e.length;if(r<2)return r?Dr(e[0]):[];for(var i=-1,a=n(r);++i<r;)for(var l=e[i],u=-1;++u<r;)u!=i&&(a[i]=Fn(a[i]||l,e[u],t,o));return Dr(Un(a,1),t,o)}function Br(e,t,o){for(var n=-1,r=e.length,a=t.length,l={};++n<r;){var u=n<a?t[n]:i;o(l,e[n],u)}return l}function Ur(e){return yl(e)?e:[]}function Vr(e){return"function"==typeof e?e:Lu}function Kr(e,t){return vl(e)?e:Zi(e,t)?[e]:sa(Ul(e))}var Zr=Sr;function $r(e,t,o){var n=e.length;return o=o===i?n:o,!t&&o>=n?e:Rr(e,t,o)}var Yr=zo||function(e){return Mt.clearTimeout(e)};function Jr(e,t){if(t)return e.slice();var o=e.length,n=Et?Et(o):new e.constructor(o);return e.copy(n),n}function Xr(e){var t=new e.constructor(e.byteLength);return new Ot(t).set(new Ot(e)),t}function Qr(e,t){var o=t?Xr(e.buffer):e.buffer;return new e.constructor(o,e.byteOffset,e.length)}function ei(e,t){if(e!==t){var o=e!==i,n=null===e,r=e==e,a=zl(e),l=t!==i,u=null===t,s=t==t,c=zl(t);if(!u&&!c&&!a&&e>t||a&&l&&s&&!u&&!c||n&&l&&s||!o&&s||!r)return 1;if(!n&&!a&&!c&&e<t||c&&o&&r&&!n&&!a||u&&o&&r||!l&&r||!s)return-1}return 0}function ti(e,t,o,r){for(var i=-1,a=e.length,l=o.length,u=-1,s=t.length,c=Uo(a-l,0),p=n(s+c),f=!r;++u<s;)p[u]=t[u];for(;++i<l;)(f||i<a)&&(p[o[i]]=e[i]);for(;c--;)p[u++]=e[i++];return p}function oi(e,t,o,r){for(var i=-1,a=e.length,l=-1,u=o.length,s=-1,c=t.length,p=Uo(a-u,0),f=n(p+c),h=!r;++i<p;)f[i]=e[i];for(var d=i;++s<c;)f[d+s]=t[s];for(;++l<u;)(h||i<a)&&(f[d+o[l]]=e[i++]);return f}function ni(e,t){var o=-1,r=e.length;for(t||(t=n(r));++o<r;)t[o]=e[o];return t}function ri(e,t,o,n){var r=!o;o||(o={});for(var a=-1,l=t.length;++a<l;){var u=t[a],s=n?n(o[u],e[u],u,o,e):i;s===i&&(s=e[u]),r?Mn(o,u,s):In(o,u,s)}return o}function ii(e,t){return function(o,n){var r=vl(o)?Vt:Rn,i=t?t():{};return r(o,e,Ni(n,2),i)}}function ai(e){return Sr(function(t,o){var n=-1,r=o.length,a=r>1?o[r-1]:i,l=r>2?o[2]:i;for(a=e.length>3&&"function"==typeof a?(r--,a):i,l&&Ki(o[0],o[1],l)&&(a=r<3?i:a,r=1),t=tt(t);++n<r;){var u=o[n];u&&e(t,u,n,a)}return t})}function li(e,t){return function(o,n){if(null==o)return o;if(!bl(o))return e(o,n);for(var r=o.length,i=t?r:-1,a=tt(o);(t?i--:++i<r)&&!1!==n(a[i],i,a););return o}}function ui(e){return function(t,o,n){for(var r=-1,i=tt(t),a=n(t),l=a.length;l--;){var u=a[e?l:++r];if(!1===o(i[u],u,i))break}return t}}function si(e){return function(t){var o=wo(t=Ul(t))?To(t):i,n=o?o[0]:t.charAt(0),r=o?$r(o,1).join(""):t.slice(1);return n[e]()+r}}function ci(e){return function(t){return to(Su(gu(t).replace(jt,"")),e,"")}}function pi(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var o=mn(e.prototype),n=e.apply(o,t);return kl(n)?n:o}}function fi(e){return function(t,o,n){var r=tt(t);if(!bl(t)){var a=Ni(o,3);t=ru(t),o=function(e){return a(r[e],e,r)}}var l=e(t,o,n);return l>-1?r[a?t[l]:l]:i}}function hi(e){return Li(function(t){var o=t.length,n=o,r=vn.prototype.thru;for(e&&t.reverse();n--;){var a=t[n];if("function"!=typeof a)throw new rt(u);if(r&&!l&&"wrapper"==Ai(a))var l=new vn([],!0)}for(n=l?n:o;++n<o;){var s=Ai(a=t[n]),c="wrapper"==s?Mi(a):i;l=c&&$i(c[0])&&c[1]==(S|y|O|w)&&!c[4].length&&1==c[9]?l[Ai(c[0])].apply(l,c[3]):1==a.length&&$i(a)?l[s]():l.thru(a)}return function(){var e=arguments,n=e[0];if(l&&1==e.length&&vl(n))return l.plant(n).value();for(var r=0,i=o?t[r].apply(this,e):n;++r<o;)i=t[r].call(this,i);return i}})}function di(e,t,o,r,a,l,u,s,c,p){var f=t&S,h=t&v,d=t&j,m=t&(y|_),g=t&x,b=d?i:pi(e);return function v(){for(var j=arguments.length,y=n(j),_=j;_--;)y[_]=arguments[_];if(m)var O=zi(v),C=function(e,t){for(var o=e.length,n=0;o--;)e[o]===t&&++n;return n}(y,O);if(r&&(y=ti(y,r,a,m)),l&&(y=oi(y,l,u,m)),j-=C,m&&j<p){var S=Eo(y,O);return _i(e,t,di,v.placeholder,o,y,S,s,c,p-j)}var w=h?o:this,x=d?w[e]:e;return j=y.length,s?y=function(e,t){for(var o=e.length,n=Vo(t.length,o),r=ni(e);n--;){var a=t[n];e[n]=Vi(a,o)?r[a]:i}return e}(y,s):g&&j>1&&y.reverse(),f&&c<j&&(y.length=c),this&&this!==Mt&&this instanceof v&&(x=b||pi(x)),x.apply(w,y)}}function mi(e,t){return function(o,n){return function(e,t,o,n){return Zn(e,function(e,r,i){t(n,o(e),r,i)}),n}(o,e,t(n),{})}}function gi(e,t){return function(o,n){var r;if(o===i&&n===i)return t;if(o!==i&&(r=o),n!==i){if(r===i)return n;"string"==typeof o||"string"==typeof n?(o=Pr(o),n=Pr(n)):(o=Nr(o),n=Nr(n)),r=e(o,n)}return r}}function vi(e){return Li(function(t){return t=Qt(t,vo(Ni())),Sr(function(o){var n=this;return e(t,function(e){return Ut(e,n,o)})})})}function ji(e,t){var o=(t=t===i?" ":Pr(t)).length;if(o<2)return o?Cr(t,e):t;var n=Cr(t,Do(e/Ro(t)));return wo(t)?$r(To(n),0,e).join(""):n.slice(0,e)}function bi(e){return function(t,o,r){return r&&"number"!=typeof r&&Ki(t,o,r)&&(o=r=i),t=Hl(t),o===i?(o=t,t=0):o=Hl(o),function(e,t,o,r){for(var i=-1,a=Uo(Do((t-e)/(o||1)),0),l=n(a);a--;)l[r?a:++i]=e,e+=o;return l}(t,o,r=r===i?t<o?1:-1:Hl(r),e)}}function yi(e){return function(t,o){return"string"==typeof t&&"string"==typeof o||(t=ql(t),o=ql(o)),e(t,o)}}function _i(e,t,o,n,r,a,l,u,s,c){var p=t&y;t|=p?O:C,(t&=~(p?C:O))&b||(t&=~(v|j));var f=[e,t,r,p?a:i,p?l:i,p?i:a,p?i:l,u,s,c],h=o.apply(i,f);return $i(e)&&na(h,f),h.placeholder=n,aa(h,e,t)}function Oi(e){var t=et[e];return function(e,o){if(e=ql(e),o=null==o?0:Vo(Wl(o),292)){var n=(Ul(e)+"e").split("e");return+((n=(Ul(t(n[0]+"e"+(+n[1]+o)))+"e").split("e"))[0]+"e"+(+n[1]-o))}return t(e)}}var Ci=en&&1/Io(new en([,-0]))[1]==M?function(e){return new en(e)}:zu;function Si(e){return function(t){var o=Gi(t);return o==Y?xo(t):o==oe?Lo(t):function(e,t){return Qt(t,function(t){return[t,e[t]]})}(t,e(t))}}function wi(e,t,o,r,a,l,s,c){var f=t&j;if(!f&&"function"!=typeof e)throw new rt(u);var h=r?r.length:0;if(h||(t&=~(O|C),r=a=i),s=s===i?s:Uo(Wl(s),0),c=c===i?c:Wl(c),h-=a?a.length:0,t&C){var d=r,m=a;r=a=i}var g=f?i:Mi(e),x=[e,t,o,r,a,d,m,l,s,c];if(g&&function(e,t){var o=e[1],n=t[1],r=o|n,i=r<(v|j|S),a=n==S&&o==y||n==S&&o==w&&e[7].length<=t[8]||n==(S|w)&&t[7].length<=t[8]&&o==y;if(!i&&!a)return e;n&v&&(e[2]=t[2],r|=o&v?0:b);var l=t[3];if(l){var u=e[3];e[3]=u?ti(u,l,t[4]):l,e[4]=u?Eo(e[3],p):t[4]}(l=t[5])&&(u=e[5],e[5]=u?oi(u,l,t[6]):l,e[6]=u?Eo(e[5],p):t[6]),(l=t[7])&&(e[7]=l),n&S&&(e[8]=null==e[8]?t[8]:Vo(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=r}(x,g),e=x[0],t=x[1],o=x[2],r=x[3],a=x[4],!(c=x[9]=x[9]===i?f?0:e.length:Uo(x[9]-h,0))&&t&(y|_)&&(t&=~(y|_)),t&&t!=v)k=t==y||t==_?function(e,t,o){var r=pi(e);return function a(){for(var l=arguments.length,u=n(l),s=l,c=zi(a);s--;)u[s]=arguments[s];var p=l<3&&u[0]!==c&&u[l-1]!==c?[]:Eo(u,c);return(l-=p.length)<o?_i(e,t,di,a.placeholder,i,u,p,i,i,o-l):Ut(this&&this!==Mt&&this instanceof a?r:e,this,u)}}(e,t,c):t!=O&&t!=(v|O)||a.length?di.apply(i,x):function(e,t,o,r){var i=t&v,a=pi(e);return function t(){for(var l=-1,u=arguments.length,s=-1,c=r.length,p=n(c+u),f=this&&this!==Mt&&this instanceof t?a:e;++s<c;)p[s]=r[s];for(;u--;)p[s++]=arguments[++l];return Ut(f,i?o:this,p)}}(e,t,o,r);else var k=function(e,t,o){var n=t&v,r=pi(e);return function t(){return(this&&this!==Mt&&this instanceof t?r:e).apply(n?o:this,arguments)}}(e,t,o);return aa((g?Er:na)(k,x),e,t)}function xi(e,t,o,n){return e===i||hl(e,lt[o])&&!ct.call(n,o)?t:e}function ki(e,t,o,n,r,a){return kl(e)&&kl(t)&&(a.set(t,e),gr(e,t,i,ki,a),a.delete(t)),e}function Ei(e){return Rl(e)?i:e}function Ii(e,t,o,n,r,a){var l=o&m,u=e.length,s=t.length;if(u!=s&&!(l&&s>u))return!1;var c=a.get(e);if(c&&a.get(t))return c==t;var p=-1,f=!0,h=o&g?new On:i;for(a.set(e,t),a.set(t,e);++p<u;){var d=e[p],v=t[p];if(n)var j=l?n(v,d,p,t,e,a):n(d,v,p,e,t,a);if(j!==i){if(j)continue;f=!1;break}if(h){if(!no(t,function(e,t){if(!bo(h,t)&&(d===e||r(d,e,o,n,a)))return h.push(t)})){f=!1;break}}else if(d!==v&&!r(d,v,o,n,a)){f=!1;break}}return a.delete(e),a.delete(t),f}function Li(e){return ia(ea(e,i,ja),e+"")}function Ri(e){return Xn(e,ru,Hi)}function Ti(e){return Xn(e,iu,Wi)}var Mi=nn?function(e){return nn.get(e)}:zu;function Ai(e){for(var t=e.name+"",o=rn[t],n=ct.call(rn,t)?o.length:0;n--;){var r=o[n],i=r.func;if(null==i||i==e)return r.name}return t}function zi(e){return(ct.call(dn,"placeholder")?dn:e).placeholder}function Ni(){var e=dn.iteratee||Ru;return e=e===Ru?sr:e,arguments.length?e(arguments[0],arguments[1]):e}function Pi(e,t){var o,n,r=e.__data__;return("string"==(n=typeof(o=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==o:null===o)?r["string"==typeof t?"string":"hash"]:r.map}function Di(e){for(var t=ru(e),o=t.length;o--;){var n=t[o],r=e[n];t[o]=[n,r,Xi(r)]}return t}function Fi(e,t){var o=function(e,t){return null==e?i:e[t]}(e,t);return ur(o)?o:i}var Hi=Ho?function(e){return null==e?[]:(e=tt(e),Yt(Ho(e),function(t){return At.call(e,t)}))}:Gu,Wi=Ho?function(e){for(var t=[];e;)eo(t,Hi(e)),e=Rt(e);return t}:Gu,Gi=Qn;function qi(e,t,o){for(var n=-1,r=(t=Kr(t,e)).length,i=!1;++n<r;){var a=ca(t[n]);if(!(i=null!=e&&o(e,a)))break;e=e[a]}return i||++n!=r?i:!!(r=null==e?0:e.length)&&xl(r)&&Vi(a,r)&&(vl(e)||gl(e))}function Bi(e){return"function"!=typeof e.constructor||Ji(e)?{}:mn(Rt(e))}function Ui(e){return vl(e)||gl(e)||!!(Pt&&e&&e[Pt])}function Vi(e,t){var o=typeof e;return!!(t=null==t?A:t)&&("number"==o||"symbol"!=o&&Ze.test(e))&&e>-1&&e%1==0&&e<t}function Ki(e,t,o){if(!kl(o))return!1;var n=typeof t;return!!("number"==n?bl(o)&&Vi(t,o.length):"string"==n&&t in o)&&hl(o[t],e)}function Zi(e,t){if(vl(e))return!1;var o=typeof e;return!("number"!=o&&"symbol"!=o&&"boolean"!=o&&null!=e&&!zl(e))||Le.test(e)||!Ie.test(e)||null!=t&&e in tt(t)}function $i(e){var t=Ai(e),o=dn[t];if("function"!=typeof o||!(t in jn.prototype))return!1;if(e===o)return!0;var n=Mi(o);return!!n&&e===n[0]}(Jo&&Gi(new Jo(new ArrayBuffer(1)))!=se||Xo&&Gi(new Xo)!=Y||Qo&&"[object Promise]"!=Gi(Qo.resolve())||en&&Gi(new en)!=oe||tn&&Gi(new tn)!=ae)&&(Gi=function(e){var t=Qn(e),o=t==Q?e.constructor:i,n=o?pa(o):"";if(n)switch(n){case an:return se;case ln:return Y;case un:return"[object Promise]";case sn:return oe;case cn:return ae}return t});var Yi=ut?Sl:qu;function Ji(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||lt)}function Xi(e){return e==e&&!kl(e)}function Qi(e,t){return function(o){return null!=o&&o[e]===t&&(t!==i||e in tt(o))}}function ea(e,t,o){return t=Uo(t===i?e.length-1:t,0),function(){for(var r=arguments,i=-1,a=Uo(r.length-t,0),l=n(a);++i<a;)l[i]=r[t+i];i=-1;for(var u=n(t+1);++i<t;)u[i]=r[i];return u[t]=o(l),Ut(e,this,u)}}function ta(e,t){return t.length<2?e:Jn(e,Rr(t,0,-1))}function oa(e,t){if("__proto__"!=t)return e[t]}var na=la(Er),ra=Po||function(e,t){return Mt.setTimeout(e,t)},ia=la(Ir);function aa(e,t,o){var n=t+"";return ia(e,function(e,t){var o=t.length;if(!o)return e;var n=o-1;return t[n]=(o>1?"& ":"")+t[n],t=t.join(o>2?", ":" "),e.replace(Pe,"{\n/* [wrapped with "+t+"] */\n")}(n,function(e,t){return Kt(H,function(o){var n="_."+o[0];t&o[1]&&!Jt(e,n)&&e.push(n)}),e.sort()}(function(e){var t=e.match(De);return t?t[1].split(Fe):[]}(n),o)))}function la(e){var t=0,o=0;return function(){var n=Ko(),r=L-(n-o);if(o=n,r>0){if(++t>=I)return arguments[0]}else t=0;return e.apply(i,arguments)}}function ua(e,t){var o=-1,n=e.length,r=n-1;for(t=t===i?n:t;++o<t;){var a=Or(o,r),l=e[a];e[a]=e[o],e[o]=l}return e.length=t,e}var sa=function(e){var t=ll(e,function(e){return o.size===c&&o.clear(),e}),o=t.cache;return t}(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Re,function(e,o,n,r){t.push(n?r.replace(We,"$1"):o||e)}),t});function ca(e){if("string"==typeof e||zl(e))return e;var t=e+"";return"0"==t&&1/e==-M?"-0":t}function pa(e){if(null!=e){try{return st.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function fa(e){if(e instanceof jn)return e.clone();var t=new vn(e.__wrapped__,e.__chain__);return t.__actions__=ni(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var ha=Sr(function(e,t){return yl(e)?Fn(e,Un(t,1,yl,!0)):[]}),da=Sr(function(e,t){var o=Ca(t);return yl(o)&&(o=i),yl(e)?Fn(e,Un(t,1,yl,!0),Ni(o,2)):[]}),ma=Sr(function(e,t){var o=Ca(t);return yl(o)&&(o=i),yl(e)?Fn(e,Un(t,1,yl,!0),i,o):[]});function ga(e,t,o){var n=null==e?0:e.length;if(!n)return-1;var r=null==o?0:Wl(o);return r<0&&(r=Uo(n+r,0)),ao(e,Ni(t,3),r)}function va(e,t,o){var n=null==e?0:e.length;if(!n)return-1;var r=n-1;return o!==i&&(r=Wl(o),r=o<0?Uo(n+r,0):Vo(r,n-1)),ao(e,Ni(t,3),r,!0)}function ja(e){return null!=e&&e.length?Un(e,1):[]}function ba(e){return e&&e.length?e[0]:i}var ya=Sr(function(e){var t=Qt(e,Ur);return t.length&&t[0]===e[0]?nr(t):[]}),_a=Sr(function(e){var t=Ca(e),o=Qt(e,Ur);return t===Ca(o)?t=i:o.pop(),o.length&&o[0]===e[0]?nr(o,Ni(t,2)):[]}),Oa=Sr(function(e){var t=Ca(e),o=Qt(e,Ur);return(t="function"==typeof t?t:i)&&o.pop(),o.length&&o[0]===e[0]?nr(o,i,t):[]});function Ca(e){var t=null==e?0:e.length;return t?e[t-1]:i}var Sa=Sr(wa);function wa(e,t){return e&&e.length&&t&&t.length?yr(e,t):e}var xa=Li(function(e,t){var o=null==e?0:e.length,n=An(e,t);return _r(e,Qt(t,function(e){return Vi(e,o)?+e:e}).sort(ei)),n});function ka(e){return null==e?e:Yo.call(e)}var Ea=Sr(function(e){return Dr(Un(e,1,yl,!0))}),Ia=Sr(function(e){var t=Ca(e);return yl(t)&&(t=i),Dr(Un(e,1,yl,!0),Ni(t,2))}),La=Sr(function(e){var t=Ca(e);return t="function"==typeof t?t:i,Dr(Un(e,1,yl,!0),i,t)});function Ra(e){if(!e||!e.length)return[];var t=0;return e=Yt(e,function(e){if(yl(e))return t=Uo(e.length,t),!0}),go(t,function(t){return Qt(e,po(t))})}function Ta(e,t){if(!e||!e.length)return[];var o=Ra(e);return null==t?o:Qt(o,function(e){return Ut(t,i,e)})}var Ma=Sr(function(e,t){return yl(e)?Fn(e,t):[]}),Aa=Sr(function(e){return qr(Yt(e,yl))}),za=Sr(function(e){var t=Ca(e);return yl(t)&&(t=i),qr(Yt(e,yl),Ni(t,2))}),Na=Sr(function(e){var t=Ca(e);return t="function"==typeof t?t:i,qr(Yt(e,yl),i,t)}),Pa=Sr(Ra);var Da=Sr(function(e){var t=e.length,o=t>1?e[t-1]:i;return o="function"==typeof o?(e.pop(),o):i,Ta(e,o)});function Fa(e){var t=dn(e);return t.__chain__=!0,t}function Ha(e,t){return t(e)}var Wa=Li(function(e){var t=e.length,o=t?e[0]:0,n=this.__wrapped__,r=function(t){return An(t,e)};return!(t>1||this.__actions__.length)&&n instanceof jn&&Vi(o)?((n=n.slice(o,+o+(t?1:0))).__actions__.push({func:Ha,args:[r],thisArg:i}),new vn(n,this.__chain__).thru(function(e){return t&&!e.length&&e.push(i),e})):this.thru(r)});var Ga=ii(function(e,t,o){ct.call(e,o)?++e[o]:Mn(e,o,1)});var qa=fi(ga),Ba=fi(va);function Ua(e,t){return(vl(e)?Kt:Hn)(e,Ni(t,3))}function Va(e,t){return(vl(e)?Zt:Wn)(e,Ni(t,3))}var Ka=ii(function(e,t,o){ct.call(e,o)?e[o].push(t):Mn(e,o,[t])});var Za=Sr(function(e,t,o){var r=-1,i="function"==typeof t,a=bl(e)?n(e.length):[];return Hn(e,function(e){a[++r]=i?Ut(t,e,o):rr(e,t,o)}),a}),$a=ii(function(e,t,o){Mn(e,o,t)});function Ya(e,t){return(vl(e)?Qt:hr)(e,Ni(t,3))}var Ja=ii(function(e,t,o){e[o?0:1].push(t)},function(){return[[],[]]});var Xa=Sr(function(e,t){if(null==e)return[];var o=t.length;return o>1&&Ki(e,t[0],t[1])?t=[]:o>2&&Ki(t[0],t[1],t[2])&&(t=[t[0]]),jr(e,Un(t,1),[])}),Qa=No||function(){return Mt.Date.now()};function el(e,t,o){return t=o?i:t,t=e&&null==t?e.length:t,wi(e,S,i,i,i,i,t)}function tl(e,t){var o;if("function"!=typeof t)throw new rt(u);return e=Wl(e),function(){return--e>0&&(o=t.apply(this,arguments)),e<=1&&(t=i),o}}var ol=Sr(function(e,t,o){var n=v;if(o.length){var r=Eo(o,zi(ol));n|=O}return wi(e,n,t,o,r)}),nl=Sr(function(e,t,o){var n=v|j;if(o.length){var r=Eo(o,zi(nl));n|=O}return wi(t,n,e,o,r)});function rl(e,t,o){var n,r,a,l,s,c,p=0,f=!1,h=!1,d=!0;if("function"!=typeof e)throw new rt(u);function m(t){var o=n,a=r;return n=r=i,p=t,l=e.apply(a,o)}function g(e){var o=e-c;return c===i||o>=t||o<0||h&&e-p>=a}function v(){var e=Qa();if(g(e))return j(e);s=ra(v,function(e){var o=t-(e-c);return h?Vo(o,a-(e-p)):o}(e))}function j(e){return s=i,d&&n?m(e):(n=r=i,l)}function b(){var e=Qa(),o=g(e);if(n=arguments,r=this,c=e,o){if(s===i)return function(e){return p=e,s=ra(v,t),f?m(e):l}(c);if(h)return s=ra(v,t),m(c)}return s===i&&(s=ra(v,t)),l}return t=ql(t)||0,kl(o)&&(f=!!o.leading,a=(h="maxWait"in o)?Uo(ql(o.maxWait)||0,t):a,d="trailing"in o?!!o.trailing:d),b.cancel=function(){s!==i&&Yr(s),p=0,n=c=r=s=i},b.flush=function(){return s===i?l:j(Qa())},b}var il=Sr(function(e,t){return Dn(e,1,t)}),al=Sr(function(e,t,o){return Dn(e,ql(t)||0,o)});function ll(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new rt(u);var o=function(){var n=arguments,r=t?t.apply(this,n):n[0],i=o.cache;if(i.has(r))return i.get(r);var a=e.apply(this,n);return o.cache=i.set(r,a)||i,a};return o.cache=new(ll.Cache||_n),o}function ul(e){if("function"!=typeof e)throw new rt(u);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}ll.Cache=_n;var sl=Zr(function(e,t){var o=(t=1==t.length&&vl(t[0])?Qt(t[0],vo(Ni())):Qt(Un(t,1),vo(Ni()))).length;return Sr(function(n){for(var r=-1,i=Vo(n.length,o);++r<i;)n[r]=t[r].call(this,n[r]);return Ut(e,this,n)})}),cl=Sr(function(e,t){var o=Eo(t,zi(cl));return wi(e,O,i,t,o)}),pl=Sr(function(e,t){var o=Eo(t,zi(pl));return wi(e,C,i,t,o)}),fl=Li(function(e,t){return wi(e,w,i,i,i,t)});function hl(e,t){return e===t||e!=e&&t!=t}var dl=yi(er),ml=yi(function(e,t){return e>=t}),gl=ir(function(){return arguments}())?ir:function(e){return El(e)&&ct.call(e,"callee")&&!At.call(e,"callee")},vl=n.isArray,jl=Ft?vo(Ft):function(e){return El(e)&&Qn(e)==ue};function bl(e){return null!=e&&xl(e.length)&&!Sl(e)}function yl(e){return El(e)&&bl(e)}var _l=Wo||qu,Ol=Ht?vo(Ht):function(e){return El(e)&&Qn(e)==U};function Cl(e){if(!El(e))return!1;var t=Qn(e);return t==K||t==V||"string"==typeof e.message&&"string"==typeof e.name&&!Rl(e)}function Sl(e){if(!kl(e))return!1;var t=Qn(e);return t==Z||t==$||t==q||t==ee}function wl(e){return"number"==typeof e&&e==Wl(e)}function xl(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=A}function kl(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function El(e){return null!=e&&"object"==typeof e}var Il=Wt?vo(Wt):function(e){return El(e)&&Gi(e)==Y};function Ll(e){return"number"==typeof e||El(e)&&Qn(e)==J}function Rl(e){if(!El(e)||Qn(e)!=Q)return!1;var t=Rt(e);if(null===t)return!0;var o=ct.call(t,"constructor")&&t.constructor;return"function"==typeof o&&o instanceof o&&st.call(o)==dt}var Tl=Gt?vo(Gt):function(e){return El(e)&&Qn(e)==te};var Ml=qt?vo(qt):function(e){return El(e)&&Gi(e)==oe};function Al(e){return"string"==typeof e||!vl(e)&&El(e)&&Qn(e)==ne}function zl(e){return"symbol"==typeof e||El(e)&&Qn(e)==re}var Nl=Bt?vo(Bt):function(e){return El(e)&&xl(e.length)&&!!xt[Qn(e)]};var Pl=yi(fr),Dl=yi(function(e,t){return e<=t});function Fl(e){if(!e)return[];if(bl(e))return Al(e)?To(e):ni(e);if(Dt&&e[Dt])return function(e){for(var t,o=[];!(t=e.next()).done;)o.push(t.value);return o}(e[Dt]());var t=Gi(e);return(t==Y?xo:t==oe?Io:hu)(e)}function Hl(e){return e?(e=ql(e))===M||e===-M?(e<0?-1:1)*z:e==e?e:0:0===e?e:0}function Wl(e){var t=Hl(e),o=t%1;return t==t?o?t-o:t:0}function Gl(e){return e?zn(Wl(e),0,P):0}function ql(e){if("number"==typeof e)return e;if(zl(e))return N;if(kl(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=kl(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(Ae,"");var o=Ue.test(e);return o||Ke.test(e)?Lt(e.slice(2),o?2:8):Be.test(e)?N:+e}function Bl(e){return ri(e,iu(e))}function Ul(e){return null==e?"":Pr(e)}var Vl=ai(function(e,t){if(Ji(t)||bl(t))ri(t,ru(t),e);else for(var o in t)ct.call(t,o)&&In(e,o,t[o])}),Kl=ai(function(e,t){ri(t,iu(t),e)}),Zl=ai(function(e,t,o,n){ri(t,iu(t),e,n)}),$l=ai(function(e,t,o,n){ri(t,ru(t),e,n)}),Yl=Li(An);var Jl=Sr(function(e,t){e=tt(e);var o=-1,n=t.length,r=n>2?t[2]:i;for(r&&Ki(t[0],t[1],r)&&(n=1);++o<n;)for(var a=t[o],l=iu(a),u=-1,s=l.length;++u<s;){var c=l[u],p=e[c];(p===i||hl(p,lt[c])&&!ct.call(e,c))&&(e[c]=a[c])}return e}),Xl=Sr(function(e){return e.push(i,ki),Ut(lu,i,e)});function Ql(e,t,o){var n=null==e?i:Jn(e,t);return n===i?o:n}function eu(e,t){return null!=e&&qi(e,t,or)}var tu=mi(function(e,t,o){null!=t&&"function"!=typeof t.toString&&(t=ht.call(t)),e[t]=o},ku(Lu)),ou=mi(function(e,t,o){null!=t&&"function"!=typeof t.toString&&(t=ht.call(t)),ct.call(e,t)?e[t].push(o):e[t]=[o]},Ni),nu=Sr(rr);function ru(e){return bl(e)?Sn(e):cr(e)}function iu(e){return bl(e)?Sn(e,!0):pr(e)}var au=ai(function(e,t,o){gr(e,t,o)}),lu=ai(function(e,t,o,n){gr(e,t,o,n)}),uu=Li(function(e,t){var o={};if(null==e)return o;var n=!1;t=Qt(t,function(t){return t=Kr(t,e),n||(n=t.length>1),t}),ri(e,Ti(e),o),n&&(o=Nn(o,f|h|d,Ei));for(var r=t.length;r--;)Fr(o,t[r]);return o});var su=Li(function(e,t){return null==e?{}:function(e,t){return br(e,t,function(t,o){return eu(e,o)})}(e,t)});function cu(e,t){if(null==e)return{};var o=Qt(Ti(e),function(e){return[e]});return t=Ni(t),br(e,o,function(e,o){return t(e,o[0])})}var pu=Si(ru),fu=Si(iu);function hu(e){return null==e?[]:jo(e,ru(e))}var du=ci(function(e,t,o){return t=t.toLowerCase(),e+(o?mu(t):t)});function mu(e){return Cu(Ul(e).toLowerCase())}function gu(e){return(e=Ul(e))&&e.replace($e,Oo).replace(bt,"")}var vu=ci(function(e,t,o){return e+(o?"-":"")+t.toLowerCase()}),ju=ci(function(e,t,o){return e+(o?" ":"")+t.toLowerCase()}),bu=si("toLowerCase");var yu=ci(function(e,t,o){return e+(o?"_":"")+t.toLowerCase()});var _u=ci(function(e,t,o){return e+(o?" ":"")+Cu(t)});var Ou=ci(function(e,t,o){return e+(o?" ":"")+t.toUpperCase()}),Cu=si("toUpperCase");function Su(e,t,o){return e=Ul(e),(t=o?i:t)===i?function(e){return Ct.test(e)}(e)?function(e){return e.match(_t)||[]}(e):function(e){return e.match(He)||[]}(e):e.match(t)||[]}var wu=Sr(function(e,t){try{return Ut(e,i,t)}catch(e){return Cl(e)?e:new Xe(e)}}),xu=Li(function(e,t){return Kt(t,function(t){t=ca(t),Mn(e,t,ol(e[t],e))}),e});function ku(e){return function(){return e}}var Eu=hi(),Iu=hi(!0);function Lu(e){return e}function Ru(e){return sr("function"==typeof e?e:Nn(e,f))}var Tu=Sr(function(e,t){return function(o){return rr(o,e,t)}}),Mu=Sr(function(e,t){return function(o){return rr(e,o,t)}});function Au(e,t,o){var n=ru(t),r=Yn(t,n);null!=o||kl(t)&&(r.length||!n.length)||(o=t,t=e,e=this,r=Yn(t,ru(t)));var i=!(kl(o)&&"chain"in o&&!o.chain),a=Sl(e);return Kt(r,function(o){var n=t[o];e[o]=n,a&&(e.prototype[o]=function(){var t=this.__chain__;if(i||t){var o=e(this.__wrapped__);return(o.__actions__=ni(this.__actions__)).push({func:n,args:arguments,thisArg:e}),o.__chain__=t,o}return n.apply(e,eo([this.value()],arguments))})}),e}function zu(){}var Nu=vi(Qt),Pu=vi($t),Du=vi(no);function Fu(e){return Zi(e)?po(ca(e)):function(e){return function(t){return Jn(t,e)}}(e)}var Hu=bi(),Wu=bi(!0);function Gu(){return[]}function qu(){return!1}var Bu=gi(function(e,t){return e+t},0),Uu=Oi("ceil"),Vu=gi(function(e,t){return e/t},1),Ku=Oi("floor");var Zu,$u=gi(function(e,t){return e*t},1),Yu=Oi("round"),Ju=gi(function(e,t){return e-t},0);return dn.after=function(e,t){if("function"!=typeof t)throw new rt(u);return e=Wl(e),function(){if(--e<1)return t.apply(this,arguments)}},dn.ary=el,dn.assign=Vl,dn.assignIn=Kl,dn.assignInWith=Zl,dn.assignWith=$l,dn.at=Yl,dn.before=tl,dn.bind=ol,dn.bindAll=xu,dn.bindKey=nl,dn.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return vl(e)?e:[e]},dn.chain=Fa,dn.chunk=function(e,t,o){t=(o?Ki(e,t,o):t===i)?1:Uo(Wl(t),0);var r=null==e?0:e.length;if(!r||t<1)return[];for(var a=0,l=0,u=n(Do(r/t));a<r;)u[l++]=Rr(e,a,a+=t);return u},dn.compact=function(e){for(var t=-1,o=null==e?0:e.length,n=0,r=[];++t<o;){var i=e[t];i&&(r[n++]=i)}return r},dn.concat=function(){var e=arguments.length;if(!e)return[];for(var t=n(e-1),o=arguments[0],r=e;r--;)t[r-1]=arguments[r];return eo(vl(o)?ni(o):[o],Un(t,1))},dn.cond=function(e){var t=null==e?0:e.length,o=Ni();return e=t?Qt(e,function(e){if("function"!=typeof e[1])throw new rt(u);return[o(e[0]),e[1]]}):[],Sr(function(o){for(var n=-1;++n<t;){var r=e[n];if(Ut(r[0],this,o))return Ut(r[1],this,o)}})},dn.conforms=function(e){return function(e){var t=ru(e);return function(o){return Pn(o,e,t)}}(Nn(e,f))},dn.constant=ku,dn.countBy=Ga,dn.create=function(e,t){var o=mn(e);return null==t?o:Tn(o,t)},dn.curry=function e(t,o,n){var r=wi(t,y,i,i,i,i,i,o=n?i:o);return r.placeholder=e.placeholder,r},dn.curryRight=function e(t,o,n){var r=wi(t,_,i,i,i,i,i,o=n?i:o);return r.placeholder=e.placeholder,r},dn.debounce=rl,dn.defaults=Jl,dn.defaultsDeep=Xl,dn.defer=il,dn.delay=al,dn.difference=ha,dn.differenceBy=da,dn.differenceWith=ma,dn.drop=function(e,t,o){var n=null==e?0:e.length;return n?Rr(e,(t=o||t===i?1:Wl(t))<0?0:t,n):[]},dn.dropRight=function(e,t,o){var n=null==e?0:e.length;return n?Rr(e,0,(t=n-(t=o||t===i?1:Wl(t)))<0?0:t):[]},dn.dropRightWhile=function(e,t){return e&&e.length?Wr(e,Ni(t,3),!0,!0):[]},dn.dropWhile=function(e,t){return e&&e.length?Wr(e,Ni(t,3),!0):[]},dn.fill=function(e,t,o,n){var r=null==e?0:e.length;return r?(o&&"number"!=typeof o&&Ki(e,t,o)&&(o=0,n=r),function(e,t,o,n){var r=e.length;for((o=Wl(o))<0&&(o=-o>r?0:r+o),(n=n===i||n>r?r:Wl(n))<0&&(n+=r),n=o>n?0:Gl(n);o<n;)e[o++]=t;return e}(e,t,o,n)):[]},dn.filter=function(e,t){return(vl(e)?Yt:Bn)(e,Ni(t,3))},dn.flatMap=function(e,t){return Un(Ya(e,t),1)},dn.flatMapDeep=function(e,t){return Un(Ya(e,t),M)},dn.flatMapDepth=function(e,t,o){return o=o===i?1:Wl(o),Un(Ya(e,t),o)},dn.flatten=ja,dn.flattenDeep=function(e){return null!=e&&e.length?Un(e,M):[]},dn.flattenDepth=function(e,t){return null!=e&&e.length?Un(e,t=t===i?1:Wl(t)):[]},dn.flip=function(e){return wi(e,x)},dn.flow=Eu,dn.flowRight=Iu,dn.fromPairs=function(e){for(var t=-1,o=null==e?0:e.length,n={};++t<o;){var r=e[t];n[r[0]]=r[1]}return n},dn.functions=function(e){return null==e?[]:Yn(e,ru(e))},dn.functionsIn=function(e){return null==e?[]:Yn(e,iu(e))},dn.groupBy=Ka,dn.initial=function(e){return null!=e&&e.length?Rr(e,0,-1):[]},dn.intersection=ya,dn.intersectionBy=_a,dn.intersectionWith=Oa,dn.invert=tu,dn.invertBy=ou,dn.invokeMap=Za,dn.iteratee=Ru,dn.keyBy=$a,dn.keys=ru,dn.keysIn=iu,dn.map=Ya,dn.mapKeys=function(e,t){var o={};return t=Ni(t,3),Zn(e,function(e,n,r){Mn(o,t(e,n,r),e)}),o},dn.mapValues=function(e,t){var o={};return t=Ni(t,3),Zn(e,function(e,n,r){Mn(o,n,t(e,n,r))}),o},dn.matches=function(e){return dr(Nn(e,f))},dn.matchesProperty=function(e,t){return mr(e,Nn(t,f))},dn.memoize=ll,dn.merge=au,dn.mergeWith=lu,dn.method=Tu,dn.methodOf=Mu,dn.mixin=Au,dn.negate=ul,dn.nthArg=function(e){return e=Wl(e),Sr(function(t){return vr(t,e)})},dn.omit=uu,dn.omitBy=function(e,t){return cu(e,ul(Ni(t)))},dn.once=function(e){return tl(2,e)},dn.orderBy=function(e,t,o,n){return null==e?[]:(vl(t)||(t=null==t?[]:[t]),vl(o=n?i:o)||(o=null==o?[]:[o]),jr(e,t,o))},dn.over=Nu,dn.overArgs=sl,dn.overEvery=Pu,dn.overSome=Du,dn.partial=cl,dn.partialRight=pl,dn.partition=Ja,dn.pick=su,dn.pickBy=cu,dn.property=Fu,dn.propertyOf=function(e){return function(t){return null==e?i:Jn(e,t)}},dn.pull=Sa,dn.pullAll=wa,dn.pullAllBy=function(e,t,o){return e&&e.length&&t&&t.length?yr(e,t,Ni(o,2)):e},dn.pullAllWith=function(e,t,o){return e&&e.length&&t&&t.length?yr(e,t,i,o):e},dn.pullAt=xa,dn.range=Hu,dn.rangeRight=Wu,dn.rearg=fl,dn.reject=function(e,t){return(vl(e)?Yt:Bn)(e,ul(Ni(t,3)))},dn.remove=function(e,t){var o=[];if(!e||!e.length)return o;var n=-1,r=[],i=e.length;for(t=Ni(t,3);++n<i;){var a=e[n];t(a,n,e)&&(o.push(a),r.push(n))}return _r(e,r),o},dn.rest=function(e,t){if("function"!=typeof e)throw new rt(u);return Sr(e,t=t===i?t:Wl(t))},dn.reverse=ka,dn.sampleSize=function(e,t,o){return t=(o?Ki(e,t,o):t===i)?1:Wl(t),(vl(e)?xn:xr)(e,t)},dn.set=function(e,t,o){return null==e?e:kr(e,t,o)},dn.setWith=function(e,t,o,n){return n="function"==typeof n?n:i,null==e?e:kr(e,t,o,n)},dn.shuffle=function(e){return(vl(e)?kn:Lr)(e)},dn.slice=function(e,t,o){var n=null==e?0:e.length;return n?(o&&"number"!=typeof o&&Ki(e,t,o)?(t=0,o=n):(t=null==t?0:Wl(t),o=o===i?n:Wl(o)),Rr(e,t,o)):[]},dn.sortBy=Xa,dn.sortedUniq=function(e){return e&&e.length?zr(e):[]},dn.sortedUniqBy=function(e,t){return e&&e.length?zr(e,Ni(t,2)):[]},dn.split=function(e,t,o){return o&&"number"!=typeof o&&Ki(e,t,o)&&(t=o=i),(o=o===i?P:o>>>0)?(e=Ul(e))&&("string"==typeof t||null!=t&&!Tl(t))&&!(t=Pr(t))&&wo(e)?$r(To(e),0,o):e.split(t,o):[]},dn.spread=function(e,t){if("function"!=typeof e)throw new rt(u);return t=null==t?0:Uo(Wl(t),0),Sr(function(o){var n=o[t],r=$r(o,0,t);return n&&eo(r,n),Ut(e,this,r)})},dn.tail=function(e){var t=null==e?0:e.length;return t?Rr(e,1,t):[]},dn.take=function(e,t,o){return e&&e.length?Rr(e,0,(t=o||t===i?1:Wl(t))<0?0:t):[]},dn.takeRight=function(e,t,o){var n=null==e?0:e.length;return n?Rr(e,(t=n-(t=o||t===i?1:Wl(t)))<0?0:t,n):[]},dn.takeRightWhile=function(e,t){return e&&e.length?Wr(e,Ni(t,3),!1,!0):[]},dn.takeWhile=function(e,t){return e&&e.length?Wr(e,Ni(t,3)):[]},dn.tap=function(e,t){return t(e),e},dn.throttle=function(e,t,o){var n=!0,r=!0;if("function"!=typeof e)throw new rt(u);return kl(o)&&(n="leading"in o?!!o.leading:n,r="trailing"in o?!!o.trailing:r),rl(e,t,{leading:n,maxWait:t,trailing:r})},dn.thru=Ha,dn.toArray=Fl,dn.toPairs=pu,dn.toPairsIn=fu,dn.toPath=function(e){return vl(e)?Qt(e,ca):zl(e)?[e]:ni(sa(Ul(e)))},dn.toPlainObject=Bl,dn.transform=function(e,t,o){var n=vl(e),r=n||_l(e)||Nl(e);if(t=Ni(t,4),null==o){var i=e&&e.constructor;o=r?n?new i:[]:kl(e)&&Sl(i)?mn(Rt(e)):{}}return(r?Kt:Zn)(e,function(e,n,r){return t(o,e,n,r)}),o},dn.unary=function(e){return el(e,1)},dn.union=Ea,dn.unionBy=Ia,dn.unionWith=La,dn.uniq=function(e){return e&&e.length?Dr(e):[]},dn.uniqBy=function(e,t){return e&&e.length?Dr(e,Ni(t,2)):[]},dn.uniqWith=function(e,t){return t="function"==typeof t?t:i,e&&e.length?Dr(e,i,t):[]},dn.unset=function(e,t){return null==e||Fr(e,t)},dn.unzip=Ra,dn.unzipWith=Ta,dn.update=function(e,t,o){return null==e?e:Hr(e,t,Vr(o))},dn.updateWith=function(e,t,o,n){return n="function"==typeof n?n:i,null==e?e:Hr(e,t,Vr(o),n)},dn.values=hu,dn.valuesIn=function(e){return null==e?[]:jo(e,iu(e))},dn.without=Ma,dn.words=Su,dn.wrap=function(e,t){return cl(Vr(t),e)},dn.xor=Aa,dn.xorBy=za,dn.xorWith=Na,dn.zip=Pa,dn.zipObject=function(e,t){return Br(e||[],t||[],In)},dn.zipObjectDeep=function(e,t){return Br(e||[],t||[],kr)},dn.zipWith=Da,dn.entries=pu,dn.entriesIn=fu,dn.extend=Kl,dn.extendWith=Zl,Au(dn,dn),dn.add=Bu,dn.attempt=wu,dn.camelCase=du,dn.capitalize=mu,dn.ceil=Uu,dn.clamp=function(e,t,o){return o===i&&(o=t,t=i),o!==i&&(o=(o=ql(o))==o?o:0),t!==i&&(t=(t=ql(t))==t?t:0),zn(ql(e),t,o)},dn.clone=function(e){return Nn(e,d)},dn.cloneDeep=function(e){return Nn(e,f|d)},dn.cloneDeepWith=function(e,t){return Nn(e,f|d,t="function"==typeof t?t:i)},dn.cloneWith=function(e,t){return Nn(e,d,t="function"==typeof t?t:i)},dn.conformsTo=function(e,t){return null==t||Pn(e,t,ru(t))},dn.deburr=gu,dn.defaultTo=function(e,t){return null==e||e!=e?t:e},dn.divide=Vu,dn.endsWith=function(e,t,o){e=Ul(e),t=Pr(t);var n=e.length,r=o=o===i?n:zn(Wl(o),0,n);return(o-=t.length)>=0&&e.slice(o,r)==t},dn.eq=hl,dn.escape=function(e){return(e=Ul(e))&&we.test(e)?e.replace(Ce,Co):e},dn.escapeRegExp=function(e){return(e=Ul(e))&&Me.test(e)?e.replace(Te,"\\$&"):e},dn.every=function(e,t,o){var n=vl(e)?$t:Gn;return o&&Ki(e,t,o)&&(t=i),n(e,Ni(t,3))},dn.find=qa,dn.findIndex=ga,dn.findKey=function(e,t){return io(e,Ni(t,3),Zn)},dn.findLast=Ba,dn.findLastIndex=va,dn.findLastKey=function(e,t){return io(e,Ni(t,3),$n)},dn.floor=Ku,dn.forEach=Ua,dn.forEachRight=Va,dn.forIn=function(e,t){return null==e?e:Vn(e,Ni(t,3),iu)},dn.forInRight=function(e,t){return null==e?e:Kn(e,Ni(t,3),iu)},dn.forOwn=function(e,t){return e&&Zn(e,Ni(t,3))},dn.forOwnRight=function(e,t){return e&&$n(e,Ni(t,3))},dn.get=Ql,dn.gt=dl,dn.gte=ml,dn.has=function(e,t){return null!=e&&qi(e,t,tr)},dn.hasIn=eu,dn.head=ba,dn.identity=Lu,dn.includes=function(e,t,o,n){e=bl(e)?e:hu(e),o=o&&!n?Wl(o):0;var r=e.length;return o<0&&(o=Uo(r+o,0)),Al(e)?o<=r&&e.indexOf(t,o)>-1:!!r&&lo(e,t,o)>-1},dn.indexOf=function(e,t,o){var n=null==e?0:e.length;if(!n)return-1;var r=null==o?0:Wl(o);return r<0&&(r=Uo(n+r,0)),lo(e,t,r)},dn.inRange=function(e,t,o){return t=Hl(t),o===i?(o=t,t=0):o=Hl(o),function(e,t,o){return e>=Vo(t,o)&&e<Uo(t,o)}(e=ql(e),t,o)},dn.invoke=nu,dn.isArguments=gl,dn.isArray=vl,dn.isArrayBuffer=jl,dn.isArrayLike=bl,dn.isArrayLikeObject=yl,dn.isBoolean=function(e){return!0===e||!1===e||El(e)&&Qn(e)==B},dn.isBuffer=_l,dn.isDate=Ol,dn.isElement=function(e){return El(e)&&1===e.nodeType&&!Rl(e)},dn.isEmpty=function(e){if(null==e)return!0;if(bl(e)&&(vl(e)||"string"==typeof e||"function"==typeof e.splice||_l(e)||Nl(e)||gl(e)))return!e.length;var t=Gi(e);if(t==Y||t==oe)return!e.size;if(Ji(e))return!cr(e).length;for(var o in e)if(ct.call(e,o))return!1;return!0},dn.isEqual=function(e,t){return ar(e,t)},dn.isEqualWith=function(e,t,o){var n=(o="function"==typeof o?o:i)?o(e,t):i;return n===i?ar(e,t,i,o):!!n},dn.isError=Cl,dn.isFinite=function(e){return"number"==typeof e&&Go(e)},dn.isFunction=Sl,dn.isInteger=wl,dn.isLength=xl,dn.isMap=Il,dn.isMatch=function(e,t){return e===t||lr(e,t,Di(t))},dn.isMatchWith=function(e,t,o){return o="function"==typeof o?o:i,lr(e,t,Di(t),o)},dn.isNaN=function(e){return Ll(e)&&e!=+e},dn.isNative=function(e){if(Yi(e))throw new Xe(l);return ur(e)},dn.isNil=function(e){return null==e},dn.isNull=function(e){return null===e},dn.isNumber=Ll,dn.isObject=kl,dn.isObjectLike=El,dn.isPlainObject=Rl,dn.isRegExp=Tl,dn.isSafeInteger=function(e){return wl(e)&&e>=-A&&e<=A},dn.isSet=Ml,dn.isString=Al,dn.isSymbol=zl,dn.isTypedArray=Nl,dn.isUndefined=function(e){return e===i},dn.isWeakMap=function(e){return El(e)&&Gi(e)==ae},dn.isWeakSet=function(e){return El(e)&&Qn(e)==le},dn.join=function(e,t){return null==e?"":qo.call(e,t)},dn.kebabCase=vu,dn.last=Ca,dn.lastIndexOf=function(e,t,o){var n=null==e?0:e.length;if(!n)return-1;var r=n;return o!==i&&(r=(r=Wl(o))<0?Uo(n+r,0):Vo(r,n-1)),t==t?function(e,t,o){for(var n=o+1;n--;)if(e[n]===t)return n;return n}(e,t,r):ao(e,so,r,!0)},dn.lowerCase=ju,dn.lowerFirst=bu,dn.lt=Pl,dn.lte=Dl,dn.max=function(e){return e&&e.length?qn(e,Lu,er):i},dn.maxBy=function(e,t){return e&&e.length?qn(e,Ni(t,2),er):i},dn.mean=function(e){return co(e,Lu)},dn.meanBy=function(e,t){return co(e,Ni(t,2))},dn.min=function(e){return e&&e.length?qn(e,Lu,fr):i},dn.minBy=function(e,t){return e&&e.length?qn(e,Ni(t,2),fr):i},dn.stubArray=Gu,dn.stubFalse=qu,dn.stubObject=function(){return{}},dn.stubString=function(){return""},dn.stubTrue=function(){return!0},dn.multiply=$u,dn.nth=function(e,t){return e&&e.length?vr(e,Wl(t)):i},dn.noConflict=function(){return Mt._===this&&(Mt._=mt),this},dn.noop=zu,dn.now=Qa,dn.pad=function(e,t,o){e=Ul(e);var n=(t=Wl(t))?Ro(e):0;if(!t||n>=t)return e;var r=(t-n)/2;return ji(Fo(r),o)+e+ji(Do(r),o)},dn.padEnd=function(e,t,o){e=Ul(e);var n=(t=Wl(t))?Ro(e):0;return t&&n<t?e+ji(t-n,o):e},dn.padStart=function(e,t,o){e=Ul(e);var n=(t=Wl(t))?Ro(e):0;return t&&n<t?ji(t-n,o)+e:e},dn.parseInt=function(e,t,o){return o||null==t?t=0:t&&(t=+t),Zo(Ul(e).replace(ze,""),t||0)},dn.random=function(e,t,o){if(o&&"boolean"!=typeof o&&Ki(e,t,o)&&(t=o=i),o===i&&("boolean"==typeof t?(o=t,t=i):"boolean"==typeof e&&(o=e,e=i)),e===i&&t===i?(e=0,t=1):(e=Hl(e),t===i?(t=e,e=0):t=Hl(t)),e>t){var n=e;e=t,t=n}if(o||e%1||t%1){var r=$o();return Vo(e+r*(t-e+It("1e-"+((r+"").length-1))),t)}return Or(e,t)},dn.reduce=function(e,t,o){var n=vl(e)?to:ho,r=arguments.length<3;return n(e,Ni(t,4),o,r,Hn)},dn.reduceRight=function(e,t,o){var n=vl(e)?oo:ho,r=arguments.length<3;return n(e,Ni(t,4),o,r,Wn)},dn.repeat=function(e,t,o){return t=(o?Ki(e,t,o):t===i)?1:Wl(t),Cr(Ul(e),t)},dn.replace=function(){var e=arguments,t=Ul(e[0]);return e.length<3?t:t.replace(e[1],e[2])},dn.result=function(e,t,o){var n=-1,r=(t=Kr(t,e)).length;for(r||(r=1,e=i);++n<r;){var a=null==e?i:e[ca(t[n])];a===i&&(n=r,a=o),e=Sl(a)?a.call(e):a}return e},dn.round=Yu,dn.runInContext=e,dn.sample=function(e){return(vl(e)?wn:wr)(e)},dn.size=function(e){if(null==e)return 0;if(bl(e))return Al(e)?Ro(e):e.length;var t=Gi(e);return t==Y||t==oe?e.size:cr(e).length},dn.snakeCase=yu,dn.some=function(e,t,o){var n=vl(e)?no:Tr;return o&&Ki(e,t,o)&&(t=i),n(e,Ni(t,3))},dn.sortedIndex=function(e,t){return Mr(e,t)},dn.sortedIndexBy=function(e,t,o){return Ar(e,t,Ni(o,2))},dn.sortedIndexOf=function(e,t){var o=null==e?0:e.length;if(o){var n=Mr(e,t);if(n<o&&hl(e[n],t))return n}return-1},dn.sortedLastIndex=function(e,t){return Mr(e,t,!0)},dn.sortedLastIndexBy=function(e,t,o){return Ar(e,t,Ni(o,2),!0)},dn.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var o=Mr(e,t,!0)-1;if(hl(e[o],t))return o}return-1},dn.startCase=_u,dn.startsWith=function(e,t,o){return e=Ul(e),o=null==o?0:zn(Wl(o),0,e.length),t=Pr(t),e.slice(o,o+t.length)==t},dn.subtract=Ju,dn.sum=function(e){return e&&e.length?mo(e,Lu):0},dn.sumBy=function(e,t){return e&&e.length?mo(e,Ni(t,2)):0},dn.template=function(e,t,o){var n=dn.templateSettings;o&&Ki(e,t,o)&&(t=i),e=Ul(e),t=Zl({},t,n,xi);var r,a,l=Zl({},t.imports,n.imports,xi),u=ru(l),s=jo(l,u),c=0,p=t.interpolate||Ye,f="__p += '",h=ot((t.escape||Ye).source+"|"+p.source+"|"+(p===Ee?Ge:Ye).source+"|"+(t.evaluate||Ye).source+"|$","g"),d="//# sourceURL="+("sourceURL"in t?t.sourceURL:"lodash.templateSources["+ ++wt+"]")+"\n";e.replace(h,function(t,o,n,i,l,u){return n||(n=i),f+=e.slice(c,u).replace(Je,So),o&&(r=!0,f+="' +\n__e("+o+") +\n'"),l&&(a=!0,f+="';\n"+l+";\n__p += '"),n&&(f+="' +\n((__t = ("+n+")) == null ? '' : __t) +\n'"),c=u+t.length,t}),f+="';\n";var m=t.variable;m||(f="with (obj) {\n"+f+"\n}\n"),f=(a?f.replace(be,""):f).replace(ye,"$1").replace(_e,"$1;"),f="function("+(m||"obj")+") {\n"+(m?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(r?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+f+"return __p\n}";var g=wu(function(){return Qe(u,d+"return "+f).apply(i,s)});if(g.source=f,Cl(g))throw g;return g},dn.times=function(e,t){if((e=Wl(e))<1||e>A)return[];var o=P,n=Vo(e,P);t=Ni(t),e-=P;for(var r=go(n,t);++o<e;)t(o);return r},dn.toFinite=Hl,dn.toInteger=Wl,dn.toLength=Gl,dn.toLower=function(e){return Ul(e).toLowerCase()},dn.toNumber=ql,dn.toSafeInteger=function(e){return e?zn(Wl(e),-A,A):0===e?e:0},dn.toString=Ul,dn.toUpper=function(e){return Ul(e).toUpperCase()},dn.trim=function(e,t,o){if((e=Ul(e))&&(o||t===i))return e.replace(Ae,"");if(!e||!(t=Pr(t)))return e;var n=To(e),r=To(t);return $r(n,yo(n,r),_o(n,r)+1).join("")},dn.trimEnd=function(e,t,o){if((e=Ul(e))&&(o||t===i))return e.replace(Ne,"");if(!e||!(t=Pr(t)))return e;var n=To(e);return $r(n,0,_o(n,To(t))+1).join("")},dn.trimStart=function(e,t,o){if((e=Ul(e))&&(o||t===i))return e.replace(ze,"");if(!e||!(t=Pr(t)))return e;var n=To(e);return $r(n,yo(n,To(t))).join("")},dn.truncate=function(e,t){var o=k,n=E;if(kl(t)){var r="separator"in t?t.separator:r;o="length"in t?Wl(t.length):o,n="omission"in t?Pr(t.omission):n}var a=(e=Ul(e)).length;if(wo(e)){var l=To(e);a=l.length}if(o>=a)return e;var u=o-Ro(n);if(u<1)return n;var s=l?$r(l,0,u).join(""):e.slice(0,u);if(r===i)return s+n;if(l&&(u+=s.length-u),Tl(r)){if(e.slice(u).search(r)){var c,p=s;for(r.global||(r=ot(r.source,Ul(qe.exec(r))+"g")),r.lastIndex=0;c=r.exec(p);)var f=c.index;s=s.slice(0,f===i?u:f)}}else if(e.indexOf(Pr(r),u)!=u){var h=s.lastIndexOf(r);h>-1&&(s=s.slice(0,h))}return s+n},dn.unescape=function(e){return(e=Ul(e))&&Se.test(e)?e.replace(Oe,Mo):e},dn.uniqueId=function(e){var t=++pt;return Ul(e)+t},dn.upperCase=Ou,dn.upperFirst=Cu,dn.each=Ua,dn.eachRight=Va,dn.first=ba,Au(dn,(Zu={},Zn(dn,function(e,t){ct.call(dn.prototype,t)||(Zu[t]=e)}),Zu),{chain:!1}),dn.VERSION="4.17.11",Kt(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){dn[e].placeholder=dn}),Kt(["drop","take"],function(e,t){jn.prototype[e]=function(o){o=o===i?1:Uo(Wl(o),0);var n=this.__filtered__&&!t?new jn(this):this.clone();return n.__filtered__?n.__takeCount__=Vo(o,n.__takeCount__):n.__views__.push({size:Vo(o,P),type:e+(n.__dir__<0?"Right":"")}),n},jn.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),Kt(["filter","map","takeWhile"],function(e,t){var o=t+1,n=o==R||3==o;jn.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Ni(e,3),type:o}),t.__filtered__=t.__filtered__||n,t}}),Kt(["head","last"],function(e,t){var o="take"+(t?"Right":"");jn.prototype[e]=function(){return this[o](1).value()[0]}}),Kt(["initial","tail"],function(e,t){var o="drop"+(t?"":"Right");jn.prototype[e]=function(){return this.__filtered__?new jn(this):this[o](1)}}),jn.prototype.compact=function(){return this.filter(Lu)},jn.prototype.find=function(e){return this.filter(e).head()},jn.prototype.findLast=function(e){return this.reverse().find(e)},jn.prototype.invokeMap=Sr(function(e,t){return"function"==typeof e?new jn(this):this.map(function(o){return rr(o,e,t)})}),jn.prototype.reject=function(e){return this.filter(ul(Ni(e)))},jn.prototype.slice=function(e,t){e=Wl(e);var o=this;return o.__filtered__&&(e>0||t<0)?new jn(o):(e<0?o=o.takeRight(-e):e&&(o=o.drop(e)),t!==i&&(o=(t=Wl(t))<0?o.dropRight(-t):o.take(t-e)),o)},jn.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},jn.prototype.toArray=function(){return this.take(P)},Zn(jn.prototype,function(e,t){var o=/^(?:filter|find|map|reject)|While$/.test(t),n=/^(?:head|last)$/.test(t),r=dn[n?"take"+("last"==t?"Right":""):t],a=n||/^find/.test(t);r&&(dn.prototype[t]=function(){var t=this.__wrapped__,l=n?[1]:arguments,u=t instanceof jn,s=l[0],c=u||vl(t),p=function(e){var t=r.apply(dn,eo([e],l));return n&&f?t[0]:t};c&&o&&"function"==typeof s&&1!=s.length&&(u=c=!1);var f=this.__chain__,h=!!this.__actions__.length,d=a&&!f,m=u&&!h;if(!a&&c){t=m?t:new jn(this);var g=e.apply(t,l);return g.__actions__.push({func:Ha,args:[p],thisArg:i}),new vn(g,f)}return d&&m?e.apply(this,l):(g=this.thru(p),d?n?g.value()[0]:g.value():g)})}),Kt(["pop","push","shift","sort","splice","unshift"],function(e){var t=it[e],o=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",n=/^(?:pop|shift)$/.test(e);dn.prototype[e]=function(){var e=arguments;if(n&&!this.__chain__){var r=this.value();return t.apply(vl(r)?r:[],e)}return this[o](function(o){return t.apply(vl(o)?o:[],e)})}}),Zn(jn.prototype,function(e,t){var o=dn[t];if(o){var n=o.name+"";(rn[n]||(rn[n]=[])).push({name:t,func:o})}}),rn[di(i,j).name]=[{name:"wrapper",func:i}],jn.prototype.clone=function(){var e=new jn(this.__wrapped__);return e.__actions__=ni(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=ni(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=ni(this.__views__),e},jn.prototype.reverse=function(){if(this.__filtered__){var e=new jn(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},jn.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,o=vl(e),n=t<0,r=o?e.length:0,i=function(e,t,o){for(var n=-1,r=o.length;++n<r;){var i=o[n],a=i.size;switch(i.type){case"drop":e+=a;break;case"dropRight":t-=a;break;case"take":t=Vo(t,e+a);break;case"takeRight":e=Uo(e,t-a)}}return{start:e,end:t}}(0,r,this.__views__),a=i.start,l=i.end,u=l-a,s=n?l:a-1,c=this.__iteratees__,p=c.length,f=0,h=Vo(u,this.__takeCount__);if(!o||!n&&r==u&&h==u)return Gr(e,this.__actions__);var d=[];e:for(;u--&&f<h;){for(var m=-1,g=e[s+=t];++m<p;){var v=c[m],j=v.iteratee,b=v.type,y=j(g);if(b==T)g=y;else if(!y){if(b==R)continue e;break e}}d[f++]=g}return d},dn.prototype.at=Wa,dn.prototype.chain=function(){return Fa(this)},dn.prototype.commit=function(){return new vn(this.value(),this.__chain__)},dn.prototype.next=function(){this.__values__===i&&(this.__values__=Fl(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?i:this.__values__[this.__index__++]}},dn.prototype.plant=function(e){for(var t,o=this;o instanceof gn;){var n=fa(o);n.__index__=0,n.__values__=i,t?r.__wrapped__=n:t=n;var r=n;o=o.__wrapped__}return r.__wrapped__=e,t},dn.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof jn){var t=e;return this.__actions__.length&&(t=new jn(this)),(t=t.reverse()).__actions__.push({func:Ha,args:[ka],thisArg:i}),new vn(t,this.__chain__)}return this.thru(ka)},dn.prototype.toJSON=dn.prototype.valueOf=dn.prototype.value=function(){return Gr(this.__wrapped__,this.__actions__)},dn.prototype.first=dn.prototype.head,Dt&&(dn.prototype[Dt]=function(){return this}),dn}();Mt._=Ao,(r=function(){return Ao}.call(t,o,t,n))===i||(n.exports=r)}).call(this)}).call(this,o(48),o(252)(e))},function(e,t,o){"use strict";
/**
 * <AUTHOR> (slafleche) LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
var n;o.d(t,"a",function(){return n}),function(e){e.MOBILE="mobile",e.TABLET="tablet",e.NO_BLEED="no_bleed",e.DESKTOP="desktop"}(n||(n={}))},function(e,t,o){"use strict";var n=o(3),r=o(6),i=o(8),a=o(1),l=o(9),u=o(10),s=o(2),c=o(0),p=o.n(c),f=o(5),h=o(510),d=o(65),m=o(553),g=o(591),v=o(22),j=o.n(v),b=o(557),y=o(592),_=function(e){function t(e){var o;return Object(n.a)(this,t),o=Object(i.a)(this,Object(a.a)(t).call(this,e)),Object(s.a)(Object(u.a)(Object(u.a)(o)),"quill",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"ID",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"componentID",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"menuID",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"buttonID",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"selfRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"buttonRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"menuRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"formatter",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"focusWatcher",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"pilcrowClickHandler",function(e){e.preventDefault(),o.setState({hasFocus:!o.state.hasFocus},function(){o.state.hasFocus&&(o.menuRef.current.focusFirstItem(),Object(d.c)())})}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"close",function(){o.setState({hasFocus:!1});var e=o.props.lastGoodSelection,t={index:e.index+e.length,length:0};o.quill.setSelection(t)}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"handleKeyDown",function(e){27===e.keyCode&&o.state.hasFocus&&(e.preventDefault(),o.close())}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"handlePilcrowKeyDown",function(e){switch(e.key){case"ArrowUp":e.preventDefault(),o.setState({hasFocus:!0},function(){o.menuRef.current.focusFirstItem()});break;case"ArrowDown":e.preventDefault(),o.setState({hasFocus:!0},function(){o.menuRef.current.focusLastItem()})}}),o.quill=e.quill,o.formatter=new m.a(o.quill),o.ID=o.props.editorID+"paragraphMenu",o.componentID=o.ID+"-component",o.menuID=o.ID+"-menu",o.buttonID=o.ID+"-button",o.state={hasFocus:!1},o}return Object(l.a)(t,e),Object(r.a)(t,[{key:"componentDidMount",value:function(){var e=this;this.focusWatcher=new b.a(this.selfRef.current,function(t){t||e.setState({hasFocus:!1})}),this.focusWatcher.start()}},{key:"componentWillUnmount",value:function(){this.focusWatcher.stop()}},{key:"render",value:function(){var e=j()({isOpen:this.isMenuVisible},"richEditor-button","richEditorParagraphMenu-handle");return this.isPilcrowVisible&&!Object(d.l)(this.quill,this.props.lastGoodSelection)||(e+=" isHidden"),p.a.createElement("div",{id:this.componentID,style:this.pilcrowStyles,className:j()("richEditorParagraphMenu",{isMenuInset:!this.props.legacyMode}),onKeyDown:this.handleKeyDown,ref:this.selfRef},p.a.createElement("button",{type:"button",id:this.buttonID,ref:this.buttonRef,"aria-label":Object(f.m)("Line Level Formatting Menu"),"aria-controls":this.menuID,"aria-expanded":this.isMenuVisible,disabled:!this.isPilcrowVisible,className:e,"aria-haspopup":"menu",onClick:this.pilcrowClickHandler,onKeyDown:this.handlePilcrowKeyDown},p.a.createElement(y.a,{activeFormats:this.props.activeFormats})),p.a.createElement("div",{id:this.menuID,className:this.toolbarClasses,style:this.toolbarStyles,role:"menu"},p.a.createElement(g.a,{menuRef:this.menuRef,formatter:this.formatter,afterClickHandler:this.close,activeFormats:this.props.activeFormats,lastGoodSelection:this.props.lastGoodSelection})))}},{key:"isPilcrowVisible",get:function(){return!!this.props.currentSelection}},{key:"isMenuVisible",get:function(){return!!this.props.lastGoodSelection&&this.state.hasFocus}},{key:"pilcrowStyles",get:function(){if(!this.props.lastGoodSelection)return{};var e=this.quill.getBounds(this.props.lastGoodSelection.index,this.props.lastGoodSelection.length);return{top:(e.top+e.bottom)/2-this.verticalOffset}}},{key:"verticalOffset",get:function(){return(parseInt(window.getComputedStyle(this.quill.root).paddingTop,10)||t.DEFAULT_OFFSET)+(this.props.legacyMode?t.LEGACY_EXTRA_OFFSET:0)}},{key:"toolbarClasses",get:function(){if(!this.props.lastGoodSelection)return"";var e=this.quill.getBounds(this.props.lastGoodSelection.index,this.props.lastGoodSelection.length),t="richEditor-toolbarContainer richEditor-paragraphToolbarContainer";return this.props.legacyMode||(t+=" likeDropDownContent"),e.top>30?t+=" isUp":t+=" isDown",t}},{key:"toolbarStyles",get:function(){return this.isMenuVisible&&!Object(d.l)(this.quill,this.props.lastGoodSelection)?{}:{visibility:"hidden",position:"absolute",zIndex:-1}}}]),t}(p.a.PureComponent);Object(s.a)(_,"DEFAULT_OFFSET",2),Object(s.a)(_,"LEGACY_EXTRA_OFFSET",2),t.a=Object(h.b)(_)},function(e,t,o){var n=o(51),r=o(91),i="Expected a function";e.exports=function(e,t,o){var a=!0,l=!0;if("function"!=typeof e)throw new TypeError(i);return r(o)&&(a="leading"in o?!!o.leading:a,l="trailing"in o?!!o.trailing:l),n(e,t,{leading:a,maxWait:t,trailing:l})}},function(e,t,o){"use strict";(function(e){o.d(t,"a",function(){return y});var n=o(3),r=o(6),i=o(8),a=o(1),l=o(9),u=o(10),s=o(2),c=o(0),p=o.n(c),f=o(44),h=o.n(f),d=o(701),m=o(589),g=o(29),v=o(22),j=o.n(v),b=o(593),y=function(t){function o(){var e,t;Object(n.a)(this,o);for(var r=arguments.length,l=new Array(r),c=0;c<r;c++)l[c]=arguments[c];return t=Object(i.a)(this,(e=Object(a.a)(o)).call.apply(e,[this].concat(l))),Object(s.a)(Object(u.a)(Object(u.a)(t)),"id",Object(g.c)("modal")),Object(s.a)(Object(u.a)(Object(u.a)(t)),"selfRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(t)),"state",{exitElementSet:!1}),Object(s.a)(Object(u.a)(Object(u.a)(t)),"handleTabbing",function(e){e.shiftKey&&9===e.keyCode?t.handleShiftTab(e):e.shiftKey||9!==e.keyCode||t.handleTab(e)}),Object(s.a)(Object(u.a)(Object(u.a)(t)),"handleDocumentEscapePress",function(e){var n=o.stack[o.stack.length-1];if("keyCode"in e&&27===e.keyCode){if(e.preventDefault(),e.stopPropagation(),1===o.stack.length&&t.props.isWholePage)return;n.props.exitHandler&&n.props.exitHandler(e)}}),Object(s.a)(Object(u.a)(Object(u.a)(t)),"handleModalClick",function(e){e.stopPropagation()}),Object(s.a)(Object(u.a)(Object(u.a)(t)),"handleScrimClick",function(e){e.preventDefault(),t.props.exitHandler&&t.props.exitHandler(e)}),t}return Object(l.a)(o,t),Object(r.a)(o,[{key:"render",value:function(){var e=this.props.size;return h.a.createPortal(p.a.createElement("div",{className:"overlay",onClick:this.handleScrimClick},p.a.createElement("div",{id:this.modalID,role:"dialog","aria-modal":!0,className:j()("modal",{isFullScreen:e===b.a.FULL_SCREEN||e===b.a.MODAL_AS_SIDE_PANEL,inheritHeight:e===b.a.FULL_SCREEN,isSidePanel:e===b.a.MODAL_AS_SIDE_PANEL,isDropDown:e===b.a.MODAL_AS_DROP_DOWN,isLarge:e===b.a.LARGE,isMedium:e===b.a.MEDIUM,isSmall:e===b.a.SMALL,isShadowed:e===b.a.LARGE||b.a.MEDIUM||b.a.SMALL},this.props.className),ref:this.selfRef,onKeyDown:this.handleTabbing,onClick:this.handleModalClick,"aria-label":"label"in this.props?this.props.label:void 0,"aria-labelledby":"titleID"in this.props?this.props.titleID:void 0,"aria-describedby":this.props.description?this.descriptionID:void 0},this.props.description&&p.a.createElement("div",{id:this.descriptionID,className:"sr-only"},this.props.description),this.props.children)),this.props.container)}},{key:"componentDidMount",value:function(){this.focusInitialElement(),this.props.pageContainer.setAttribute("aria-hidden",!0),Object(d.disableBodyScroll)(this.selfRef.current),0===o.stack.length&&document.addEventListener("keydown",this.handleDocumentEscapePress),o.stack.push(this),this.forceUpdate()}},{key:"componentDidUpdate",value:function(e){e.elementToFocus!==this.props.elementToFocus&&this.focusInitialElement(),this.setCloseFocusElement()}},{key:"componentWillUnmount",value:function(){o.stack.pop(),0===o.stack.length?(this.props.pageContainer.removeAttribute("aria-hidden"),Object(d.enableBodyScroll)(this.selfRef.current),document.removeEventListener("keydown",this.handleDocumentEscapePress)):this.props.pageContainer.setAttribute("aria-hidden",!0);var t=o.focusHistory.pop()||document.body;t.focus(),e(function(){t.focus()})}},{key:"focusInitialElement",value:function(){var e=this.props.elementToFocus?this.props.elementToFocus:this.tabHandler.getInitial();e&&e.focus()}},{key:"setCloseFocusElement",value:function(){this.props.elementToFocusOnExit&&!this.state.exitElementSet&&(o.focusHistory.push(this.props.elementToFocusOnExit),this.setState({exitElementSet:!0}))}},{key:"handleShiftTab",value:function(e){var t=this.tabHandler.getNext(void 0,!0);t&&(e.preventDefault(),e.stopPropagation(),t.focus())}},{key:"handleTab",value:function(e){var t=this.tabHandler.getNext();t&&(e.preventDefault(),e.stopPropagation(),t.focus())}},{key:"modalID",get:function(){return this.id+"-modal"}},{key:"descriptionID",get:function(){return this.id+"-description"}},{key:"tabHandler",get:function(){return new m.a(this.selfRef.current)}}]),o}(p.a.Component);Object(s.a)(y,"defaultProps",{pageContainer:document.getElementById("page"),container:document.getElementById("modals"),isWholePage:!1}),Object(s.a)(y,"focusHistory",[]),Object(s.a)(y,"stack",[])}).call(this,o(251).setImmediate)},function(e,t,o){var n,r,i;r=[t],void 0===(i="function"==typeof(n=function(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var t=!1;if("undefined"!=typeof window){var o={get passive(){t=!0}};window.addEventListener("testPassive",null,o),window.removeEventListener("testPassive",null,o)}var n="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iPad|iPhone|iPod|(iPad Simulator)|(iPhone Simulator)|(iPod Simulator)/.test(window.navigator.platform),r=null,i=[],a=!1,l=-1,u=void 0,s=void 0,c=function(e){var t=e||window.event;return 1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},p=function(){setTimeout(function(){void 0!==s&&(document.body.style.paddingRight=s,s=void 0),void 0!==u&&(document.body.style.overflow=u,u=void 0)})};e.disableBodyScroll=function(e,o){var p;n?e&&!i.includes(e)&&(i=[].concat(function(e){if(Array.isArray(e)){for(var t=0,o=Array(e.length);t<e.length;t++)o[t]=e[t];return o}return Array.from(e)}(i),[e]),e.ontouchstart=function(e){1===e.targetTouches.length&&(l=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var o,n,r,i;1===t.targetTouches.length&&(n=e,i=(o=t).targetTouches[0].clientY-l,n&&0===n.scrollTop&&0<i?c(o):(r=n)&&r.scrollHeight-r.scrollTop<=r.clientHeight&&i<0?c(o):o.stopPropagation())},a||(document.addEventListener("touchmove",c,t?{passive:!1}:void 0),a=!0)):(p=o,setTimeout(function(){if(void 0===s){var e=!!p&&!0===p.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(s=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===u&&(u=document.body.style.overflow,document.body.style.overflow="hidden")}),r||(r=e))},e.clearAllBodyScrollLocks=function(){n?(i.forEach(function(e){e.ontouchstart=null,e.ontouchmove=null}),a&&(document.removeEventListener("touchmove",c,t?{passive:!1}:void 0),a=!1),i=[],l=-1):(p(),r=null)},e.enableBodyScroll=function(e){n?(e.ontouchstart=null,e.ontouchmove=null,i=i.filter(function(t){return t!==e}),a&&0===i.length&&(document.removeEventListener("touchmove",c,t?{passive:!1}:void 0),a=!1)):r===e&&(p(),r=null)}})?n.apply(t,r):n)||(e.exports=i)},function(e,t,o){o(703),e.exports=o(506).Object.getPrototypeOf},function(e,t,o){var n=o(542),r=o(594);o(561)("getPrototypeOf",function(){return function(e){return r(n(e))}})},function(e,t,o){o(705);var n=o(506).Object;e.exports=function(e,t,o){return n.defineProperty(e,t,o)}},function(e,t,o){var n=o(513);n(n.S+n.F*!o(519),"Object",{defineProperty:o(517).f})},function(e,t,o){e.exports={default:o(707),__esModule:!0}},function(e,t,o){o(546),o(548),e.exports=o(567).f("iterator")},function(e,t,o){var n=o(564),r=o(558);e.exports=function(e){return function(t,o){var i,a,l=String(r(t)),u=n(o),s=l.length;return u<0||u>=s?e?"":void 0:(i=l.charCodeAt(u))<55296||i>56319||u+1===s||(a=l.charCodeAt(u+1))<56320||a>57343?e?l.charAt(u):i:e?l.slice(u,u+2):a-56320+(i-55296<<10)+65536}}},function(e,t,o){"use strict";var n=o(565),r=o(545),i=o(547),a={};o(521)(a,o(512)("iterator"),function(){return this}),e.exports=function(e,t,o){e.prototype=n(a,{next:r(1,o)}),i(e,t+" Iterator")}},function(e,t,o){var n=o(517),r=o(514),i=o(531);e.exports=o(519)?Object.defineProperties:function(e,t){r(e);for(var o,a=i(t),l=a.length,u=0;l>u;)n.f(e,o=a[u++],t[o]);return e}},function(e,t,o){var n=o(522),r=o(602),i=o(712);e.exports=function(e){return function(t,o,a){var l,u=n(t),s=r(u.length),c=i(a,s);if(e&&o!=o){for(;s>c;)if((l=u[c++])!=l)return!0}else for(;s>c;c++)if((e||c in u)&&u[c]===o)return e||c||0;return!e&&-1}}},function(e,t,o){var n=o(564),r=Math.max,i=Math.min;e.exports=function(e,t){return(e=n(e))<0?r(e+t,0):i(e,t)}},function(e,t,o){"use strict";var n=o(714),r=o(715),i=o(526),a=o(522);e.exports=o(598)(Array,"Array",function(e,t){this._t=a(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,o=this._i++;return!e||o>=e.length?(this._t=void 0,r(1)):r(0,"keys"==t?o:"values"==t?e[o]:[o,e[o]])},"values"),i.Arguments=i.Array,n("keys"),n("values"),n("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,o){e.exports={default:o(717),__esModule:!0}},function(e,t,o){o(718),o(605),o(723),o(724),e.exports=o(506).Symbol},function(e,t,o){"use strict";var n=o(511),r=o(520),i=o(519),a=o(513),l=o(599),u=o(719).KEY,s=o(525),c=o(560),p=o(547),f=o(543),h=o(512),d=o(567),m=o(568),g=o(720),v=o(721),j=o(514),b=o(518),y=o(522),_=o(563),O=o(545),C=o(565),S=o(722),w=o(570),x=o(517),k=o(531),E=w.f,I=x.f,L=S.f,R=n.Symbol,T=n.JSON,M=T&&T.stringify,A=h("_hidden"),z=h("toPrimitive"),N={}.propertyIsEnumerable,P=c("symbol-registry"),D=c("symbols"),F=c("op-symbols"),H=Object.prototype,W="function"==typeof R,G=n.QObject,q=!G||!G.prototype||!G.prototype.findChild,B=i&&s(function(){return 7!=C(I({},"a",{get:function(){return I(this,"a",{value:7}).a}})).a})?function(e,t,o){var n=E(H,t);n&&delete H[t],I(e,t,o),n&&e!==H&&I(H,t,n)}:I,U=function(e){var t=D[e]=C(R.prototype);return t._k=e,t},V=W&&"symbol"==typeof R.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof R},K=function(e,t,o){return e===H&&K(F,t,o),j(e),t=_(t,!0),j(o),r(D,t)?(o.enumerable?(r(e,A)&&e[A][t]&&(e[A][t]=!1),o=C(o,{enumerable:O(0,!1)})):(r(e,A)||I(e,A,O(1,{})),e[A][t]=!0),B(e,t,o)):I(e,t,o)},Z=function(e,t){j(e);for(var o,n=g(t=y(t)),r=0,i=n.length;i>r;)K(e,o=n[r++],t[o]);return e},$=function(e){var t=N.call(this,e=_(e,!0));return!(this===H&&r(D,e)&&!r(F,e))&&(!(t||!r(this,e)||!r(D,e)||r(this,A)&&this[A][e])||t)},Y=function(e,t){if(e=y(e),t=_(t,!0),e!==H||!r(D,t)||r(F,t)){var o=E(e,t);return!o||!r(D,t)||r(e,A)&&e[A][t]||(o.enumerable=!0),o}},J=function(e){for(var t,o=L(y(e)),n=[],i=0;o.length>i;)r(D,t=o[i++])||t==A||t==u||n.push(t);return n},X=function(e){for(var t,o=e===H,n=L(o?F:y(e)),i=[],a=0;n.length>a;)!r(D,t=n[a++])||o&&!r(H,t)||i.push(D[t]);return i};W||(l((R=function(){if(this instanceof R)throw TypeError("Symbol is not a constructor!");var e=f(arguments.length>0?arguments[0]:void 0),t=function(o){this===H&&t.call(F,o),r(this,A)&&r(this[A],e)&&(this[A][e]=!1),B(this,e,O(1,o))};return i&&q&&B(H,e,{configurable:!0,set:t}),U(e)}).prototype,"toString",function(){return this._k}),w.f=Y,x.f=K,o(604).f=S.f=J,o(549).f=$,o(569).f=X,i&&!o(529)&&l(H,"propertyIsEnumerable",$,!0),d.f=function(e){return U(h(e))}),a(a.G+a.W+a.F*!W,{Symbol:R});for(var Q="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ee=0;Q.length>ee;)h(Q[ee++]);for(var te=k(h.store),oe=0;te.length>oe;)m(te[oe++]);a(a.S+a.F*!W,"Symbol",{for:function(e){return r(P,e+="")?P[e]:P[e]=R(e)},keyFor:function(e){if(!V(e))throw TypeError(e+" is not a symbol!");for(var t in P)if(P[t]===e)return t},useSetter:function(){q=!0},useSimple:function(){q=!1}}),a(a.S+a.F*!W,"Object",{create:function(e,t){return void 0===t?C(e):Z(C(e),t)},defineProperty:K,defineProperties:Z,getOwnPropertyDescriptor:Y,getOwnPropertyNames:J,getOwnPropertySymbols:X}),T&&a(a.S+a.F*(!W||s(function(){var e=R();return"[null]"!=M([e])||"{}"!=M({a:e})||"{}"!=M(Object(e))})),"JSON",{stringify:function(e){for(var t,o,n=[e],r=1;arguments.length>r;)n.push(arguments[r++]);if(o=t=n[1],(b(t)||void 0!==e)&&!V(e))return v(t)||(t=function(e,t){if("function"==typeof o&&(t=o.call(this,e,t)),!V(t))return t}),n[1]=t,M.apply(T,n)}}),R.prototype[z]||o(521)(R.prototype,z,R.prototype.valueOf),p(R,"Symbol"),p(Math,"Math",!0),p(n.JSON,"JSON",!0)},function(e,t,o){var n=o(543)("meta"),r=o(518),i=o(520),a=o(517).f,l=0,u=Object.isExtensible||function(){return!0},s=!o(525)(function(){return u(Object.preventExtensions({}))}),c=function(e){a(e,n,{value:{i:"O"+ ++l,w:{}}})},p=e.exports={KEY:n,NEED:!1,fastKey:function(e,t){if(!r(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!i(e,n)){if(!u(e))return"F";if(!t)return"E";c(e)}return e[n].i},getWeak:function(e,t){if(!i(e,n)){if(!u(e))return!0;if(!t)return!1;c(e)}return e[n].w},onFreeze:function(e){return s&&p.NEED&&u(e)&&!i(e,n)&&c(e),e}}},function(e,t,o){var n=o(531),r=o(569),i=o(549);e.exports=function(e){var t=n(e),o=r.f;if(o)for(var a,l=o(e),u=i.f,s=0;l.length>s;)u.call(e,a=l[s++])&&t.push(a);return t}},function(e,t,o){var n=o(532);e.exports=Array.isArray||function(e){return"Array"==n(e)}},function(e,t,o){var n=o(522),r=o(604).f,i={}.toString,a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return a&&"[object Window]"==i.call(e)?function(e){try{return r(e)}catch(e){return a.slice()}}(e):r(n(e))}},function(e,t,o){o(568)("asyncIterator")},function(e,t,o){o(568)("observable")},function(e,t,o){e.exports={default:o(726),__esModule:!0}},function(e,t,o){o(727),e.exports=o(506).Object.setPrototypeOf},function(e,t,o){var n=o(513);n(n.S,"Object",{setPrototypeOf:o(728).set})},function(e,t,o){var n=o(518),r=o(514),i=function(e,t){if(r(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=o(530)(Function.call,o(570).f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,o){return i(e,o),t?e.__proto__=o:n(e,o),e}}({},!1):void 0),check:i}},function(e,t,o){e.exports={default:o(730),__esModule:!0}},function(e,t,o){o(731);var n=o(506).Object;e.exports=function(e,t){return n.create(e,t)}},function(e,t,o){var n=o(513);n(n.S,"Object",{create:o(565)})},function(e,t,o){o(733),e.exports=o(506).Object.assign},function(e,t,o){var n=o(513);n(n.S+n.F,"Object",{assign:o(734)})},function(e,t,o){"use strict";var n=o(531),r=o(569),i=o(549),a=o(542),l=o(601),u=Object.assign;e.exports=!u||o(525)(function(){var e={},t={},o=Symbol(),n="abcdefghijklmnopqrst";return e[o]=7,n.split("").forEach(function(e){t[e]=e}),7!=u({},e)[o]||Object.keys(u({},t)).join("")!=n})?function(e,t){for(var o=a(e),u=arguments.length,s=1,c=r.f,p=i.f;u>s;)for(var f,h=l(arguments[s++]),d=c?n(h).concat(c(h)):n(h),m=d.length,g=0;m>g;)p.call(h,f=d[g++])&&(o[f]=h[f]);return o}:u},function(e,t,o){"use strict";var n=o(75),r=o(76),i=o(736);e.exports=function(){function e(e,t,o,n,a,l){l!==i&&r(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function t(){return e}e.isRequired=e;var o={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t};return o.checkPropTypes=n,o.PropTypes=o,o}},function(e,t,o){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,t,o){o(738),e.exports=o(506).Object.keys},function(e,t,o){var n=o(542),r=o(531);o(561)("keys",function(){return function(e){return r(n(e))}})},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=!("undefined"==typeof window||!window.document||!window.document.createElement),e.exports=t.default},function(e,t,o){e.exports={default:o(741),__esModule:!0}},function(e,t,o){o(605),o(546),o(548),o(742),o(753),o(754),e.exports=o(506).Promise},function(e,t,o){"use strict";var n,r,i,a,l=o(529),u=o(511),s=o(530),c=o(573),p=o(513),f=o(518),h=o(544),d=o(743),m=o(744),g=o(609),v=o(610).set,j=o(748)(),b=o(574),y=o(611),_=o(749),O=o(612),C=u.TypeError,S=u.process,w=S&&S.versions,x=w&&w.v8||"",k=u.Promise,E="process"==c(S),I=function(){},L=r=b.f,R=!!function(){try{var e=k.resolve(1),t=(e.constructor={})[o(512)("species")]=function(e){e(I,I)};return(E||"function"==typeof PromiseRejectionEvent)&&e.then(I)instanceof t&&0!==x.indexOf("6.6")&&-1===_.indexOf("Chrome/66")}catch(e){}}(),T=function(e){var t;return!(!f(e)||"function"!=typeof(t=e.then))&&t},M=function(e,t){if(!e._n){e._n=!0;var o=e._c;j(function(){for(var n=e._v,r=1==e._s,i=0,a=function(t){var o,i,a,l=r?t.ok:t.fail,u=t.resolve,s=t.reject,c=t.domain;try{l?(r||(2==e._h&&N(e),e._h=1),!0===l?o=n:(c&&c.enter(),o=l(n),c&&(c.exit(),a=!0)),o===t.promise?s(C("Promise-chain cycle")):(i=T(o))?i.call(o,u,s):u(o)):s(n)}catch(e){c&&!a&&c.exit(),s(e)}};o.length>i;)a(o[i++]);e._c=[],e._n=!1,t&&!e._h&&A(e)})}},A=function(e){v.call(u,function(){var t,o,n,r=e._v,i=z(e);if(i&&(t=y(function(){E?S.emit("unhandledRejection",r,e):(o=u.onunhandledrejection)?o({promise:e,reason:r}):(n=u.console)&&n.error&&n.error("Unhandled promise rejection",r)}),e._h=E||z(e)?2:1),e._a=void 0,i&&t.e)throw t.v})},z=function(e){return 1!==e._h&&0===(e._a||e._c).length},N=function(e){v.call(u,function(){var t;E?S.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})})},P=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),M(t,!0))},D=function(e){var t,o=this;if(!o._d){o._d=!0,o=o._w||o;try{if(o===e)throw C("Promise can't be resolved itself");(t=T(e))?j(function(){var n={_w:o,_d:!1};try{t.call(e,s(D,n,1),s(P,n,1))}catch(e){P.call(n,e)}}):(o._v=e,o._s=1,M(o,!1))}catch(e){P.call({_w:o,_d:!1},e)}}};R||(k=function(e){d(this,k,"Promise","_h"),h(e),n.call(this);try{e(s(D,this,1),s(P,this,1))}catch(e){P.call(this,e)}},(n=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=o(750)(k.prototype,{then:function(e,t){var o=L(g(this,k));return o.ok="function"!=typeof e||e,o.fail="function"==typeof t&&t,o.domain=E?S.domain:void 0,this._c.push(o),this._a&&this._a.push(o),this._s&&M(this,!1),o.promise},catch:function(e){return this.then(void 0,e)}}),i=function(){var e=new n;this.promise=e,this.resolve=s(D,e,1),this.reject=s(P,e,1)},b.f=L=function(e){return e===k||e===a?new i(e):r(e)}),p(p.G+p.W+p.F*!R,{Promise:k}),o(547)(k,"Promise"),o(751)("Promise"),a=o(506).Promise,p(p.S+p.F*!R,"Promise",{reject:function(e){var t=L(this);return(0,t.reject)(e),t.promise}}),p(p.S+p.F*(l||!R),"Promise",{resolve:function(e){return O(l&&this===a?k:this,e)}}),p(p.S+p.F*!(R&&o(752)(function(e){k.all(e).catch(I)})),"Promise",{all:function(e){var t=this,o=L(t),n=o.resolve,r=o.reject,i=y(function(){var o=[],i=0,a=1;m(e,!1,function(e){var l=i++,u=!1;o.push(void 0),a++,t.resolve(e).then(function(e){u||(u=!0,o[l]=e,--a||n(o))},r)}),--a||n(o)});return i.e&&r(i.v),o.promise},race:function(e){var t=this,o=L(t),n=o.reject,r=y(function(){m(e,!1,function(e){t.resolve(e).then(o.resolve,n)})});return r.e&&n(r.v),o.promise}})},function(e,t){e.exports=function(e,t,o,n){if(!(e instanceof t)||void 0!==n&&n in e)throw TypeError(o+": incorrect invocation!");return e}},function(e,t,o){var n=o(530),r=o(745),i=o(746),a=o(514),l=o(602),u=o(608),s={},c={};(t=e.exports=function(e,t,o,p,f){var h,d,m,g,v=f?function(){return e}:u(e),j=n(o,p,t?2:1),b=0;if("function"!=typeof v)throw TypeError(e+" is not iterable!");if(i(v)){for(h=l(e.length);h>b;b++)if((g=t?j(a(d=e[b])[0],d[1]):j(e[b]))===s||g===c)return g}else for(m=v.call(e);!(d=m.next()).done;)if((g=r(m,j,d.value,t))===s||g===c)return g}).BREAK=s,t.RETURN=c},function(e,t,o){var n=o(514);e.exports=function(e,t,o,r){try{return r?t(n(o)[0],o[1]):t(o)}catch(t){var i=e.return;throw void 0!==i&&n(i.call(e)),t}}},function(e,t,o){var n=o(526),r=o(512)("iterator"),i=Array.prototype;e.exports=function(e){return void 0!==e&&(n.Array===e||i[r]===e)}},function(e,t){e.exports=function(e,t,o){var n=void 0===o;switch(t.length){case 0:return n?e():e.call(o);case 1:return n?e(t[0]):e.call(o,t[0]);case 2:return n?e(t[0],t[1]):e.call(o,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(o,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(o,t[0],t[1],t[2],t[3])}return e.apply(o,t)}},function(e,t,o){var n=o(511),r=o(610).set,i=n.MutationObserver||n.WebKitMutationObserver,a=n.process,l=n.Promise,u="process"==o(532)(a);e.exports=function(){var e,t,o,s=function(){var n,r;for(u&&(n=a.domain)&&n.exit();e;){r=e.fn,e=e.next;try{r()}catch(n){throw e?o():t=void 0,n}}t=void 0,n&&n.enter()};if(u)o=function(){a.nextTick(s)};else if(!i||n.navigator&&n.navigator.standalone)if(l&&l.resolve){var c=l.resolve(void 0);o=function(){c.then(s)}}else o=function(){r.call(n,s)};else{var p=!0,f=document.createTextNode("");new i(s).observe(f,{characterData:!0}),o=function(){f.data=p=!p}}return function(n){var r={fn:n,next:void 0};t&&(t.next=r),e||(e=r,o()),t=r}}},function(e,t,o){var n=o(511).navigator;e.exports=n&&n.userAgent||""},function(e,t,o){var n=o(521);e.exports=function(e,t,o){for(var r in t)o&&e[r]?e[r]=t[r]:n(e,r,t[r]);return e}},function(e,t,o){"use strict";var n=o(511),r=o(506),i=o(517),a=o(519),l=o(512)("species");e.exports=function(e){var t="function"==typeof r[e]?r[e]:n[e];a&&t&&!t[l]&&i.f(t,l,{configurable:!0,get:function(){return this}})}},function(e,t,o){var n=o(512)("iterator"),r=!1;try{var i=[7][n]();i.return=function(){r=!0},Array.from(i,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!r)return!1;var o=!1;try{var i=[7],a=i[n]();a.next=function(){return{done:o=!0}},i[n]=function(){return a},e(i)}catch(e){}return o}},function(e,t,o){"use strict";var n=o(513),r=o(506),i=o(511),a=o(609),l=o(612);n(n.P+n.R,"Promise",{finally:function(e){var t=a(this,r.Promise||i.Promise),o="function"==typeof e;return this.then(o?function(o){return l(t,e()).then(function(){return o})}:e,o?function(o){return l(t,e()).then(function(){throw o})}:e)}})},function(e,t,o){"use strict";var n=o(513),r=o(574),i=o(611);n(n.S,"Promise",{try:function(e){var t=r.f(this),o=i(e);return(o.e?t.reject:t.resolve)(o.v),t.promise}})},function(e,t,o){e.exports={default:o(756),__esModule:!0}},function(e,t,o){o(757);var n=o(506).Object;e.exports=function(e,t){return n.getOwnPropertyDescriptor(e,t)}},function(e,t,o){var n=o(522),r=o(570).f;o(561)("getOwnPropertyDescriptor",function(){return function(e,t){return r(n(e),t)}})},function(e,t,o){"use strict";t.__esModule=!0;var n,r=o(596),i=(n=r)&&n.__esModule?n:{default:n};t.default=function(e,t,o){return t in e?(0,i.default)(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}},function(e,t,o){"use strict";t.__esModule=!0;var n=i(o(760)),r=i(o(763));function i(e){return e&&e.__esModule?e:{default:e}}t.default=function(){return function(e,t){if(Array.isArray(e))return e;if((0,n.default)(Object(e)))return function(e,t){var o=[],n=!0,i=!1,a=void 0;try{for(var l,u=(0,r.default)(e);!(n=(l=u.next()).done)&&(o.push(l.value),!t||o.length!==t);n=!0);}catch(e){i=!0,a=e}finally{try{!n&&u.return&&u.return()}finally{if(i)throw a}}return o}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}()},function(e,t,o){e.exports={default:o(761),__esModule:!0}},function(e,t,o){o(548),o(546),e.exports=o(762)},function(e,t,o){var n=o(573),r=o(512)("iterator"),i=o(526);e.exports=o(506).isIterable=function(e){var t=Object(e);return void 0!==t[r]||"@@iterator"in t||i.hasOwnProperty(n(t))}},function(e,t,o){e.exports={default:o(764),__esModule:!0}},function(e,t,o){o(548),o(546),e.exports=o(765)},function(e,t,o){var n=o(514),r=o(608);e.exports=o(506).getIterator=function(e){var t=r(e);if("function"!=typeof t)throw TypeError(e+" is not iterable!");return n(t.call(e))}},function(module,exports,__webpack_require__){"use strict";var evalAllowed=!1;try{eval("evalAllowed = true")}catch(e){}var platformSupported=!!Object.setPrototypeOf&&evalAllowed;module.exports=__webpack_require__(767)},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n,r=(n=o(0))&&"object"==typeof n&&"default"in n?n.default:n,i=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},a=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t},l=function(e){function t(){return i(this,t),a(this,e.apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t.prototype.render=function(){return r.Children.only(this.props.children)},t}(r.Component);t.AppContainer=l,t.hot=function(){return function(e){return e}},t.areComponentsEqual=function(e,t){return e===t},t.setConfig=function(){},t.cold=function(e){return e},t.configureComponent=function(){}},function(e,t,o){"use strict";var n=o(0),r=o.n(n),i=o(5),a=o(3),l=o(6),u=o(8),s=o(1),c=o(9),p=o(10),f=o(2),h=o(510),d=o(553),m=o(591),g=o(66),v=o(583),j=o(29),b=o(22),y=o.n(b),_=o(88),O=o(557),C=function e(t,o,n){var r=this;Object(a.a)(this,e),this.root=t,this.returnElement=o,this.callback=n,Object(f.a)(this,"start",function(){r.root.addEventListener("keydown",r.keydownListener)}),Object(f.a)(this,"stop",function(){r.root.removeEventListener("keydown",r.keydownListener)}),Object(f.a)(this,"keydownListener",function(e){"Escape"===e.key&&r.root.contains(document.activeElement)&&(e.preventDefault(),r.returnElement.focus(),r.callback&&r.callback(e))})},S=o(700),w=o(593),x=function(e){function t(){var e,o;Object(a.a)(this,t);for(var n=arguments.length,i=new Array(n),l=0;l<n;l++)i[l]=arguments[l];return o=Object(u.a)(this,(e=Object(s.a)(t)).call.apply(e,[this].concat(i))),Object(f.a)(Object(p.a)(Object(p.a)(o)),"state",{id:Object(j.b)(o.props,"popover"),isVisible:!1}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"initalFocusRef",r.a.createRef()),Object(f.a)(Object(p.a)(Object(p.a)(o)),"buttonRef",r.a.createRef()),Object(f.a)(Object(p.a)(Object(p.a)(o)),"controllerRef",r.a.createRef()),Object(f.a)(Object(p.a)(Object(p.a)(o)),"focusWatcher",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"escapeListener",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"handleFocusChange",function(e){e||(o.setState({isVisible:!1}),o.props.onVisibilityChange&&o.props.onVisibilityChange(!1))}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"togglePopover",function(e){e.stopPropagation(),o.setState(function(e){return{isVisible:!e.isVisible}}),o.props.onVisibilityChange&&o.props.onVisibilityChange(o.state.isVisible)}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"closeMenuHandler",function(e){if(!e.detail||!e.detail.firingKey||e.detail.firingKey!==o.constructor.name){e.stopPropagation(),e.preventDefault(),o.props.onClose&&o.props.onClose();var t=document.activeElement,n=o.controllerRef.current;o.setState({isVisible:!1}),n&&n.contains(t)&&o.buttonRef.current&&(o.buttonRef.current.focus(),o.buttonRef.current.classList.add("focus-visible")),o.props.onVisibilityChange&&o.props.onVisibilityChange(!1)}}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"stopPropagation",function(e){e.stopPropagation()}),o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e=y()(this.props.buttonClassName,this.props.toggleButtonClassName,{isOpen:this.state.isVisible}),t="name"in this.props?this.props.name:this.props.selectedItemLabel,o={id:this.contentID,initialFocusRef:this.initalFocusRef,isVisible:this.state.isVisible,closeMenuHandler:this.closeMenuHandler,renderAbove:this.props.renderAbove,renderLeft:this.props.renderLeft,openAsModal:this.props.openAsModal};return r.a.createElement("div",{id:this.state.id,className:y()({dropDown:!this.props.openAsModal,asModal:this.props.openAsModal},this.props.className),ref:this.controllerRef,onClick:this.stopPropagation},r.a.createElement(_.b,{id:this.buttonID,onClick:this.togglePopover,className:e,type:"button",title:t,"aria-label":"name"in this.props?this.props.name:void 0,"aria-controls":this.contentID,"aria-expanded":this.state.isVisible,"aria-haspopup":"true",disabled:this.props.disabled,baseClass:this.props.buttonBaseClass,buttonRef:this.buttonRef},this.props.buttonContents),!this.props.disabled&&this.state.isVisible&&r.a.createElement(r.a.Fragment,null,this.props.openAsModal?r.a.createElement(S.a,{label:Object(i.m)("title"),size:w.a.SMALL,exitHandler:this.closeMenuHandler,elementToFocusOnExit:this.buttonRef.current},this.props.children(o)):this.props.children(o)))}},{key:"componentDidUpdate",value:function(e,t){!t.isVisible&&this.state.isVisible?(this.props.onVisibilityChange&&this.props.onVisibilityChange(this.state.isVisible),this.initalFocusRef.current?this.initalFocusRef.current.focus():this.buttonRef.current&&this.buttonRef.current.focus()):t.isVisible&&!this.state.isVisible&&this.props.onVisibilityChange&&this.props.onVisibilityChange(this.state.isVisible)}},{key:"componentDidMount",value:function(){this.focusWatcher=new O.a(this.controllerRef.current,this.handleFocusChange),this.focusWatcher.start(),this.escapeListener=new C(this.controllerRef.current,this.buttonRef.current,this.closeMenuHandler),this.escapeListener.start(),this.props.setExternalButtonRef&&this.props.setExternalButtonRef(this.buttonRef)}},{key:"componentWillUnmount",value:function(){this.focusWatcher.stop(),this.escapeListener.stop()}},{key:"buttonID",get:function(){return this.state.id+"-handle"}},{key:"contentID",get:function(){return this.state.id+"-contents"}}]),t}(r.a.PureComponent),k=o(556),E=function(e){function t(){return Object(a.a)(this,t),Object(u.a)(this,Object(s.a)(t).apply(this,arguments))}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){return this.props.isVisible?n.createElement("div",{id:this.props.id,"aria-labelledby":this.props.parentID,className:y()("hasVerticalPadding",{"dropDown-contents":!this.props.openAsModal,"dropDown-asModal":this.props.openAsModal},this.props.className),style:Object(k.a)(this.props.renderAbove,this.props.renderLeft,!!this.props.legacyMode),onClick:this.props.onClick},this.props.children):n.createElement("div",{id:this.props.id,"aria-hidden":!0,"aria-labelledby":this.props.parentID,className:"sr-only"})}}]),t}(n.Component),I=function(e){function t(){return Object(a.a)(this,t),Object(u.a)(this,Object(s.a)(t).apply(this,arguments))}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e=this.props,t=e.children,o=e.title,r="h".concat(this.props.depth);return n.createElement(r,{id:this.props.id,className:y()("heading","heading-".concat(this.renderAsDepth),{pageTitle:1===this.renderAsDepth},this.props.className)},t||o)}},{key:"renderAsDepth",get:function(){return this.props.renderAsDepth?this.props.renderAsDepth:this.props.depth}}]),t}(n.Component);Object(f.a)(I,"defaultProps",{depth:2});
/**
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
var L=function(e){function t(){return Object(a.a)(this,t),Object(u.a)(this,Object(s.a)(t).apply(this,arguments))}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e=this.props.outerTag?"".concat(this.props.outerTag):"div",t=this.props.outerTag?"".concat(this.props.innerTag):"div";return r.a.createElement(e,{className:y()("smartAlign-outer",this.props.className)},r.a.createElement(t,{className:"smartAlign-inner"},this.props.children))}}]),t}(r.a.Component),R=function(e){function t(){return Object(a.a)(this,t),Object(u.a)(this,Object(s.a)(t).apply(this,arguments))}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e="".concat(this.props.tag?this.props.tag:"div");return n.createElement(e,{className:y()("u-flexSpacer",this.props.className),"aria-hidden":!0,tabIndex:-1}," ",this.props.children&&n.createElement("span",{className:"sr-only"},this.props.children))}}]),t}(n.Component),T=o(524),M=function(e){function t(e){var o;return Object(a.a)(this,t),o=Object(u.a)(this,Object(s.a)(t).call(this,e)),Object(f.a)(Object(p.a)(Object(p.a)(o)),"id",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"doNothing",function(e){e.stopPropagation()}),o.id=Object(j.b)(e,"dropDown"),o.state={selectedText:""},o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"setSelectedText",value:function(e){this.setState({selectedText:e})}},{key:"render",value:function(){var e=this,t=this.props.title;return n.createElement(x,{id:this.id,className:this.props.className,buttonBaseClass:this.props.buttonBaseClass||_.a.CUSTOM,name:this.props.name,buttonContents:this.props.buttonContents||Object(v.b)(),buttonClassName:this.props.buttonClassName,selectedItemLabel:this.selectedText,disabled:this.props.disabled,setExternalButtonRef:this.props.setExternalButtonRef,toggleButtonClassName:this.props.toggleButtonClassName,onVisibilityChange:this.props.onVisibilityChange,openAsModal:!!this.props.openAsModal},function(o){return n.createElement(E,Object(g.a)({},o,{id:e.id+"-handle",parentID:e.id,className:e.props.contentsClassName,onClick:e.doNothing,renderLeft:!!e.props.renderLeft,renderAbove:!!e.props.renderAbove,openAsModal:e.props.openAsModal}),t?n.createElement("header",{className:"frameHeader"},n.createElement(R,{className:"frameHeader-leftSpacer"}),n.createElement(L,null,n.createElement(I,{title:t,className:"dropDown-title"})),n.createElement("div",{className:"frameHeader-closePosition"},n.createElement(T.a,{className:"frameHeader-close",onClick:o.closeMenuHandler,baseClass:_.a.CUSTOM}))):null,n.createElement("ul",{className:"dropDownItems"},e.props.children))})}},{key:"selectedText",get:function(){return this.state.selectedText}}]),t}(n.Component);
/*
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */Object(f.a)(M,"defaultProps",{openAsModal:!1});var A=o(516),z=o(592),N=function(e){function t(e){var o;return Object(a.a)(this,t),o=Object(u.a)(this,Object(s.a)(t).call(this,e)),Object(f.a)(Object(p.a)(Object(p.a)(o)),"quill",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"ID",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"menuRef",r.a.createRef()),Object(f.a)(Object(p.a)(Object(p.a)(o)),"formatter",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"handlePilcrowKeyDown",function(e){switch(e.key){case"ArrowUp":e.preventDefault(),o.setState({hasFocus:!0},function(){o.menuRef.current.focusFirstItem()});break;case"ArrowDown":e.preventDefault(),o.setState({hasFocus:!0},function(){o.menuRef.current.focusLastItem()})}}),o.quill=e.quill,o.formatter=new d.a(o.quill),o.ID=o.props.editorID+"paragraphMenu",o.state={hasFocus:!1},o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){return r.a.createElement(M,{id:this.ID,className:"mobileParagraphMenu-dropDown",buttonContents:A.m(),buttonBaseClass:_.a.ICON,disabled:this.props.disabled,renderAbove:this.props.renderAbove,renderLeft:this.props.renderLeft,contentsClassName:"noMinWidth"},r.a.createElement(m.a,{menuRef:this.menuRef,formatter:this.formatter,activeFormats:r.a.createElement(z.a,{activeFormats:this.props.activeFormats}),lastGoodSelection:this.props.lastGoodSelection,onKeyDown:this.handlePilcrowKeyDown}))}}]),t}(r.a.PureComponent),P=Object(h.b)(N),D=o(65),F=o(507),H=o.n(F),W=o(504),G=o.n(W),q=o(505),B=o.n(q),U=o(508),V=o.n(U),K=o(509),Z=o.n(K);function $(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=e&&this.setState(e)}function Y(e){this.setState(function(t){var o=this.constructor.getDerivedStateFromProps(e,t);return null!=o?o:null}.bind(this))}function J(e,t){try{var o=this.props,n=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(o,n)}finally{this.props=o,this.state=n}}function X(e){var t=e.prototype;if(!t||!t.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof e.getDerivedStateFromProps&&"function"!=typeof t.getSnapshotBeforeUpdate)return e;var o=null,n=null,r=null;if("function"==typeof t.componentWillMount?o="componentWillMount":"function"==typeof t.UNSAFE_componentWillMount&&(o="UNSAFE_componentWillMount"),"function"==typeof t.componentWillReceiveProps?n="componentWillReceiveProps":"function"==typeof t.UNSAFE_componentWillReceiveProps&&(n="UNSAFE_componentWillReceiveProps"),"function"==typeof t.componentWillUpdate?r="componentWillUpdate":"function"==typeof t.UNSAFE_componentWillUpdate&&(r="UNSAFE_componentWillUpdate"),null!==o||null!==n||null!==r){var i=e.displayName||e.name,a="function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+i+" uses "+a+" but also contains the following legacy lifecycles:"+(null!==o?"\n  "+o:"")+(null!==n?"\n  "+n:"")+(null!==r?"\n  "+r:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof e.getDerivedStateFromProps&&(t.componentWillMount=$,t.componentWillReceiveProps=Y),"function"==typeof t.getSnapshotBeforeUpdate){if("function"!=typeof t.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=J;var l=t.componentDidUpdate;t.componentDidUpdate=function(e,t,o){var n=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:o;l.call(this,e,t,n)}}return e}$.__suppressDeprecationWarning=!0,Y.__suppressDeprecationWarning=!0,J.__suppressDeprecationWarning=!0;var Q=o(606),ee=o.n(Q),te=o(515),oe=o.n(te),ne=o(527),re=o.n(ne);function ie(e){var t=e.cellCount,o=e.cellSize,n=e.computeMetadataCallback,r=e.computeMetadataCallbackProps,i=e.nextCellsCount,a=e.nextCellSize,l=e.nextScrollToIndex,u=e.scrollToIndex,s=e.updateScrollOffsetForScrollToIndex;t===i&&("number"!=typeof o&&"number"!=typeof a||o===a)||(n(r),u>=0&&u===l&&s())}var ae=o(571),le=o.n(ae),ue=(o(503),function(){function e(t){var o=t.cellCount,n=t.cellSizeGetter,r=t.estimatedCellSize;G()(this,e),this._cellSizeAndPositionData={},this._lastMeasuredIndex=-1,this._lastBatchedIndex=-1,this._cellSizeGetter=n,this._cellCount=o,this._estimatedCellSize=r}return B()(e,[{key:"areOffsetsAdjusted",value:function(){return!1}},{key:"configure",value:function(e){var t=e.cellCount,o=e.estimatedCellSize,n=e.cellSizeGetter;this._cellCount=t,this._estimatedCellSize=o,this._cellSizeGetter=n}},{key:"getCellCount",value:function(){return this._cellCount}},{key:"getEstimatedCellSize",value:function(){return this._estimatedCellSize}},{key:"getLastMeasuredIndex",value:function(){return this._lastMeasuredIndex}},{key:"getOffsetAdjustment",value:function(){return 0}},{key:"getSizeAndPositionOfCell",value:function(e){if(e<0||e>=this._cellCount)throw Error("Requested index "+e+" is outside of range 0.."+this._cellCount);if(e>this._lastMeasuredIndex)for(var t=this.getSizeAndPositionOfLastMeasuredCell(),o=t.offset+t.size,n=this._lastMeasuredIndex+1;n<=e;n++){var r=this._cellSizeGetter({index:n});if(void 0===r||isNaN(r))throw Error("Invalid size returned for cell "+n+" of value "+r);null===r?(this._cellSizeAndPositionData[n]={offset:o,size:0},this._lastBatchedIndex=e):(this._cellSizeAndPositionData[n]={offset:o,size:r},o+=r,this._lastMeasuredIndex=e)}return this._cellSizeAndPositionData[e]}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._lastMeasuredIndex>=0?this._cellSizeAndPositionData[this._lastMeasuredIndex]:{offset:0,size:0}}},{key:"getTotalSize",value:function(){var e=this.getSizeAndPositionOfLastMeasuredCell();return e.offset+e.size+(this._cellCount-this._lastMeasuredIndex-1)*this._estimatedCellSize}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,o=void 0===t?"auto":t,n=e.containerSize,r=e.currentOffset,i=e.targetIndex;if(n<=0)return 0;var a=this.getSizeAndPositionOfCell(i),l=a.offset,u=l-n+a.size,s=void 0;switch(o){case"start":s=l;break;case"end":s=u;break;case"center":s=l-(n-a.size)/2;break;default:s=Math.max(u,Math.min(l,r))}var c=this.getTotalSize();return Math.max(0,Math.min(c-n,s))}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,o=e.offset;if(0===this.getTotalSize())return{};var n=o+t,r=this._findNearestCell(o),i=this.getSizeAndPositionOfCell(r);o=i.offset+i.size;for(var a=r;o<n&&a<this._cellCount-1;)a++,o+=this.getSizeAndPositionOfCell(a).size;return{start:r,stop:a}}},{key:"resetCell",value:function(e){this._lastMeasuredIndex=Math.min(this._lastMeasuredIndex,e-1)}},{key:"_binarySearch",value:function(e,t,o){for(;t<=e;){var n=t+Math.floor((e-t)/2),r=this.getSizeAndPositionOfCell(n).offset;if(r===o)return n;r<o?t=n+1:r>o&&(e=n-1)}return t>0?t-1:0}},{key:"_exponentialSearch",value:function(e,t){for(var o=1;e<this._cellCount&&this.getSizeAndPositionOfCell(e).offset<t;)e+=o,o*=2;return this._binarySearch(Math.min(e,this._cellCount-1),Math.floor(e/2),t)}},{key:"_findNearestCell",value:function(e){if(isNaN(e))throw Error("Invalid offset "+e+" specified");e=Math.max(0,e);var t=this.getSizeAndPositionOfLastMeasuredCell(),o=Math.max(0,this._lastMeasuredIndex);return t.offset>=e?this._binarySearch(o,0,e):this._exponentialSearch(o,e)}}]),e}()),se=function(){return"undefined"!=typeof window&&window.chrome&&window.chrome.webstore?16777100:15e5},ce=function(){function e(t){var o=t.maxScrollSize,n=void 0===o?se():o,r=le()(t,["maxScrollSize"]);G()(this,e),this._cellSizeAndPositionManager=new ue(r),this._maxScrollSize=n}return B()(e,[{key:"areOffsetsAdjusted",value:function(){return this._cellSizeAndPositionManager.getTotalSize()>this._maxScrollSize}},{key:"configure",value:function(e){this._cellSizeAndPositionManager.configure(e)}},{key:"getCellCount",value:function(){return this._cellSizeAndPositionManager.getCellCount()}},{key:"getEstimatedCellSize",value:function(){return this._cellSizeAndPositionManager.getEstimatedCellSize()}},{key:"getLastMeasuredIndex",value:function(){return this._cellSizeAndPositionManager.getLastMeasuredIndex()}},{key:"getOffsetAdjustment",value:function(e){var t=e.containerSize,o=e.offset,n=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize(),i=this._getOffsetPercentage({containerSize:t,offset:o,totalSize:r});return Math.round(i*(r-n))}},{key:"getSizeAndPositionOfCell",value:function(e){return this._cellSizeAndPositionManager.getSizeAndPositionOfCell(e)}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._cellSizeAndPositionManager.getSizeAndPositionOfLastMeasuredCell()}},{key:"getTotalSize",value:function(){return Math.min(this._maxScrollSize,this._cellSizeAndPositionManager.getTotalSize())}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,o=void 0===t?"auto":t,n=e.containerSize,r=e.currentOffset,i=e.targetIndex;r=this._safeOffsetToOffset({containerSize:n,offset:r});var a=this._cellSizeAndPositionManager.getUpdatedOffsetForIndex({align:o,containerSize:n,currentOffset:r,targetIndex:i});return this._offsetToSafeOffset({containerSize:n,offset:a})}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,o=e.offset;return o=this._safeOffsetToOffset({containerSize:t,offset:o}),this._cellSizeAndPositionManager.getVisibleCellRange({containerSize:t,offset:o})}},{key:"resetCell",value:function(e){this._cellSizeAndPositionManager.resetCell(e)}},{key:"_getOffsetPercentage",value:function(e){var t=e.containerSize,o=e.offset,n=e.totalSize;return n<=t?0:o/(n-t)}},{key:"_offsetToSafeOffset",value:function(e){var t=e.containerSize,o=e.offset,n=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize();if(n===r)return o;var i=this._getOffsetPercentage({containerSize:t,offset:o,totalSize:n});return Math.round(i*(r-t))}},{key:"_safeOffsetToOffset",value:function(e){var t=e.containerSize,o=e.offset,n=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize();if(n===r)return o;var i=this._getOffsetPercentage({containerSize:t,offset:o,totalSize:r});return Math.round(i*(n-t))}}]),e}(),pe=o(572),fe=o.n(pe);function he(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={};return function(o){var n=o.callback,r=o.indices,i=fe()(r),a=!e||i.every(function(e){var t=r[e];return Array.isArray(t)?t.length>0:t>=0}),l=i.length!==fe()(t).length||i.some(function(e){var o=t[e],n=r[e];return Array.isArray(n)?o.join(",")!==n.join(","):o!==n});t=r,a&&l&&n(r)}}var de=1;function me(e){var t=e.cellSize,o=e.cellSizeAndPositionManager,n=e.previousCellsCount,r=e.previousCellSize,i=e.previousScrollToAlignment,a=e.previousScrollToIndex,l=e.previousSize,u=e.scrollOffset,s=e.scrollToAlignment,c=e.scrollToIndex,p=e.size,f=e.sizeJustIncreasedFromZero,h=e.updateScrollIndexCallback,d=o.getCellCount(),m=c>=0&&c<d;m&&(p!==l||f||!r||"number"==typeof t&&t!==r||s!==i||c!==a)?h(c):!m&&d>0&&(p<l||d<n)&&u>o.getTotalSize()-p&&h(d-1)}var ge=o(607),ve=o.n(ge),je=o(740),be=o.n(je),ye=void 0,_e=(ye="undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).requestAnimationFrame||ye.webkitRequestAnimationFrame||ye.mozRequestAnimationFrame||ye.oRequestAnimationFrame||ye.msRequestAnimationFrame||function(e){return ye.setTimeout(e,1e3/60)},Oe=ye.cancelAnimationFrame||ye.webkitCancelAnimationFrame||ye.mozCancelAnimationFrame||ye.oCancelAnimationFrame||ye.msCancelAnimationFrame||function(e){ye.clearTimeout(e)},Ce=_e,Se=Oe,we=function(e){return Se(e.id)},xe=function(e,t){var o=void 0;be.a.resolve().then(function(){o=Date.now()});var n={id:Ce(function r(){Date.now()-o>=t?e.call():n.id=Ce(r)})};return n},ke="observed",Ee="requested",Ie=function(e){function t(e){G()(this,t);var o=V()(this,(t.__proto__||H()(t)).call(this,e));o._onGridRenderedMemoizer=he(),o._onScrollMemoizer=he(!1),o._deferredInvalidateColumnIndex=null,o._deferredInvalidateRowIndex=null,o._recomputeScrollLeftFlag=!1,o._recomputeScrollTopFlag=!1,o._horizontalScrollBarSize=0,o._verticalScrollBarSize=0,o._scrollbarPresenceChanged=!1,o._renderedColumnStartIndex=0,o._renderedColumnStopIndex=0,o._renderedRowStartIndex=0,o._renderedRowStopIndex=0,o._styleCache={},o._cellCache={},o._debounceScrollEndedCallback=function(){o._disablePointerEventsTimeoutId=null,o.setState({isScrolling:!1,needToResetStyleCache:!1})},o._invokeOnGridRenderedHelper=function(){var e=o.props.onSectionRendered;o._onGridRenderedMemoizer({callback:e,indices:{columnOverscanStartIndex:o._columnStartIndex,columnOverscanStopIndex:o._columnStopIndex,columnStartIndex:o._renderedColumnStartIndex,columnStopIndex:o._renderedColumnStopIndex,rowOverscanStartIndex:o._rowStartIndex,rowOverscanStopIndex:o._rowStopIndex,rowStartIndex:o._renderedRowStartIndex,rowStopIndex:o._renderedRowStopIndex}})},o._setScrollingContainerRef=function(e){o._scrollingContainer=e},o._onScroll=function(e){e.target===o._scrollingContainer&&o.handleScrollEvent(e.target)};var n=new ce({cellCount:e.columnCount,cellSizeGetter:function(o){return t._wrapSizeGetter(e.columnWidth)(o)},estimatedCellSize:t._getEstimatedColumnSize(e)}),r=new ce({cellCount:e.rowCount,cellSizeGetter:function(o){return t._wrapSizeGetter(e.rowHeight)(o)},estimatedCellSize:t._getEstimatedRowSize(e)});return o.state={instanceProps:{columnSizeAndPositionManager:n,rowSizeAndPositionManager:r,prevColumnWidth:e.columnWidth,prevRowHeight:e.rowHeight,prevColumnCount:e.columnCount,prevRowCount:e.rowCount,prevIsScrolling:!0===e.isScrolling,prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow,scrollbarSize:0,scrollbarSizeMeasured:!1},isScrolling:!1,scrollDirectionHorizontal:de,scrollDirectionVertical:de,scrollLeft:0,scrollTop:0,scrollPositionChangeReason:null,needToResetStyleCache:!1},e.scrollToRow>0&&(o._initialScrollTop=o._getCalculatedScrollTop(e,o.state)),e.scrollToColumn>0&&(o._initialScrollLeft=o._getCalculatedScrollLeft(e,o.state)),o}return Z()(t,e),B()(t,[{key:"getOffsetForCell",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.alignment,o=void 0===t?this.props.scrollToAlignment:t,n=e.columnIndex,r=void 0===n?this.props.scrollToColumn:n,i=e.rowIndex,a=void 0===i?this.props.scrollToRow:i,l=oe()({},this.props,{scrollToAlignment:o,scrollToColumn:r,scrollToRow:a});return{scrollLeft:this._getCalculatedScrollLeft(l),scrollTop:this._getCalculatedScrollTop(l)}}},{key:"getTotalRowsHeight",value:function(){return this.state.instanceProps.rowSizeAndPositionManager.getTotalSize()}},{key:"getTotalColumnsWidth",value:function(){return this.state.instanceProps.columnSizeAndPositionManager.getTotalSize()}},{key:"handleScrollEvent",value:function(e){var t=e.scrollLeft,o=void 0===t?0:t,n=e.scrollTop,r=void 0===n?0:n;if(!(r<0)){this._debounceScrollEnded();var i=this.props,a=i.autoHeight,l=i.autoWidth,u=i.height,s=i.width,c=this.state.instanceProps,p=c.scrollbarSize,f=c.rowSizeAndPositionManager.getTotalSize(),h=c.columnSizeAndPositionManager.getTotalSize(),d=Math.min(Math.max(0,h-s+p),o),m=Math.min(Math.max(0,f-u+p),r);if(this.state.scrollLeft!==d||this.state.scrollTop!==m){var g={isScrolling:!0,scrollDirectionHorizontal:d!==this.state.scrollLeft?d>this.state.scrollLeft?de:-1:this.state.scrollDirectionHorizontal,scrollDirectionVertical:m!==this.state.scrollTop?m>this.state.scrollTop?de:-1:this.state.scrollDirectionVertical,scrollPositionChangeReason:ke};a||(g.scrollTop=m),l||(g.scrollLeft=d),g.needToResetStyleCache=!1,this.setState(g)}this._invokeOnScrollMemoizer({scrollLeft:d,scrollTop:m,totalColumnsWidth:h,totalRowsHeight:f})}}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,o=e.rowIndex;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,t):t,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,o):o}},{key:"measureAllCells",value:function(){var e=this.props,t=e.columnCount,o=e.rowCount,n=this.state.instanceProps;n.columnSizeAndPositionManager.getSizeAndPositionOfCell(t-1),n.rowSizeAndPositionManager.getSizeAndPositionOfCell(o-1)}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,n=e.rowIndex,r=void 0===n?0:n,i=this.props,a=i.scrollToColumn,l=i.scrollToRow,u=this.state.instanceProps;u.columnSizeAndPositionManager.resetCell(o),u.rowSizeAndPositionManager.resetCell(r),this._recomputeScrollLeftFlag=a>=0&&(this.state.scrollDirectionHorizontal===de?o<=a:o>=a),this._recomputeScrollTopFlag=l>=0&&(this.state.scrollDirectionVertical===de?r<=l:r>=l),this._styleCache={},this._cellCache={},this.forceUpdate()}},{key:"scrollToCell",value:function(e){var t=e.columnIndex,o=e.rowIndex,n=this.props.columnCount,r=this.props;n>1&&void 0!==t&&this._updateScrollLeftForScrollToColumn(oe()({},r,{scrollToColumn:t})),void 0!==o&&this._updateScrollTopForScrollToRow(oe()({},r,{scrollToRow:o}))}},{key:"componentDidMount",value:function(){var e=this.props,o=e.getScrollbarSize,n=e.height,r=e.scrollLeft,i=e.scrollToColumn,a=e.scrollTop,l=e.scrollToRow,u=e.width,s=this.state.instanceProps;if(this._initialScrollTop=0,this._initialScrollLeft=0,this._handleInvalidatedGridSize(),s.scrollbarSizeMeasured||this.setState(function(e){var t=oe()({},e,{needToResetStyleCache:!1});return t.instanceProps.scrollbarSize=o(),t.instanceProps.scrollbarSizeMeasured=!0,t}),"number"==typeof r&&r>=0||"number"==typeof a&&a>=0){var c=t._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:r,scrollTop:a});c&&(c.needToResetStyleCache=!1,this.setState(c))}this._scrollingContainer&&(this._scrollingContainer.scrollLeft!==this.state.scrollLeft&&(this._scrollingContainer.scrollLeft=this.state.scrollLeft),this._scrollingContainer.scrollTop!==this.state.scrollTop&&(this._scrollingContainer.scrollTop=this.state.scrollTop));var p=n>0&&u>0;i>=0&&p&&this._updateScrollLeftForScrollToColumn(),l>=0&&p&&this._updateScrollTopForScrollToRow(),this._invokeOnGridRenderedHelper(),this._invokeOnScrollMemoizer({scrollLeft:r||0,scrollTop:a||0,totalColumnsWidth:s.columnSizeAndPositionManager.getTotalSize(),totalRowsHeight:s.rowSizeAndPositionManager.getTotalSize()}),this._maybeCallOnScrollbarPresenceChange()}},{key:"componentDidUpdate",value:function(e,t){var o=this,n=this.props,r=n.autoHeight,i=n.autoWidth,a=n.columnCount,l=n.height,u=n.rowCount,s=n.scrollToAlignment,c=n.scrollToColumn,p=n.scrollToRow,f=n.width,h=this.state,d=h.scrollLeft,m=h.scrollPositionChangeReason,g=h.scrollTop,v=h.instanceProps;this._handleInvalidatedGridSize();var j=a>0&&0===e.columnCount||u>0&&0===e.rowCount;m===Ee&&(!i&&d>=0&&(d!==this._scrollingContainer.scrollLeft||j)&&(this._scrollingContainer.scrollLeft=d),!r&&g>=0&&(g!==this._scrollingContainer.scrollTop||j)&&(this._scrollingContainer.scrollTop=g));var b=(0===e.width||0===e.height)&&l>0&&f>0;if(this._recomputeScrollLeftFlag?(this._recomputeScrollLeftFlag=!1,this._updateScrollLeftForScrollToColumn(this.props)):me({cellSizeAndPositionManager:v.columnSizeAndPositionManager,previousCellsCount:e.columnCount,previousCellSize:e.columnWidth,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToColumn,previousSize:e.width,scrollOffset:d,scrollToAlignment:s,scrollToIndex:c,size:f,sizeJustIncreasedFromZero:b,updateScrollIndexCallback:function(){return o._updateScrollLeftForScrollToColumn(o.props)}}),this._recomputeScrollTopFlag?(this._recomputeScrollTopFlag=!1,this._updateScrollTopForScrollToRow(this.props)):me({cellSizeAndPositionManager:v.rowSizeAndPositionManager,previousCellsCount:e.rowCount,previousCellSize:e.rowHeight,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToRow,previousSize:e.height,scrollOffset:g,scrollToAlignment:s,scrollToIndex:p,size:l,sizeJustIncreasedFromZero:b,updateScrollIndexCallback:function(){return o._updateScrollTopForScrollToRow(o.props)}}),this._invokeOnGridRenderedHelper(),d!==t.scrollLeft||g!==t.scrollTop){var y=v.rowSizeAndPositionManager.getTotalSize(),_=v.columnSizeAndPositionManager.getTotalSize();this._invokeOnScrollMemoizer({scrollLeft:d,scrollTop:g,totalColumnsWidth:_,totalRowsHeight:y})}this._maybeCallOnScrollbarPresenceChange()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&we(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,t=e.autoContainerWidth,o=e.autoHeight,r=e.autoWidth,i=e.className,a=e.containerProps,l=e.containerRole,u=e.containerStyle,s=e.height,c=e.id,p=e.noContentRenderer,f=e.role,h=e.style,d=e.tabIndex,m=e.width,g=this.state,v=g.instanceProps,j=g.needToResetStyleCache,b=this._isScrolling(),y={boxSizing:"border-box",direction:"ltr",height:o?"auto":s,position:"relative",width:r?"auto":m,WebkitOverflowScrolling:"touch",willChange:"transform"};j&&(this._styleCache={}),this.state.isScrolling||this._resetStyleCache(),this._calculateChildrenToRender(this.props,this.state);var _=v.columnSizeAndPositionManager.getTotalSize(),O=v.rowSizeAndPositionManager.getTotalSize(),C=O>s?v.scrollbarSize:0,S=_>m?v.scrollbarSize:0;S===this._horizontalScrollBarSize&&C===this._verticalScrollBarSize||(this._horizontalScrollBarSize=S,this._verticalScrollBarSize=C,this._scrollbarPresenceChanged=!0),y.overflowX=_+C<=m?"hidden":"auto",y.overflowY=O+S<=s?"hidden":"auto";var w=this._childrenToDisplay,x=0===w.length&&s>0&&m>0;return n.createElement("div",oe()({ref:this._setScrollingContainerRef},a,{"aria-label":this.props["aria-label"],"aria-readonly":this.props["aria-readonly"],className:re()("ReactVirtualized__Grid",i),id:c,onScroll:this._onScroll,role:f,style:oe()({},y,h),tabIndex:d}),w.length>0&&n.createElement("div",{className:"ReactVirtualized__Grid__innerScrollContainer",role:l,style:oe()({width:t?"auto":_,height:O,maxWidth:_,maxHeight:O,overflow:"hidden",pointerEvents:b?"none":"",position:"relative"},u)},w),x&&p())}},{key:"_calculateChildrenToRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,o=e.cellRenderer,n=e.cellRangeRenderer,r=e.columnCount,i=e.deferredMeasurementCache,a=e.height,l=e.overscanColumnCount,u=e.overscanIndicesGetter,s=e.overscanRowCount,c=e.rowCount,p=e.width,f=e.isScrollingOptOut,h=t.scrollDirectionHorizontal,d=t.scrollDirectionVertical,m=t.instanceProps,g=this._initialScrollTop>0?this._initialScrollTop:t.scrollTop,v=this._initialScrollLeft>0?this._initialScrollLeft:t.scrollLeft,j=this._isScrolling(e,t);if(this._childrenToDisplay=[],a>0&&p>0){var b=m.columnSizeAndPositionManager.getVisibleCellRange({containerSize:p,offset:v}),y=m.rowSizeAndPositionManager.getVisibleCellRange({containerSize:a,offset:g}),_=m.columnSizeAndPositionManager.getOffsetAdjustment({containerSize:p,offset:v}),O=m.rowSizeAndPositionManager.getOffsetAdjustment({containerSize:a,offset:g});this._renderedColumnStartIndex=b.start,this._renderedColumnStopIndex=b.stop,this._renderedRowStartIndex=y.start,this._renderedRowStopIndex=y.stop;var C=u({direction:"horizontal",cellCount:r,overscanCellsCount:l,scrollDirection:h,startIndex:"number"==typeof b.start?b.start:0,stopIndex:"number"==typeof b.stop?b.stop:-1}),S=u({direction:"vertical",cellCount:c,overscanCellsCount:s,scrollDirection:d,startIndex:"number"==typeof y.start?y.start:0,stopIndex:"number"==typeof y.stop?y.stop:-1}),w=C.overscanStartIndex,x=C.overscanStopIndex,k=S.overscanStartIndex,E=S.overscanStopIndex;if(i){if(!i.hasFixedHeight())for(var I=k;I<=E;I++)if(!i.has(I,0)){w=0,x=r-1;break}if(!i.hasFixedWidth())for(var L=w;L<=x;L++)if(!i.has(0,L)){k=0,E=c-1;break}}this._childrenToDisplay=n({cellCache:this._cellCache,cellRenderer:o,columnSizeAndPositionManager:m.columnSizeAndPositionManager,columnStartIndex:w,columnStopIndex:x,deferredMeasurementCache:i,horizontalOffsetAdjustment:_,isScrolling:j,isScrollingOptOut:f,parent:this,rowSizeAndPositionManager:m.rowSizeAndPositionManager,rowStartIndex:k,rowStopIndex:E,scrollLeft:v,scrollTop:g,styleCache:this._styleCache,verticalOffsetAdjustment:O,visibleColumnIndices:b,visibleRowIndices:y}),this._columnStartIndex=w,this._columnStopIndex=x,this._rowStartIndex=k,this._rowStopIndex=E}}},{key:"_debounceScrollEnded",value:function(){var e=this.props.scrollingResetTimeInterval;this._disablePointerEventsTimeoutId&&we(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=xe(this._debounceScrollEndedCallback,e)}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex&&"number"==typeof this._deferredInvalidateRowIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t})}}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,o=e.scrollLeft,n=e.scrollTop,r=e.totalColumnsWidth,i=e.totalRowsHeight;this._onScrollMemoizer({callback:function(e){var o=e.scrollLeft,n=e.scrollTop,a=t.props,l=a.height;(0,a.onScroll)({clientHeight:l,clientWidth:a.width,scrollHeight:i,scrollLeft:o,scrollTop:n,scrollWidth:r})},indices:{scrollLeft:o,scrollTop:n}})}},{key:"_isScrolling",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return Object.hasOwnProperty.call(e,"isScrolling")?Boolean(e.isScrolling):Boolean(t.isScrolling)}},{key:"_maybeCallOnScrollbarPresenceChange",value:function(){if(this._scrollbarPresenceChanged){var e=this.props.onScrollbarPresenceChange;this._scrollbarPresenceChanged=!1,e({horizontal:this._horizontalScrollBarSize>0,size:this.state.instanceProps.scrollbarSize,vertical:this._verticalScrollBarSize>0})}}},{key:"scrollToPosition",value:function(e){var o=e.scrollLeft,n=e.scrollTop,r=t._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:o,scrollTop:n});r&&(r.needToResetStyleCache=!1,this.setState(r))}},{key:"_getCalculatedScrollLeft",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return t._getCalculatedScrollLeft(e,o)}},{key:"_updateScrollLeftForScrollToColumn",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,n=t._getScrollLeftForScrollToColumnStateUpdate(e,o);n&&(n.needToResetStyleCache=!1,this.setState(n))}},{key:"_getCalculatedScrollTop",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return t._getCalculatedScrollTop(e,o)}},{key:"_resetStyleCache",value:function(){var e=this._styleCache,t=this._cellCache,o=this.props.isScrollingOptOut;this._cellCache={},this._styleCache={};for(var n=this._rowStartIndex;n<=this._rowStopIndex;n++)for(var r=this._columnStartIndex;r<=this._columnStopIndex;r++){var i=n+"-"+r;this._styleCache[i]=e[i],o&&(this._cellCache[i]=t[i])}}},{key:"_updateScrollTopForScrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,n=t._getScrollTopForScrollToRowStateUpdate(e,o);n&&(n.needToResetStyleCache=!1,this.setState(n))}}],[{key:"getDerivedStateFromProps",value:function(e,o){var n={};0===e.columnCount&&0!==o.scrollLeft||0===e.rowCount&&0!==o.scrollTop?(n.scrollLeft=0,n.scrollTop=0):(e.scrollLeft!==o.scrollLeft&&e.scrollToColumn<0||e.scrollTop!==o.scrollTop&&e.scrollToRow<0)&&ee()(n,t._getScrollToPositionStateUpdate({prevState:o,scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}));var r=o.instanceProps;n.needToResetStyleCache=!1,e.columnWidth===r.prevColumnWidth&&e.rowHeight===r.prevRowHeight||(n.needToResetStyleCache=!0),r.columnSizeAndPositionManager.configure({cellCount:e.columnCount,estimatedCellSize:t._getEstimatedColumnSize(e),cellSizeGetter:t._wrapSizeGetter(e.columnWidth)}),r.rowSizeAndPositionManager.configure({cellCount:e.rowCount,estimatedCellSize:t._getEstimatedRowSize(e),cellSizeGetter:t._wrapSizeGetter(e.rowHeight)}),0!==r.prevColumnCount&&0!==r.prevRowCount||(r.prevColumnCount=0,r.prevRowCount=0),e.autoHeight&&!1===e.isScrolling&&!0===r.prevIsScrolling&&ee()(n,{isScrolling:!1});var i=void 0,a=void 0;return ie({cellCount:r.prevColumnCount,cellSize:"number"==typeof r.prevColumnWidth?r.prevColumnWidth:null,computeMetadataCallback:function(){return r.columnSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.columnCount,nextCellSize:"number"==typeof e.columnWidth?e.columnWidth:null,nextScrollToIndex:e.scrollToColumn,scrollToIndex:r.prevScrollToColumn,updateScrollOffsetForScrollToIndex:function(){i=t._getScrollLeftForScrollToColumnStateUpdate(e,o)}}),ie({cellCount:r.prevRowCount,cellSize:"number"==typeof r.prevRowHeight?r.prevRowHeight:null,computeMetadataCallback:function(){return r.rowSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.rowCount,nextCellSize:"number"==typeof e.rowHeight?e.rowHeight:null,nextScrollToIndex:e.scrollToRow,scrollToIndex:r.prevScrollToRow,updateScrollOffsetForScrollToIndex:function(){a=t._getScrollTopForScrollToRowStateUpdate(e,o)}}),r.prevColumnCount=e.columnCount,r.prevColumnWidth=e.columnWidth,r.prevIsScrolling=!0===e.isScrolling,r.prevRowCount=e.rowCount,r.prevRowHeight=e.rowHeight,r.prevScrollToColumn=e.scrollToColumn,r.prevScrollToRow=e.scrollToRow,r.scrollbarSize=e.getScrollbarSize(),void 0===r.scrollbarSize?(r.scrollbarSizeMeasured=!1,r.scrollbarSize=0):r.scrollbarSizeMeasured=!0,n.instanceProps=r,oe()({},n,i,a)}},{key:"_getEstimatedColumnSize",value:function(e){return"number"==typeof e.columnWidth?e.columnWidth:e.estimatedColumnSize}},{key:"_getEstimatedRowSize",value:function(e){return"number"==typeof e.rowHeight?e.rowHeight:e.estimatedRowSize}},{key:"_getScrollToPositionStateUpdate",value:function(e){var t=e.prevState,o=e.scrollLeft,n=e.scrollTop,r={scrollPositionChangeReason:Ee};return"number"==typeof o&&o>=0&&(r.scrollDirectionHorizontal=o>t.scrollLeft?de:-1,r.scrollLeft=o),"number"==typeof n&&n>=0&&(r.scrollDirectionVertical=n>t.scrollTop?de:-1,r.scrollTop=n),"number"==typeof o&&o>=0&&o!==t.scrollLeft||"number"==typeof n&&n>=0&&n!==t.scrollTop?r:null}},{key:"_wrapSizeGetter",value:function(e){return"function"==typeof e?e:function(){return e}}},{key:"_getCalculatedScrollLeft",value:function(e,t){var o=e.columnCount,n=e.height,r=e.scrollToAlignment,i=e.scrollToColumn,a=e.width,l=t.scrollLeft,u=t.instanceProps;if(o>0){var s=o-1,c=i<0?s:Math.min(s,i),p=u.rowSizeAndPositionManager.getTotalSize(),f=u.scrollbarSizeMeasured&&p>n?u.scrollbarSize:0;return u.columnSizeAndPositionManager.getUpdatedOffsetForIndex({align:r,containerSize:a-f,currentOffset:l,targetIndex:c})}return 0}},{key:"_getScrollLeftForScrollToColumnStateUpdate",value:function(e,o){var n=o.scrollLeft,r=t._getCalculatedScrollLeft(e,o);return"number"==typeof r&&r>=0&&n!==r?t._getScrollToPositionStateUpdate({prevState:o,scrollLeft:r,scrollTop:-1}):null}},{key:"_getCalculatedScrollTop",value:function(e,t){var o=e.height,n=e.rowCount,r=e.scrollToAlignment,i=e.scrollToRow,a=e.width,l=t.scrollTop,u=t.instanceProps;if(n>0){var s=n-1,c=i<0?s:Math.min(s,i),p=u.columnSizeAndPositionManager.getTotalSize(),f=u.scrollbarSizeMeasured&&p>a?u.scrollbarSize:0;return u.rowSizeAndPositionManager.getUpdatedOffsetForIndex({align:r,containerSize:o-f,currentOffset:l,targetIndex:c})}return 0}},{key:"_getScrollTopForScrollToRowStateUpdate",value:function(e,o){var n=o.scrollTop,r=t._getCalculatedScrollTop(e,o);return"number"==typeof r&&r>=0&&n!==r?t._getScrollToPositionStateUpdate({prevState:o,scrollLeft:-1,scrollTop:r}):null}}]),t}(n.PureComponent);Ie.defaultProps={"aria-label":"grid","aria-readonly":!0,autoContainerWidth:!1,autoHeight:!1,autoWidth:!1,cellRangeRenderer:function(e){for(var t=e.cellCache,o=e.cellRenderer,n=e.columnSizeAndPositionManager,r=e.columnStartIndex,i=e.columnStopIndex,a=e.deferredMeasurementCache,l=e.horizontalOffsetAdjustment,u=e.isScrolling,s=e.isScrollingOptOut,c=e.parent,p=e.rowSizeAndPositionManager,f=e.rowStartIndex,h=e.rowStopIndex,d=e.styleCache,m=e.verticalOffsetAdjustment,g=e.visibleColumnIndices,v=e.visibleRowIndices,j=[],b=n.areOffsetsAdjusted()||p.areOffsetsAdjusted(),y=!u&&!b,_=f;_<=h;_++)for(var O=p.getSizeAndPositionOfCell(_),C=r;C<=i;C++){var S=n.getSizeAndPositionOfCell(C),w=C>=g.start&&C<=g.stop&&_>=v.start&&_<=v.stop,x=_+"-"+C,k=void 0;y&&d[x]?k=d[x]:a&&!a.has(_,C)?k={height:"auto",left:0,position:"absolute",top:0,width:"auto"}:(k={height:O.size,left:S.offset+l,position:"absolute",top:O.offset+m,width:S.size},d[x]=k);var E={columnIndex:C,isScrolling:u,isVisible:w,key:x,parent:c,rowIndex:_,style:k},I=void 0;!s&&!u||l||m?I=o(E):(t[x]||(t[x]=o(E)),I=t[x]),null!=I&&!1!==I&&j.push(I)}return j},containerRole:"rowgroup",containerStyle:{},estimatedColumnSize:100,estimatedRowSize:30,getScrollbarSize:ve.a,noContentRenderer:function(){return null},onScroll:function(){},onScrollbarPresenceChange:function(){},onSectionRendered:function(){},overscanColumnCount:0,overscanIndicesGetter:function(e){var t=e.cellCount,o=e.overscanCellsCount,n=e.scrollDirection,r=e.startIndex,i=e.stopIndex;return n===de?{overscanStartIndex:Math.max(0,r),overscanStopIndex:Math.min(t-1,i+o)}:{overscanStartIndex:Math.max(0,r-o),overscanStopIndex:Math.min(t-1,i)}},overscanRowCount:10,role:"grid",scrollingResetTimeInterval:150,scrollToAlignment:"auto",scrollToColumn:-1,scrollToRow:-1,style:{},tabIndex:0,isScrollingOptOut:!1},Ie.propTypes=null,X(Ie);var Le=Ie,Re=1;function Te(e){var t=e.cellCount,o=e.overscanCellsCount,n=e.scrollDirection,r=e.startIndex,i=e.stopIndex;return o=Math.max(1,o),n===Re?{overscanStartIndex:Math.max(0,r-1),overscanStopIndex:Math.min(t-1,i+o)}:{overscanStartIndex:Math.max(0,r-o),overscanStopIndex:Math.min(t-1,i+1)}}var Me=function(e){function t(){var e,o,n,r;G()(this,t);for(var i=arguments.length,a=Array(i),l=0;l<i;l++)a[l]=arguments[l];return o=n=V()(this,(e=t.__proto__||H()(t)).call.apply(e,[this].concat(a))),n.state={scrollToColumn:0,scrollToRow:0},n._columnStartIndex=0,n._columnStopIndex=0,n._rowStartIndex=0,n._rowStopIndex=0,n._onKeyDown=function(e){var t=n.props,o=t.columnCount,r=t.disabled,i=t.mode,a=t.rowCount;if(!r){var l=n._getScrollState(),u=l.scrollToColumn,s=l.scrollToRow,c=n._getScrollState(),p=c.scrollToColumn,f=c.scrollToRow;switch(e.key){case"ArrowDown":f="cells"===i?Math.min(f+1,a-1):Math.min(n._rowStopIndex+1,a-1);break;case"ArrowLeft":p="cells"===i?Math.max(p-1,0):Math.max(n._columnStartIndex-1,0);break;case"ArrowRight":p="cells"===i?Math.min(p+1,o-1):Math.min(n._columnStopIndex+1,o-1);break;case"ArrowUp":f="cells"===i?Math.max(f-1,0):Math.max(n._rowStartIndex-1,0)}p===u&&f===s||(e.preventDefault(),n._updateScrollState({scrollToColumn:p,scrollToRow:f}))}},n._onSectionRendered=function(e){var t=e.columnStartIndex,o=e.columnStopIndex,r=e.rowStartIndex,i=e.rowStopIndex;n._columnStartIndex=t,n._columnStopIndex=o,n._rowStartIndex=r,n._rowStopIndex=i},r=o,V()(n,r)}return Z()(t,e),B()(t,[{key:"setScrollIndexes",value:function(e){var t=e.scrollToColumn,o=e.scrollToRow;this.setState({scrollToRow:o,scrollToColumn:t})}},{key:"render",value:function(){var e=this.props,t=e.className,o=e.children,r=this._getScrollState(),i=r.scrollToColumn,a=r.scrollToRow;return n.createElement("div",{className:t,onKeyDown:this._onKeyDown},o({onSectionRendered:this._onSectionRendered,scrollToColumn:i,scrollToRow:a}))}},{key:"_getScrollState",value:function(){return this.props.isControlled?this.props:this.state}},{key:"_updateScrollState",value:function(e){var t=e.scrollToColumn,o=e.scrollToRow,n=this.props,r=n.isControlled,i=n.onScrollToChange;"function"==typeof i&&i({scrollToColumn:t,scrollToRow:o}),r||this.setState({scrollToColumn:t,scrollToRow:o})}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.isControlled?null:e.scrollToColumn!==t.scrollToColumn||e.scrollToRow!==t.scrollToRow?{scrollToColumn:e.scrollToColumn,scrollToRow:e.scrollToRow}:null}}]),t}(n.PureComponent);Me.defaultProps={disabled:!1,isControlled:!1,mode:"edges",scrollToColumn:0,scrollToRow:0},Me.propTypes=null,X(Me);var Ae=o(613),ze=function(e){function t(){var e,o,n,r;G()(this,t);for(var i=arguments.length,a=Array(i),l=0;l<i;l++)a[l]=arguments[l];return o=n=V()(this,(e=t.__proto__||H()(t)).call.apply(e,[this].concat(a))),n.state={height:n.props.defaultHeight||0,width:n.props.defaultWidth||0},n._onResize=function(){var e=n.props,t=e.disableHeight,o=e.disableWidth,r=e.onResize;if(n._parentNode){var i=n._parentNode.offsetHeight||0,a=n._parentNode.offsetWidth||0,l=window.getComputedStyle(n._parentNode)||{},u=parseInt(l.paddingLeft,10)||0,s=parseInt(l.paddingRight,10)||0,c=parseInt(l.paddingTop,10)||0,p=parseInt(l.paddingBottom,10)||0,f=i-c-p,h=a-u-s;(!t&&n.state.height!==f||!o&&n.state.width!==h)&&(n.setState({height:i-c-p,width:a-u-s}),r({height:i,width:a}))}},n._setRef=function(e){n._autoSizer=e},r=o,V()(n,r)}return Z()(t,e),B()(t,[{key:"componentDidMount",value:function(){var e=this.props.nonce;this._autoSizer&&this._autoSizer.parentNode&&this._autoSizer.parentNode.ownerDocument&&this._autoSizer.parentNode.ownerDocument.defaultView&&this._autoSizer.parentNode instanceof this._autoSizer.parentNode.ownerDocument.defaultView.HTMLElement&&(this._parentNode=this._autoSizer.parentNode,this._detectElementResize=Object(Ae.a)(e),this._detectElementResize.addResizeListener(this._parentNode,this._onResize),this._onResize())}},{key:"componentWillUnmount",value:function(){this._detectElementResize&&this._parentNode&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize)}},{key:"render",value:function(){var e=this.props,t=e.children,o=e.className,r=e.disableHeight,i=e.disableWidth,a=e.style,l=this.state,u=l.height,s=l.width,c={overflow:"visible"},p={};return r||(c.height=0,p.height=u),i||(c.width=0,p.width=s),n.createElement("div",{className:o,ref:this._setRef,style:oe()({},c,a)},t(p))}}]),t}(n.PureComponent);ze.defaultProps={onResize:function(){},disableHeight:!1,disableWidth:!1,style:{}},ze.propTypes=null;var Ne=ze,Pe=o(44),De=function(e){function t(){var e,o,n,r;G()(this,t);for(var i=arguments.length,a=Array(i),l=0;l<i;l++)a[l]=arguments[l];return o=n=V()(this,(e=t.__proto__||H()(t)).call.apply(e,[this].concat(a))),n._measure=function(){var e=n.props,t=e.cache,o=e.columnIndex,r=void 0===o?0:o,i=e.parent,a=e.rowIndex,l=void 0===a?n.props.index||0:a,u=n._getCellMeasurements(),s=u.height,c=u.width;s===t.getHeight(l,r)&&c===t.getWidth(l,r)||(t.set(l,r,c,s),i&&"function"==typeof i.recomputeGridSize&&i.recomputeGridSize({columnIndex:r,rowIndex:l}))},r=o,V()(n,r)}return Z()(t,e),B()(t,[{key:"componentDidMount",value:function(){this._maybeMeasureCell()}},{key:"componentDidUpdate",value:function(){this._maybeMeasureCell()}},{key:"render",value:function(){var e=this.props.children;return"function"==typeof e?e({measure:this._measure}):e}},{key:"_getCellMeasurements",value:function(){var e=this.props.cache,t=Object(Pe.findDOMNode)(this);if(t&&t.ownerDocument&&t.ownerDocument.defaultView&&t instanceof t.ownerDocument.defaultView.HTMLElement){var o=t.style.width,n=t.style.height;e.hasFixedWidth()||(t.style.width="auto"),e.hasFixedHeight()||(t.style.height="auto");var r=Math.ceil(t.offsetHeight),i=Math.ceil(t.offsetWidth);return o&&(t.style.width=o),n&&(t.style.height=n),{height:r,width:i}}return{height:0,width:0}}},{key:"_maybeMeasureCell",value:function(){var e=this.props,t=e.cache,o=e.columnIndex,n=void 0===o?0:o,r=e.parent,i=e.rowIndex,a=void 0===i?this.props.index||0:i;if(!t.has(a,n)){var l=this._getCellMeasurements(),u=l.height,s=l.width;t.set(a,n,s,u),r&&"function"==typeof r.invalidateCellSizeAfterRender&&r.invalidateCellSizeAfterRender({columnIndex:n,rowIndex:a})}}}]),t}(n.PureComponent);De.__internalCellMeasurerFlag=!1,De.propTypes=null;var Fe=30,He=100;!function(){function e(){var t=this,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};G()(this,e),this._cellHeightCache={},this._cellWidthCache={},this._columnWidthCache={},this._rowHeightCache={},this._columnCount=0,this._rowCount=0,this.columnWidth=function(e){var o=e.index,n=t._keyMapper(0,o);return t._columnWidthCache.hasOwnProperty(n)?t._columnWidthCache[n]:t._defaultWidth},this.rowHeight=function(e){var o=e.index,n=t._keyMapper(o,0);return t._rowHeightCache.hasOwnProperty(n)?t._rowHeightCache[n]:t._defaultHeight};var n=o.defaultHeight,r=o.defaultWidth,i=o.fixedHeight,a=o.fixedWidth,l=o.keyMapper,u=o.minHeight,s=o.minWidth;this._hasFixedHeight=!0===i,this._hasFixedWidth=!0===a,this._minHeight=u||0,this._minWidth=s||0,this._keyMapper=l||We,this._defaultHeight=Math.max(this._minHeight,"number"==typeof n?n:Fe),this._defaultWidth=Math.max(this._minWidth,"number"==typeof r?r:He)}B()(e,[{key:"clear",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=this._keyMapper(e,t);delete this._cellHeightCache[o],delete this._cellWidthCache[o],this._updateCachedColumnAndRowSizes(e,t)}},{key:"clearAll",value:function(){this._cellHeightCache={},this._cellWidthCache={},this._columnWidthCache={},this._rowHeightCache={},this._rowCount=0,this._columnCount=0}},{key:"hasFixedHeight",value:function(){return this._hasFixedHeight}},{key:"hasFixedWidth",value:function(){return this._hasFixedWidth}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedHeight)return this._defaultHeight;var o=this._keyMapper(e,t);return this._cellHeightCache.hasOwnProperty(o)?Math.max(this._minHeight,this._cellHeightCache[o]):this._defaultHeight}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedWidth)return this._defaultWidth;var o=this._keyMapper(e,t);return this._cellWidthCache.hasOwnProperty(o)?Math.max(this._minWidth,this._cellWidthCache[o]):this._defaultWidth}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=this._keyMapper(e,t);return this._cellHeightCache.hasOwnProperty(o)}},{key:"set",value:function(e,t,o,n){var r=this._keyMapper(e,t);t>=this._columnCount&&(this._columnCount=t+1),e>=this._rowCount&&(this._rowCount=e+1),this._cellHeightCache[r]=n,this._cellWidthCache[r]=o,this._updateCachedColumnAndRowSizes(e,t)}},{key:"_updateCachedColumnAndRowSizes",value:function(e,t){if(!this._hasFixedWidth){for(var o=0,n=0;n<this._rowCount;n++)o=Math.max(o,this.getWidth(n,t));var r=this._keyMapper(0,t);this._columnWidthCache[r]=o}if(!this._hasFixedHeight){for(var i=0,a=0;a<this._columnCount;a++)i=Math.max(i,this.getHeight(e,a));var l=this._keyMapper(e,0);this._rowHeightCache[l]=i}}},{key:"defaultHeight",get:function(){return this._defaultHeight}},{key:"defaultWidth",get:function(){return this._defaultWidth}}])}();function We(e,t){return e+"-"+t}var Ge={OBSERVED:"observed",REQUESTED:"requested"},qe=function(e){function t(){var e;G()(this,t);for(var o=arguments.length,n=Array(o),r=0;r<o;r++)n[r]=arguments[r];var i=V()(this,(e=t.__proto__||H()(t)).call.apply(e,[this].concat(n)));return i.state={isScrolling:!1,scrollLeft:0,scrollTop:0},i._calculateSizeAndPositionDataOnNextUpdate=!1,i._onSectionRenderedMemoizer=he(),i._onScrollMemoizer=he(!1),i._invokeOnSectionRenderedHelper=function(){var e=i.props,t=e.cellLayoutManager,o=e.onSectionRendered;i._onSectionRenderedMemoizer({callback:o,indices:{indices:t.getLastRenderedIndices()}})},i._setScrollingContainerRef=function(e){i._scrollingContainer=e},i._updateScrollPositionForScrollToCell=function(){var e=i.props,t=e.cellLayoutManager,o=e.height,n=e.scrollToAlignment,r=e.scrollToCell,a=e.width,l=i.state,u=l.scrollLeft,s=l.scrollTop;if(r>=0){var c=t.getScrollPositionForCell({align:n,cellIndex:r,height:o,scrollLeft:u,scrollTop:s,width:a});c.scrollLeft===u&&c.scrollTop===s||i._setScrollPosition(c)}},i._onScroll=function(e){if(e.target===i._scrollingContainer){i._enablePointerEventsAfterDelay();var t=i.props,o=t.cellLayoutManager,n=t.height,r=t.isScrollingChange,a=t.width,l=i._scrollbarSize,u=o.getTotalSize(),s=u.height,c=u.width,p=Math.max(0,Math.min(c-a+l,e.target.scrollLeft)),f=Math.max(0,Math.min(s-n+l,e.target.scrollTop));if(i.state.scrollLeft!==p||i.state.scrollTop!==f){var h=e.cancelable?Ge.OBSERVED:Ge.REQUESTED;i.state.isScrolling||r(!0),i.setState({isScrolling:!0,scrollLeft:p,scrollPositionChangeReason:h,scrollTop:f})}i._invokeOnScrollMemoizer({scrollLeft:p,scrollTop:f,totalWidth:c,totalHeight:s})}},i._scrollbarSize=ve()(),void 0===i._scrollbarSize?(i._scrollbarSizeMeasured=!1,i._scrollbarSize=0):i._scrollbarSizeMeasured=!0,i}return Z()(t,e),B()(t,[{key:"recomputeCellSizesAndPositions",value:function(){this._calculateSizeAndPositionDataOnNextUpdate=!0,this.forceUpdate()}},{key:"componentDidMount",value:function(){var e=this.props,t=e.cellLayoutManager,o=e.scrollLeft,n=e.scrollToCell,r=e.scrollTop;this._scrollbarSizeMeasured||(this._scrollbarSize=ve()(),this._scrollbarSizeMeasured=!0,this.setState({})),n>=0?this._updateScrollPositionForScrollToCell():(o>=0||r>=0)&&this._setScrollPosition({scrollLeft:o,scrollTop:r}),this._invokeOnSectionRenderedHelper();var i=t.getTotalSize(),a=i.height,l=i.width;this._invokeOnScrollMemoizer({scrollLeft:o||0,scrollTop:r||0,totalHeight:a,totalWidth:l})}},{key:"componentDidUpdate",value:function(e,t){var o=this.props,n=o.height,r=o.scrollToAlignment,i=o.scrollToCell,a=o.width,l=this.state,u=l.scrollLeft,s=l.scrollPositionChangeReason,c=l.scrollTop;s===Ge.REQUESTED&&(u>=0&&u!==t.scrollLeft&&u!==this._scrollingContainer.scrollLeft&&(this._scrollingContainer.scrollLeft=u),c>=0&&c!==t.scrollTop&&c!==this._scrollingContainer.scrollTop&&(this._scrollingContainer.scrollTop=c)),n===e.height&&r===e.scrollToAlignment&&i===e.scrollToCell&&a===e.width||this._updateScrollPositionForScrollToCell(),this._invokeOnSectionRenderedHelper()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,t=e.autoHeight,o=e.cellCount,r=e.cellLayoutManager,i=e.className,a=e.height,l=e.horizontalOverscanSize,u=e.id,s=e.noContentRenderer,c=e.style,p=e.verticalOverscanSize,f=e.width,h=this.state,d=h.isScrolling,m=h.scrollLeft,g=h.scrollTop;(this._lastRenderedCellCount!==o||this._lastRenderedCellLayoutManager!==r||this._calculateSizeAndPositionDataOnNextUpdate)&&(this._lastRenderedCellCount=o,this._lastRenderedCellLayoutManager=r,this._calculateSizeAndPositionDataOnNextUpdate=!1,r.calculateSizeAndPositionData());var v=r.getTotalSize(),j=v.height,b=v.width,y=Math.max(0,m-l),_=Math.max(0,g-p),O=Math.min(b,m+f+l),C=Math.min(j,g+a+p),S=a>0&&f>0?r.cellRenderers({height:C-_,isScrolling:d,width:O-y,x:y,y:_}):[],w={boxSizing:"border-box",direction:"ltr",height:t?"auto":a,position:"relative",WebkitOverflowScrolling:"touch",width:f,willChange:"transform"},x=j>a?this._scrollbarSize:0,k=b>f?this._scrollbarSize:0;return w.overflowX=b+x<=f?"hidden":"auto",w.overflowY=j+k<=a?"hidden":"auto",n.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:re()("ReactVirtualized__Collection",i),id:u,onScroll:this._onScroll,role:"grid",style:oe()({},w,c),tabIndex:0},o>0&&n.createElement("div",{className:"ReactVirtualized__Collection__innerScrollContainer",style:{height:j,maxHeight:j,maxWidth:b,overflow:"hidden",pointerEvents:d?"none":"",width:b}},S),0===o&&s())}},{key:"_enablePointerEventsAfterDelay",value:function(){var e=this;this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=setTimeout(function(){(0,e.props.isScrollingChange)(!1),e._disablePointerEventsTimeoutId=null,e.setState({isScrolling:!1})},150)}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,o=e.scrollLeft,n=e.scrollTop,r=e.totalHeight,i=e.totalWidth;this._onScrollMemoizer({callback:function(e){var o=e.scrollLeft,n=e.scrollTop,a=t.props,l=a.height;(0,a.onScroll)({clientHeight:l,clientWidth:a.width,scrollHeight:r,scrollLeft:o,scrollTop:n,scrollWidth:i})},indices:{scrollLeft:o,scrollTop:n}})}},{key:"_setScrollPosition",value:function(e){var t=e.scrollLeft,o=e.scrollTop,n={scrollPositionChangeReason:Ge.REQUESTED};t>=0&&(n.scrollLeft=t),o>=0&&(n.scrollTop=o),(t>=0&&t!==this.state.scrollLeft||o>=0&&o!==this.state.scrollTop)&&this.setState(n)}}],[{key:"getDerivedStateFromProps",value:function(e,t){return 0!==e.cellCount||0===t.scrollLeft&&0===t.scrollTop?e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop?e.scrollTop:t.scrollTop}:null:{scrollLeft:0,scrollTop:0}}}]),t}(n.PureComponent);qe.defaultProps={"aria-label":"grid",horizontalOverscanSize:0,noContentRenderer:function(){return null},onScroll:function(){return null},onSectionRendered:function(){return null},scrollToAlignment:"auto",scrollToCell:-1,style:{},verticalOverscanSize:0},qe.propTypes={},X(qe);var Be=qe,Ue=function(){function e(t){var o=t.height,n=t.width,r=t.x,i=t.y;G()(this,e),this.height=o,this.width=n,this.x=r,this.y=i,this._indexMap={},this._indices=[]}return B()(e,[{key:"addCellIndex",value:function(e){var t=e.index;this._indexMap[t]||(this._indexMap[t]=!0,this._indices.push(t))}},{key:"getCellIndices",value:function(){return this._indices}},{key:"toString",value:function(){return this.x+","+this.y+" "+this.width+"x"+this.height}}]),e}(),Ve=100,Ke=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ve;G()(this,e),this._sectionSize=t,this._cellMetadata=[],this._sections={}}return B()(e,[{key:"getCellIndices",value:function(e){var t=e.height,o=e.width,n=e.x,r=e.y,i={};return this.getSections({height:t,width:o,x:n,y:r}).forEach(function(e){return e.getCellIndices().forEach(function(e){i[e]=e})}),fe()(i).map(function(e){return i[e]})}},{key:"getCellMetadata",value:function(e){var t=e.index;return this._cellMetadata[t]}},{key:"getSections",value:function(e){for(var t=e.height,o=e.width,n=e.x,r=e.y,i=Math.floor(n/this._sectionSize),a=Math.floor((n+o-1)/this._sectionSize),l=Math.floor(r/this._sectionSize),u=Math.floor((r+t-1)/this._sectionSize),s=[],c=i;c<=a;c++)for(var p=l;p<=u;p++){var f=c+"."+p;this._sections[f]||(this._sections[f]=new Ue({height:this._sectionSize,width:this._sectionSize,x:c*this._sectionSize,y:p*this._sectionSize})),s.push(this._sections[f])}return s}},{key:"getTotalSectionCount",value:function(){return fe()(this._sections).length}},{key:"toString",value:function(){var e=this;return fe()(this._sections).map(function(t){return e._sections[t].toString()})}},{key:"registerCell",value:function(e){var t=e.cellMetadatum,o=e.index;this._cellMetadata[o]=t,this.getSections(t).forEach(function(e){return e.addCellIndex({index:o})})}}]),e}();function Ze(e){var t=e.align,o=void 0===t?"auto":t,n=e.cellOffset,r=e.cellSize,i=e.containerSize,a=e.currentOffset,l=n,u=l-i+r;switch(o){case"start":return l;case"end":return u;case"center":return l-(i-r)/2;default:return Math.max(u,Math.min(l,a))}}var $e=function(e){function t(e,o){G()(this,t);var n=V()(this,(t.__proto__||H()(t)).call(this,e,o));return n._cellMetadata=[],n._lastRenderedCellIndices=[],n._cellCache=[],n._isScrollingChange=n._isScrollingChange.bind(n),n._setCollectionViewRef=n._setCollectionViewRef.bind(n),n}return Z()(t,e),B()(t,[{key:"forceUpdate",value:function(){void 0!==this._collectionView&&this._collectionView.forceUpdate()}},{key:"recomputeCellSizesAndPositions",value:function(){this._cellCache=[],this._collectionView.recomputeCellSizesAndPositions()}},{key:"render",value:function(){var e=le()(this.props,[]);return n.createElement(Be,oe()({cellLayoutManager:this,isScrollingChange:this._isScrollingChange,ref:this._setCollectionViewRef},e))}},{key:"calculateSizeAndPositionData",value:function(){var e=this.props,t=function(e){for(var t=e.cellCount,o=e.cellSizeAndPositionGetter,n=e.sectionSize,r=[],i=new Ke(n),a=0,l=0,u=0;u<t;u++){var s=o({index:u});if(null==s.height||isNaN(s.height)||null==s.width||isNaN(s.width)||null==s.x||isNaN(s.x)||null==s.y||isNaN(s.y))throw Error("Invalid metadata returned for cell "+u+":\n        x:"+s.x+", y:"+s.y+", width:"+s.width+", height:"+s.height);a=Math.max(a,s.y+s.height),l=Math.max(l,s.x+s.width),r[u]=s,i.registerCell({cellMetadatum:s,index:u})}return{cellMetadata:r,height:a,sectionManager:i,width:l}}({cellCount:e.cellCount,cellSizeAndPositionGetter:e.cellSizeAndPositionGetter,sectionSize:e.sectionSize});this._cellMetadata=t.cellMetadata,this._sectionManager=t.sectionManager,this._height=t.height,this._width=t.width}},{key:"getLastRenderedIndices",value:function(){return this._lastRenderedCellIndices}},{key:"getScrollPositionForCell",value:function(e){var t=e.align,o=e.cellIndex,n=e.height,r=e.scrollLeft,i=e.scrollTop,a=e.width,l=this.props.cellCount;if(o>=0&&o<l){var u=this._cellMetadata[o];r=Ze({align:t,cellOffset:u.x,cellSize:u.width,containerSize:a,currentOffset:r,targetIndex:o}),i=Ze({align:t,cellOffset:u.y,cellSize:u.height,containerSize:n,currentOffset:i,targetIndex:o})}return{scrollLeft:r,scrollTop:i}}},{key:"getTotalSize",value:function(){return{height:this._height,width:this._width}}},{key:"cellRenderers",value:function(e){var t=this,o=e.height,n=e.isScrolling,r=e.width,i=e.x,a=e.y,l=this.props,u=l.cellGroupRenderer,s=l.cellRenderer;return this._lastRenderedCellIndices=this._sectionManager.getCellIndices({height:o,width:r,x:i,y:a}),u({cellCache:this._cellCache,cellRenderer:s,cellSizeAndPositionGetter:function(e){var o=e.index;return t._sectionManager.getCellMetadata({index:o})},indices:this._lastRenderedCellIndices,isScrolling:n})}},{key:"_isScrollingChange",value:function(e){e||(this._cellCache=[])}},{key:"_setCollectionViewRef",value:function(e){this._collectionView=e}}]),t}(n.PureComponent);$e.defaultProps={"aria-label":"grid",cellGroupRenderer:function(e){var t=e.cellCache,o=e.cellRenderer,n=e.cellSizeAndPositionGetter,r=e.indices,i=e.isScrolling;return r.map(function(e){var r=n({index:e}),a={index:e,isScrolling:i,key:e,style:{height:r.height,left:r.x,position:"absolute",top:r.y,width:r.width}};return i?(e in t||(t[e]=o(a)),t[e]):o(a)}).filter(function(e){return!!e})}};$e.propTypes={};var Ye=function(e){function t(e,o){G()(this,t);var n=V()(this,(t.__proto__||H()(t)).call(this,e,o));return n._registerChild=n._registerChild.bind(n),n}return Z()(t,e),B()(t,[{key:"componentDidUpdate",value:function(e){var t=this.props,o=t.columnMaxWidth,n=t.columnMinWidth,r=t.columnCount,i=t.width;o===e.columnMaxWidth&&n===e.columnMinWidth&&r===e.columnCount&&i===e.width||this._registeredChild&&this._registeredChild.recomputeGridSize()}},{key:"render",value:function(){var e=this.props,t=e.children,o=e.columnMaxWidth,n=e.columnMinWidth,r=e.columnCount,i=e.width,a=n||1,l=o?Math.min(o,i):i,u=i/r;return u=Math.max(a,u),u=Math.min(l,u),u=Math.floor(u),t({adjustedWidth:Math.min(i,u*r),columnWidth:u,getColumnWidth:function(){return u},registerChild:this._registerChild})}},{key:"_registerChild",value:function(e){if(e&&"function"!=typeof e.recomputeGridSize)throw Error("Unexpected child type registered; only Grid/MultiGrid children are supported.");this._registeredChild=e,this._registeredChild&&this._registeredChild.recomputeGridSize()}}]),t}(n.PureComponent);Ye.propTypes={};var Je=function(e){function t(e,o){G()(this,t);var n=V()(this,(t.__proto__||H()(t)).call(this,e,o));return n._loadMoreRowsMemoizer=he(),n._onRowsRendered=n._onRowsRendered.bind(n),n._registerChild=n._registerChild.bind(n),n}return Z()(t,e),B()(t,[{key:"resetLoadMoreRowsCache",value:function(e){this._loadMoreRowsMemoizer=he(),e&&this._doStuff(this._lastRenderedStartIndex,this._lastRenderedStopIndex)}},{key:"render",value:function(){return(0,this.props.children)({onRowsRendered:this._onRowsRendered,registerChild:this._registerChild})}},{key:"_loadUnloadedRanges",value:function(e){var t=this,o=this.props.loadMoreRows;e.forEach(function(e){var n=o(e);n&&n.then(function(){var o,n,r,i,a;o={lastRenderedStartIndex:t._lastRenderedStartIndex,lastRenderedStopIndex:t._lastRenderedStopIndex,startIndex:e.startIndex,stopIndex:e.stopIndex},n=o.lastRenderedStartIndex,r=o.lastRenderedStopIndex,i=o.startIndex,a=o.stopIndex,i>r||a<n||t._registeredChild&&function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o="function"==typeof e.recomputeGridSize?e.recomputeGridSize:e.recomputeRowHeights;o?o.call(e,t):e.forceUpdate()}(t._registeredChild,t._lastRenderedStartIndex)})})}},{key:"_onRowsRendered",value:function(e){var t=e.startIndex,o=e.stopIndex;this._lastRenderedStartIndex=t,this._lastRenderedStopIndex=o,this._doStuff(t,o)}},{key:"_doStuff",value:function(e,t){var o=this,n=this.props,r=n.isRowLoaded,i=n.minimumBatchSize,a=n.rowCount,l=n.threshold,u=function(e){for(var t=e.isRowLoaded,o=e.minimumBatchSize,n=e.rowCount,r=e.startIndex,i=e.stopIndex,a=[],l=null,u=null,s=r;s<=i;s++){var c=t({index:s});c?null!==u&&(a.push({startIndex:l,stopIndex:u}),l=u=null):(u=s,null===l&&(l=s))}if(null!==u){for(var p=Math.min(Math.max(u,l+o-1),n-1),f=u+1;f<=p&&!t({index:f});f++)u=f;a.push({startIndex:l,stopIndex:u})}if(a.length)for(var h=a[0];h.stopIndex-h.startIndex+1<o&&h.startIndex>0;){var d=h.startIndex-1;if(t({index:d}))break;h.startIndex=d}return a}({isRowLoaded:r,minimumBatchSize:i,rowCount:a,startIndex:Math.max(0,e-l),stopIndex:Math.min(a-1,t+l)}),s=u.reduce(function(e,t){return e.concat([t.startIndex,t.stopIndex])},[]);this._loadMoreRowsMemoizer({callback:function(){o._loadUnloadedRanges(u)},indices:{squashedUnloadedRanges:s}})}},{key:"_registerChild",value:function(e){this._registeredChild=e}}]),t}(n.PureComponent);Je.defaultProps={minimumBatchSize:10,rowCount:0,threshold:15};Je.propTypes={};var Xe=o(755),Qe=o.n(Xe),et=function(e){function t(){var e,o,n,r;G()(this,t);for(var i=arguments.length,a=Array(i),l=0;l<i;l++)a[l]=arguments[l];return o=n=V()(this,(e=t.__proto__||H()(t)).call.apply(e,[this].concat(a))),n._cellRenderer=function(e){var t=e.parent,o=e.rowIndex,r=e.style,i=e.isScrolling,a=e.isVisible,l=e.key,u=n.props.rowRenderer;return Qe()(r,"width").writable&&(r.width="100%"),u({index:o,style:r,isScrolling:i,isVisible:a,key:l,parent:t})},n._setRef=function(e){n.Grid=e},n._onScroll=function(e){var t=e.clientHeight,o=e.scrollHeight,r=e.scrollTop;(0,n.props.onScroll)({clientHeight:t,scrollHeight:o,scrollTop:r})},n._onSectionRendered=function(e){var t=e.rowOverscanStartIndex,o=e.rowOverscanStopIndex,r=e.rowStartIndex,i=e.rowStopIndex;(0,n.props.onRowsRendered)({overscanStartIndex:t,overscanStopIndex:o,startIndex:r,stopIndex:i})},r=o,V()(n,r)}return Z()(t,e),B()(t,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,o=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:o,columnIndex:0}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,o=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:o,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,n=e.rowIndex,r=void 0===n?0:n;this.Grid&&this.Grid.recomputeGridSize({rowIndex:r,columnIndex:o})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e,columnIndex:0})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"render",value:function(){var e=this.props,t=e.className,o=e.noRowsRenderer,r=e.scrollToIndex,i=e.width,a=re()("ReactVirtualized__List",t);return n.createElement(Le,oe()({},this.props,{autoContainerWidth:!0,cellRenderer:this._cellRenderer,className:a,columnWidth:i,columnCount:1,noContentRenderer:o,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,scrollToRow:r}))}}]),t}(n.PureComponent);et.defaultProps={autoHeight:!1,estimatedRowSize:30,onScroll:function(){},noRowsRenderer:function(){return null},onRowsRendered:function(){},overscanIndicesGetter:Te,overscanRowCount:10,scrollToAlignment:"auto",scrollToIndex:-1,style:{}},et.propTypes=null;var tt=o(758),ot=o.n(tt),nt=o(759),rt=o.n(nt);var it={ge:function(e,t,o,n,r){return"function"==typeof o?function(e,t,o,n,r){for(var i=o+1;t<=o;){var a=t+o>>>1;r(e[a],n)>=0?(i=a,o=a-1):t=a+1}return i}(e,void 0===n?0:0|n,void 0===r?e.length-1:0|r,t,o):function(e,t,o,n){for(var r=o+1;t<=o;){var i=t+o>>>1;e[i]>=n?(r=i,o=i-1):t=i+1}return r}(e,void 0===o?0:0|o,void 0===n?e.length-1:0|n,t)},gt:function(e,t,o,n,r){return"function"==typeof o?function(e,t,o,n,r){for(var i=o+1;t<=o;){var a=t+o>>>1;r(e[a],n)>0?(i=a,o=a-1):t=a+1}return i}(e,void 0===n?0:0|n,void 0===r?e.length-1:0|r,t,o):function(e,t,o,n){for(var r=o+1;t<=o;){var i=t+o>>>1;e[i]>n?(r=i,o=i-1):t=i+1}return r}(e,void 0===o?0:0|o,void 0===n?e.length-1:0|n,t)},lt:function(e,t,o,n,r){return"function"==typeof o?function(e,t,o,n,r){for(var i=t-1;t<=o;){var a=t+o>>>1;r(e[a],n)<0?(i=a,t=a+1):o=a-1}return i}(e,void 0===n?0:0|n,void 0===r?e.length-1:0|r,t,o):function(e,t,o,n){for(var r=t-1;t<=o;){var i=t+o>>>1;e[i]<n?(r=i,t=i+1):o=i-1}return r}(e,void 0===o?0:0|o,void 0===n?e.length-1:0|n,t)},le:function(e,t,o,n,r){return"function"==typeof o?function(e,t,o,n,r){for(var i=t-1;t<=o;){var a=t+o>>>1;r(e[a],n)<=0?(i=a,t=a+1):o=a-1}return i}(e,void 0===n?0:0|n,void 0===r?e.length-1:0|r,t,o):function(e,t,o,n){for(var r=t-1;t<=o;){var i=t+o>>>1;e[i]<=n?(r=i,t=i+1):o=i-1}return r}(e,void 0===o?0:0|o,void 0===n?e.length-1:0|n,t)},eq:function(e,t,o,n,r){return"function"==typeof o?function(e,t,o,n,r){for(;t<=o;){var i=t+o>>>1,a=r(e[i],n);if(0===a)return i;a<=0?t=i+1:o=i-1}return-1}(e,void 0===n?0:0|n,void 0===r?e.length-1:0|r,t,o):function(e,t,o,n){for(;t<=o;){var r=t+o>>>1,i=e[r];if(i===n)return r;i<=n?t=r+1:o=r-1}return-1}(e,void 0===o?0:0|o,void 0===n?e.length-1:0|n,t)}},at=0,lt=1;function ut(e,t,o,n,r){this.mid=e,this.left=t,this.right=o,this.leftPoints=n,this.rightPoints=r,this.count=(t?t.count:0)+(o?o.count:0)+n.length}var st=ut.prototype;function ct(e,t){e.mid=t.mid,e.left=t.left,e.right=t.right,e.leftPoints=t.leftPoints,e.rightPoints=t.rightPoints,e.count=t.count}function pt(e,t){var o=yt(t);e.mid=o.mid,e.left=o.left,e.right=o.right,e.leftPoints=o.leftPoints,e.rightPoints=o.rightPoints,e.count=o.count}function ft(e,t){var o=e.intervals([]);o.push(t),pt(e,o)}function ht(e,t){var o=e.intervals([]),n=o.indexOf(t);return n<0?at:(o.splice(n,1),pt(e,o),lt)}function dt(e,t,o){for(var n=0;n<e.length&&e[n][0]<=t;++n){var r=o(e[n]);if(r)return r}}function mt(e,t,o){for(var n=e.length-1;n>=0&&e[n][1]>=t;--n){var r=o(e[n]);if(r)return r}}function gt(e,t){for(var o=0;o<e.length;++o){var n=t(e[o]);if(n)return n}}function vt(e,t){return e-t}function jt(e,t){var o=e[0]-t[0];return o||e[1]-t[1]}function bt(e,t){var o=e[1]-t[1];return o||e[0]-t[0]}function yt(e){if(0===e.length)return null;for(var t=[],o=0;o<e.length;++o)t.push(e[o][0],e[o][1]);t.sort(vt);var n=t[t.length>>1],r=[],i=[],a=[];for(o=0;o<e.length;++o){var l=e[o];l[1]<n?r.push(l):n<l[0]?i.push(l):a.push(l)}var u=a,s=a.slice();return u.sort(jt),s.sort(bt),new ut(n,yt(r),yt(i),u,s)}function _t(e){this.root=e}st.intervals=function(e){return e.push.apply(e,this.leftPoints),this.left&&this.left.intervals(e),this.right&&this.right.intervals(e),e},st.insert=function(e){var t=this.count-this.leftPoints.length;if(this.count+=1,e[1]<this.mid)this.left?4*(this.left.count+1)>3*(t+1)?ft(this,e):this.left.insert(e):this.left=yt([e]);else if(e[0]>this.mid)this.right?4*(this.right.count+1)>3*(t+1)?ft(this,e):this.right.insert(e):this.right=yt([e]);else{var o=it.ge(this.leftPoints,e,jt),n=it.ge(this.rightPoints,e,bt);this.leftPoints.splice(o,0,e),this.rightPoints.splice(n,0,e)}},st.remove=function(e){var t=this.count-this.leftPoints;if(e[1]<this.mid)return this.left?4*(this.right?this.right.count:0)>3*(t-1)?ht(this,e):2===(i=this.left.remove(e))?(this.left=null,this.count-=1,lt):(i===lt&&(this.count-=1),i):at;if(e[0]>this.mid)return this.right?4*(this.left?this.left.count:0)>3*(t-1)?ht(this,e):2===(i=this.right.remove(e))?(this.right=null,this.count-=1,lt):(i===lt&&(this.count-=1),i):at;if(1===this.count)return this.leftPoints[0]===e?2:at;if(1===this.leftPoints.length&&this.leftPoints[0]===e){if(this.left&&this.right){for(var o=this,n=this.left;n.right;)o=n,n=n.right;if(o===this)n.right=this.right;else{var r=this.left,i=this.right;o.count-=n.count,o.right=n.left,n.left=r,n.right=i}ct(this,n),this.count=(this.left?this.left.count:0)+(this.right?this.right.count:0)+this.leftPoints.length}else this.left?ct(this,this.left):ct(this,this.right);return lt}for(r=it.ge(this.leftPoints,e,jt);r<this.leftPoints.length&&this.leftPoints[r][0]===e[0];++r)if(this.leftPoints[r]===e){this.count-=1,this.leftPoints.splice(r,1);for(i=it.ge(this.rightPoints,e,bt);i<this.rightPoints.length&&this.rightPoints[i][1]===e[1];++i)if(this.rightPoints[i]===e)return this.rightPoints.splice(i,1),lt}return at},st.queryPoint=function(e,t){if(e<this.mid){if(this.left)if(o=this.left.queryPoint(e,t))return o;return dt(this.leftPoints,e,t)}if(e>this.mid){var o;if(this.right)if(o=this.right.queryPoint(e,t))return o;return mt(this.rightPoints,e,t)}return gt(this.leftPoints,t)},st.queryInterval=function(e,t,o){var n;if(e<this.mid&&this.left&&(n=this.left.queryInterval(e,t,o)))return n;if(t>this.mid&&this.right&&(n=this.right.queryInterval(e,t,o)))return n;return t<this.mid?dt(this.leftPoints,t,o):e>this.mid?mt(this.rightPoints,e,o):gt(this.leftPoints,o)};var Ot=_t.prototype;Ot.insert=function(e){this.root?this.root.insert(e):this.root=new ut(e[0],null,null,[e],[e])},Ot.remove=function(e){if(this.root){var t=this.root.remove(e);return 2===t&&(this.root=null),t!==at}return!1},Ot.queryPoint=function(e,t){if(this.root)return this.root.queryPoint(e,t)},Ot.queryInterval=function(e,t,o){if(e<=t&&this.root)return this.root.queryInterval(e,t,o)},Object.defineProperty(Ot,"count",{get:function(){return this.root?this.root.count:0}}),Object.defineProperty(Ot,"intervals",{get:function(){return this.root?this.root.intervals([]):[]}});var Ct=function(){function e(){var t;G()(this,e),this._columnSizeMap={},this._intervalTree=t&&0!==t.length?new _t(yt(t)):new _t(null),this._leftMap={}}return B()(e,[{key:"estimateTotalHeight",value:function(e,t,o){var n=e-this.count;return this.tallestColumnSize+Math.ceil(n/t)*o}},{key:"range",value:function(e,t,o){var n=this;this._intervalTree.queryInterval(e,e+t,function(e){var t=rt()(e,3),r=t[0],i=(t[1],t[2]);return o(i,n._leftMap[i],r)})}},{key:"setPosition",value:function(e,t,o,n){this._intervalTree.insert([o,o+n,e]),this._leftMap[e]=t;var r=this._columnSizeMap,i=r[t];r[t]=void 0===i?o+n:Math.max(i,o+n)}},{key:"count",get:function(){return this._intervalTree.count}},{key:"shortestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var o in e){var n=e[o];t=0===t?n:Math.min(t,n)}return t}},{key:"tallestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var o in e){var n=e[o];t=Math.max(t,n)}return t}}]),e}(),St=function(e){function t(){var e,o,n,r;G()(this,t);for(var i=arguments.length,a=Array(i),l=0;l<i;l++)a[l]=arguments[l];return o=n=V()(this,(e=t.__proto__||H()(t)).call.apply(e,[this].concat(a))),n.state={isScrolling:!1,scrollTop:0},n._invalidateOnUpdateStartIndex=null,n._invalidateOnUpdateStopIndex=null,n._positionCache=new Ct,n._startIndex=null,n._startIndexMemoized=null,n._stopIndex=null,n._stopIndexMemoized=null,n._debounceResetIsScrollingCallback=function(){n.setState({isScrolling:!1})},n._setScrollingContainerRef=function(e){n._scrollingContainer=e},n._onScroll=function(e){var t=n.props.height,o=e.target.scrollTop,r=Math.min(Math.max(0,n._getEstimatedTotalHeight()-t),o);o===r&&(n._debounceResetIsScrolling(),n.state.scrollTop!==r&&n.setState({isScrolling:!0,scrollTop:r}))},r=o,V()(n,r)}return Z()(t,e),B()(t,[{key:"clearCellPositions",value:function(){this._positionCache=new Ct,this.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.rowIndex;null===this._invalidateOnUpdateStartIndex?(this._invalidateOnUpdateStartIndex=t,this._invalidateOnUpdateStopIndex=t):(this._invalidateOnUpdateStartIndex=Math.min(this._invalidateOnUpdateStartIndex,t),this._invalidateOnUpdateStopIndex=Math.max(this._invalidateOnUpdateStopIndex,t))}},{key:"recomputeCellPositions",value:function(){var e=this._positionCache.count-1;this._positionCache=new Ct,this._populatePositionCache(0,e),this.forceUpdate()}},{key:"componentDidMount",value:function(){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback()}},{key:"componentDidUpdate",value:function(e,t){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback(),this.props.scrollTop!==e.scrollTop&&this._debounceResetIsScrolling()}},{key:"componentWillUnmount",value:function(){this._debounceResetIsScrollingId&&we(this._debounceResetIsScrollingId)}},{key:"render",value:function(){var e=this,t=this.props,o=t.autoHeight,r=t.cellCount,i=t.cellMeasurerCache,a=t.cellRenderer,l=t.className,u=t.height,s=t.id,c=t.keyMapper,p=t.overscanByPixels,f=t.role,h=t.style,d=t.tabIndex,m=t.width,g=t.rowDirection,v=this.state,j=v.isScrolling,b=v.scrollTop,y=[],_=this._getEstimatedTotalHeight(),O=this._positionCache.shortestColumnSize,C=this._positionCache.count,S=0,w=void 0;if(this._positionCache.range(Math.max(0,b-p),u+2*p,function(t,o,n){var r;void 0===w?(S=t,w=t):(S=Math.min(S,t),w=Math.max(w,t)),y.push(a({index:t,isScrolling:j,key:c(t),parent:e,style:(r={height:i.getHeight(t)},ot()(r,"ltr"===g?"left":"right",o),ot()(r,"position","absolute"),ot()(r,"top",n),ot()(r,"width",i.getWidth(t)),r)}))}),O<b+u+p&&C<r)for(var x=Math.min(r-C,Math.ceil((b+u+p-O)/i.defaultHeight*m/i.defaultWidth)),k=C;k<C+x;k++)w=k,y.push(a({index:k,isScrolling:j,key:c(k),parent:this,style:{width:i.getWidth(k)}}));return this._startIndex=S,this._stopIndex=w,n.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:re()("ReactVirtualized__Masonry",l),id:s,onScroll:this._onScroll,role:f,style:oe()({boxSizing:"border-box",direction:"ltr",height:o?"auto":u,overflowX:"hidden",overflowY:_<u?"hidden":"auto",position:"relative",width:m,WebkitOverflowScrolling:"touch",willChange:"transform"},h),tabIndex:d},n.createElement("div",{className:"ReactVirtualized__Masonry__innerScrollContainer",style:{width:"100%",height:_,maxWidth:"100%",maxHeight:_,overflow:"hidden",pointerEvents:j?"none":"",position:"relative"}},y))}},{key:"_checkInvalidateOnUpdate",value:function(){if("number"==typeof this._invalidateOnUpdateStartIndex){var e=this._invalidateOnUpdateStartIndex,t=this._invalidateOnUpdateStopIndex;this._invalidateOnUpdateStartIndex=null,this._invalidateOnUpdateStopIndex=null,this._populatePositionCache(e,t),this.forceUpdate()}}},{key:"_debounceResetIsScrolling",value:function(){var e=this.props.scrollingResetTimeInterval;this._debounceResetIsScrollingId&&we(this._debounceResetIsScrollingId),this._debounceResetIsScrollingId=xe(this._debounceResetIsScrollingCallback,e)}},{key:"_getEstimatedTotalHeight",value:function(){var e=this.props,t=e.cellCount,o=e.cellMeasurerCache,n=e.width,r=Math.max(1,Math.floor(n/o.defaultWidth));return this._positionCache.estimateTotalHeight(t,r,o.defaultHeight)}},{key:"_invokeOnScrollCallback",value:function(){var e=this.props,t=e.height,o=e.onScroll,n=this.state.scrollTop;this._onScrollMemoized!==n&&(o({clientHeight:t,scrollHeight:this._getEstimatedTotalHeight(),scrollTop:n}),this._onScrollMemoized=n)}},{key:"_invokeOnCellsRenderedCallback",value:function(){this._startIndexMemoized===this._startIndex&&this._stopIndexMemoized===this._stopIndex||((0,this.props.onCellsRendered)({startIndex:this._startIndex,stopIndex:this._stopIndex}),this._startIndexMemoized=this._startIndex,this._stopIndexMemoized=this._stopIndex)}},{key:"_populatePositionCache",value:function(e,t){for(var o=this.props,n=o.cellMeasurerCache,r=o.cellPositioner,i=e;i<=t;i++){var a=r(i),l=a.left,u=a.top;this._positionCache.setPosition(i,l,u,n.getHeight(i))}}}],[{key:"getDerivedStateFromProps",value:function(e,t){return void 0!==e.scrollTop&&t.scrollTop!==e.scrollTop?{isScrolling:!0,scrollTop:e.scrollTop}:null}}]),t}(n.PureComponent);function wt(){}St.defaultProps={autoHeight:!1,keyMapper:function(e){return e},onCellsRendered:wt,onScroll:wt,overscanByPixels:20,role:"grid",scrollingResetTimeInterval:150,style:{},tabIndex:0,rowDirection:"ltr"},St.propTypes=null;X(St);var xt=function(){function e(){var t=this,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};G()(this,e),this.columnWidth=function(e){var o=e.index;t._cellMeasurerCache.columnWidth({index:o+t._columnIndexOffset})},this.rowHeight=function(e){var o=e.index;t._cellMeasurerCache.rowHeight({index:o+t._rowIndexOffset})};var n=o.cellMeasurerCache,r=o.columnIndexOffset,i=void 0===r?0:r,a=o.rowIndexOffset,l=void 0===a?0:a;this._cellMeasurerCache=n,this._columnIndexOffset=i,this._rowIndexOffset=l}return B()(e,[{key:"clear",value:function(e,t){this._cellMeasurerCache.clear(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"clearAll",value:function(){this._cellMeasurerCache.clearAll()}},{key:"hasFixedHeight",value:function(){return this._cellMeasurerCache.hasFixedHeight()}},{key:"hasFixedWidth",value:function(){return this._cellMeasurerCache.hasFixedWidth()}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getHeight(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getWidth(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.has(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"set",value:function(e,t,o,n){this._cellMeasurerCache.set(e+this._rowIndexOffset,t+this._columnIndexOffset,o,n)}},{key:"defaultHeight",get:function(){return this._cellMeasurerCache.defaultHeight}},{key:"defaultWidth",get:function(){return this._cellMeasurerCache.defaultWidth}}]),e}(),kt=function(e){function t(e,o){G()(this,t);var n=V()(this,(t.__proto__||H()(t)).call(this,e,o));Et.call(n);var r=e.deferredMeasurementCache,i=e.fixedColumnCount,a=e.fixedRowCount;return n._maybeCalculateCachedStyles(!0),r&&(n._deferredMeasurementCacheBottomLeftGrid=a>0?new xt({cellMeasurerCache:r,columnIndexOffset:0,rowIndexOffset:a}):r,n._deferredMeasurementCacheBottomRightGrid=i>0||a>0?new xt({cellMeasurerCache:r,columnIndexOffset:i,rowIndexOffset:a}):r,n._deferredMeasurementCacheTopRightGrid=i>0?new xt({cellMeasurerCache:r,columnIndexOffset:i,rowIndexOffset:0}):r),n}return Z()(t,e),B()(t,[{key:"forceUpdateGrids",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.forceUpdate(),this._bottomRightGrid&&this._bottomRightGrid.forceUpdate(),this._topLeftGrid&&this._topLeftGrid.forceUpdate(),this._topRightGrid&&this._topRightGrid.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,n=e.rowIndex,r=void 0===n?0:n;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,o):o,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,r):r}},{key:"measureAllCells",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.measureAllCells(),this._bottomRightGrid&&this._bottomRightGrid.measureAllCells(),this._topLeftGrid&&this._topLeftGrid.measureAllCells(),this._topRightGrid&&this._topRightGrid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,n=e.rowIndex,r=void 0===n?0:n,i=this.props,a=i.fixedColumnCount,l=i.fixedRowCount,u=Math.max(0,o-a),s=Math.max(0,r-l);this._bottomLeftGrid&&this._bottomLeftGrid.recomputeGridSize({columnIndex:o,rowIndex:s}),this._bottomRightGrid&&this._bottomRightGrid.recomputeGridSize({columnIndex:u,rowIndex:s}),this._topLeftGrid&&this._topLeftGrid.recomputeGridSize({columnIndex:o,rowIndex:r}),this._topRightGrid&&this._topRightGrid.recomputeGridSize({columnIndex:u,rowIndex:r}),this._leftGridWidth=null,this._topGridHeight=null,this._maybeCalculateCachedStyles(!0)}},{key:"componentDidMount",value:function(){var e=this.props,t=e.scrollLeft,o=e.scrollTop;if(t>0||o>0){var n={};t>0&&(n.scrollLeft=t),o>0&&(n.scrollTop=o),this.setState(n)}this._handleInvalidatedGridSize()}},{key:"componentDidUpdate",value:function(){this._handleInvalidatedGridSize()}},{key:"render",value:function(){var e=this.props,t=e.onScroll,o=e.onSectionRendered,r=(e.onScrollbarPresenceChange,e.scrollLeft,e.scrollToColumn),i=(e.scrollTop,e.scrollToRow),a=le()(e,["onScroll","onSectionRendered","onScrollbarPresenceChange","scrollLeft","scrollToColumn","scrollTop","scrollToRow"]);if(this._prepareForRender(),0===this.props.width||0===this.props.height)return null;var l=this.state,u=l.scrollLeft,s=l.scrollTop;return n.createElement("div",{style:this._containerOuterStyle},n.createElement("div",{style:this._containerTopStyle},this._renderTopLeftGrid(a),this._renderTopRightGrid(oe()({},a,{onScroll:t,scrollLeft:u}))),n.createElement("div",{style:this._containerBottomStyle},this._renderBottomLeftGrid(oe()({},a,{onScroll:t,scrollTop:s})),this._renderBottomRightGrid(oe()({},a,{onScroll:t,onSectionRendered:o,scrollLeft:u,scrollToColumn:r,scrollToRow:i,scrollTop:s}))))}},{key:"_getBottomGridHeight",value:function(e){return e.height-this._getTopGridHeight(e)}},{key:"_getLeftGridWidth",value:function(e){var t=e.fixedColumnCount,o=e.columnWidth;if(null==this._leftGridWidth)if("function"==typeof o){for(var n=0,r=0;r<t;r++)n+=o({index:r});this._leftGridWidth=n}else this._leftGridWidth=o*t;return this._leftGridWidth}},{key:"_getRightGridWidth",value:function(e){return e.width-this._getLeftGridWidth(e)}},{key:"_getTopGridHeight",value:function(e){var t=e.fixedRowCount,o=e.rowHeight;if(null==this._topGridHeight)if("function"==typeof o){for(var n=0,r=0;r<t;r++)n+=o({index:r});this._topGridHeight=n}else this._topGridHeight=o*t;return this._topGridHeight}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t}),this.forceUpdate()}}},{key:"_maybeCalculateCachedStyles",value:function(e){var t=this.props,o=t.columnWidth,n=t.enableFixedColumnScroll,r=t.enableFixedRowScroll,i=t.height,a=t.fixedColumnCount,l=t.fixedRowCount,u=t.rowHeight,s=t.style,c=t.styleBottomLeftGrid,p=t.styleBottomRightGrid,f=t.styleTopLeftGrid,h=t.styleTopRightGrid,d=t.width,m=e||i!==this._lastRenderedHeight||d!==this._lastRenderedWidth,g=e||o!==this._lastRenderedColumnWidth||a!==this._lastRenderedFixedColumnCount,v=e||l!==this._lastRenderedFixedRowCount||u!==this._lastRenderedRowHeight;(e||m||s!==this._lastRenderedStyle)&&(this._containerOuterStyle=oe()({height:i,overflow:"visible",width:d},s)),(e||m||v)&&(this._containerTopStyle={height:this._getTopGridHeight(this.props),position:"relative",width:d},this._containerBottomStyle={height:i-this._getTopGridHeight(this.props),overflow:"visible",position:"relative",width:d}),(e||c!==this._lastRenderedStyleBottomLeftGrid)&&(this._bottomLeftGridStyle=oe()({left:0,overflowX:"hidden",overflowY:n?"auto":"hidden",position:"absolute"},c)),(e||g||p!==this._lastRenderedStyleBottomRightGrid)&&(this._bottomRightGridStyle=oe()({left:this._getLeftGridWidth(this.props),position:"absolute"},p)),(e||f!==this._lastRenderedStyleTopLeftGrid)&&(this._topLeftGridStyle=oe()({left:0,overflowX:"hidden",overflowY:"hidden",position:"absolute",top:0},f)),(e||g||h!==this._lastRenderedStyleTopRightGrid)&&(this._topRightGridStyle=oe()({left:this._getLeftGridWidth(this.props),overflowX:r?"auto":"hidden",overflowY:"hidden",position:"absolute",top:0},h)),this._lastRenderedColumnWidth=o,this._lastRenderedFixedColumnCount=a,this._lastRenderedFixedRowCount=l,this._lastRenderedHeight=i,this._lastRenderedRowHeight=u,this._lastRenderedStyle=s,this._lastRenderedStyleBottomLeftGrid=c,this._lastRenderedStyleBottomRightGrid=p,this._lastRenderedStyleTopLeftGrid=f,this._lastRenderedStyleTopRightGrid=h,this._lastRenderedWidth=d}},{key:"_prepareForRender",value:function(){this._lastRenderedColumnWidth===this.props.columnWidth&&this._lastRenderedFixedColumnCount===this.props.fixedColumnCount||(this._leftGridWidth=null),this._lastRenderedFixedRowCount===this.props.fixedRowCount&&this._lastRenderedRowHeight===this.props.rowHeight||(this._topGridHeight=null),this._maybeCalculateCachedStyles(),this._lastRenderedColumnWidth=this.props.columnWidth,this._lastRenderedFixedColumnCount=this.props.fixedColumnCount,this._lastRenderedFixedRowCount=this.props.fixedRowCount,this._lastRenderedRowHeight=this.props.rowHeight}},{key:"_renderBottomLeftGrid",value:function(e){var t=e.enableFixedColumnScroll,o=e.fixedColumnCount,r=e.fixedRowCount,i=e.rowCount,a=e.hideBottomLeftGridScrollbar,l=this.state.showVerticalScrollbar;if(!o)return null;var u=l?1:0,s=this._getBottomGridHeight(e),c=this._getLeftGridWidth(e),p=this.state.showVerticalScrollbar?this.state.scrollbarSize:0,f=a?c+p:c,h=n.createElement(Le,oe()({},e,{cellRenderer:this._cellRendererBottomLeftGrid,className:this.props.classNameBottomLeftGrid,columnCount:o,deferredMeasurementCache:this._deferredMeasurementCacheBottomLeftGrid,height:s,onScroll:t?this._onScrollTop:void 0,ref:this._bottomLeftGridRef,rowCount:Math.max(0,i-r)+u,rowHeight:this._rowHeightBottomGrid,style:this._bottomLeftGridStyle,tabIndex:null,width:f}));return a?n.createElement("div",{className:"BottomLeftGrid_ScrollWrapper",style:oe()({},this._bottomLeftGridStyle,{height:s,width:c,overflowY:"hidden"})},h):h}},{key:"_renderBottomRightGrid",value:function(e){var t=e.columnCount,o=e.fixedColumnCount,r=e.fixedRowCount,i=e.rowCount,a=e.scrollToColumn,l=e.scrollToRow;return n.createElement(Le,oe()({},e,{cellRenderer:this._cellRendererBottomRightGrid,className:this.props.classNameBottomRightGrid,columnCount:Math.max(0,t-o),columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheBottomRightGrid,height:this._getBottomGridHeight(e),onScroll:this._onScroll,onScrollbarPresenceChange:this._onScrollbarPresenceChange,ref:this._bottomRightGridRef,rowCount:Math.max(0,i-r),rowHeight:this._rowHeightBottomGrid,scrollToColumn:a-o,scrollToRow:l-r,style:this._bottomRightGridStyle,width:this._getRightGridWidth(e)}))}},{key:"_renderTopLeftGrid",value:function(e){var t=e.fixedColumnCount,o=e.fixedRowCount;return t&&o?n.createElement(Le,oe()({},e,{className:this.props.classNameTopLeftGrid,columnCount:t,height:this._getTopGridHeight(e),ref:this._topLeftGridRef,rowCount:o,style:this._topLeftGridStyle,tabIndex:null,width:this._getLeftGridWidth(e)})):null}},{key:"_renderTopRightGrid",value:function(e){var t=e.columnCount,o=e.enableFixedRowScroll,r=e.fixedColumnCount,i=e.fixedRowCount,a=e.scrollLeft,l=e.hideTopRightGridScrollbar,u=this.state,s=u.showHorizontalScrollbar,c=u.scrollbarSize;if(!i)return null;var p=s?1:0,f=this._getTopGridHeight(e),h=this._getRightGridWidth(e),d=s?c:0,m=f,g=this._topRightGridStyle;l&&(m=f+d,g=oe()({},this._topRightGridStyle,{left:0}));var v=n.createElement(Le,oe()({},e,{cellRenderer:this._cellRendererTopRightGrid,className:this.props.classNameTopRightGrid,columnCount:Math.max(0,t-r)+p,columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheTopRightGrid,height:m,onScroll:o?this._onScrollLeft:void 0,ref:this._topRightGridRef,rowCount:i,scrollLeft:a,style:g,tabIndex:null,width:h}));return l?n.createElement("div",{className:"TopRightGrid_ScrollWrapper",style:oe()({},this._topRightGridStyle,{height:f,width:h,overflowX:"hidden"})},v):v}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft&&e.scrollLeft>=0?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop&&e.scrollTop>=0?e.scrollTop:t.scrollTop}:null}}]),t}(n.PureComponent);kt.defaultProps={classNameBottomLeftGrid:"",classNameBottomRightGrid:"",classNameTopLeftGrid:"",classNameTopRightGrid:"",enableFixedColumnScroll:!1,enableFixedRowScroll:!1,fixedColumnCount:0,fixedRowCount:0,scrollToColumn:-1,scrollToRow:-1,style:{},styleBottomLeftGrid:{},styleBottomRightGrid:{},styleTopLeftGrid:{},styleTopRightGrid:{},hideTopRightGridScrollbar:!1,hideBottomLeftGridScrollbar:!1};var Et=function(){var e=this;this.state={scrollLeft:0,scrollTop:0,scrollbarSize:0,showHorizontalScrollbar:!1,showVerticalScrollbar:!1},this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this._bottomLeftGridRef=function(t){e._bottomLeftGrid=t},this._bottomRightGridRef=function(t){e._bottomRightGrid=t},this._cellRendererBottomLeftGrid=function(t){var o=t.rowIndex,r=le()(t,["rowIndex"]),i=e.props,a=i.cellRenderer,l=i.fixedRowCount;return o===i.rowCount-l?n.createElement("div",{key:r.key,style:oe()({},r.style,{height:20})}):a(oe()({},r,{parent:e,rowIndex:o+l}))},this._cellRendererBottomRightGrid=function(t){var o=t.columnIndex,n=t.rowIndex,r=le()(t,["columnIndex","rowIndex"]),i=e.props,a=i.cellRenderer,l=i.fixedColumnCount,u=i.fixedRowCount;return a(oe()({},r,{columnIndex:o+l,parent:e,rowIndex:n+u}))},this._cellRendererTopRightGrid=function(t){var o=t.columnIndex,r=le()(t,["columnIndex"]),i=e.props,a=i.cellRenderer,l=i.columnCount,u=i.fixedColumnCount;return o===l-u?n.createElement("div",{key:r.key,style:oe()({},r.style,{width:20})}):a(oe()({},r,{columnIndex:o+u,parent:e}))},this._columnWidthRightGrid=function(t){var o=t.index,n=e.props,r=n.columnCount,i=n.fixedColumnCount,a=n.columnWidth,l=e.state,u=l.scrollbarSize;return l.showHorizontalScrollbar&&o===r-i?u:"function"==typeof a?a({index:o+i}):a},this._onScroll=function(t){var o=t.scrollLeft,n=t.scrollTop;e.setState({scrollLeft:o,scrollTop:n});var r=e.props.onScroll;r&&r(t)},this._onScrollbarPresenceChange=function(t){var o=t.horizontal,n=t.size,r=t.vertical,i=e.state,a=i.showHorizontalScrollbar,l=i.showVerticalScrollbar;if(o!==a||r!==l){e.setState({scrollbarSize:n,showHorizontalScrollbar:o,showVerticalScrollbar:r});var u=e.props.onScrollbarPresenceChange;"function"==typeof u&&u({horizontal:o,size:n,vertical:r})}},this._onScrollLeft=function(t){var o=t.scrollLeft;e._onScroll({scrollLeft:o,scrollTop:e.state.scrollTop})},this._onScrollTop=function(t){var o=t.scrollTop;e._onScroll({scrollTop:o,scrollLeft:e.state.scrollLeft})},this._rowHeightBottomGrid=function(t){var o=t.index,n=e.props,r=n.fixedRowCount,i=n.rowCount,a=n.rowHeight,l=e.state,u=l.scrollbarSize;return l.showVerticalScrollbar&&o===i-r?u:"function"==typeof a?a({index:o+r}):a},this._topLeftGridRef=function(t){e._topLeftGrid=t},this._topRightGridRef=function(t){e._topRightGrid=t}};kt.propTypes={},X(kt);var It=function(e){function t(e,o){G()(this,t);var n=V()(this,(t.__proto__||H()(t)).call(this,e,o));return n.state={clientHeight:0,clientWidth:0,scrollHeight:0,scrollLeft:0,scrollTop:0,scrollWidth:0},n._onScroll=n._onScroll.bind(n),n}return Z()(t,e),B()(t,[{key:"render",value:function(){var e=this.props.children,t=this.state,o=t.clientHeight,n=t.clientWidth,r=t.scrollHeight,i=t.scrollLeft,a=t.scrollTop,l=t.scrollWidth;return e({clientHeight:o,clientWidth:n,onScroll:this._onScroll,scrollHeight:r,scrollLeft:i,scrollTop:a,scrollWidth:l})}},{key:"_onScroll",value:function(e){var t=e.clientHeight,o=e.clientWidth,n=e.scrollHeight,r=e.scrollLeft,i=e.scrollTop,a=e.scrollWidth;this.setState({clientHeight:t,clientWidth:o,scrollHeight:n,scrollLeft:r,scrollTop:i,scrollWidth:a})}}]),t}(n.PureComponent);It.propTypes={};function Lt(e){var t=e.className,o=e.columns,r=e.style;return n.createElement("div",{className:t,role:"row",style:r},o)}Lt.propTypes=null;var Rt={ASC:"ASC",DESC:"DESC"};function Tt(e){var t=e.sortDirection,o=re()("ReactVirtualized__Table__sortableHeaderIcon",{"ReactVirtualized__Table__sortableHeaderIcon--ASC":t===Rt.ASC,"ReactVirtualized__Table__sortableHeaderIcon--DESC":t===Rt.DESC});return n.createElement("svg",{className:o,width:18,height:18,viewBox:"0 0 24 24"},t===Rt.ASC?n.createElement("path",{d:"M7 14l5-5 5 5z"}):n.createElement("path",{d:"M7 10l5 5 5-5z"}),n.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}))}function Mt(e){var t=e.dataKey,o=e.label,r=e.sortBy,i=e.sortDirection,a=r===t,l=[n.createElement("span",{className:"ReactVirtualized__Table__headerTruncatedText",key:"label",title:o},o)];return a&&l.push(n.createElement(Tt,{key:"SortIndicator",sortDirection:i})),l}function At(e){var t=e.className,o=e.columns,r=e.index,i=e.key,a=e.onRowClick,l=e.onRowDoubleClick,u=e.onRowMouseOut,s=e.onRowMouseOver,c=e.onRowRightClick,p=e.rowData,f=e.style,h={"aria-rowindex":r+1};return(a||l||u||s||c)&&(h["aria-label"]="row",h.tabIndex=0,a&&(h.onClick=function(e){return a({event:e,index:r,rowData:p})}),l&&(h.onDoubleClick=function(e){return l({event:e,index:r,rowData:p})}),u&&(h.onMouseOut=function(e){return u({event:e,index:r,rowData:p})}),s&&(h.onMouseOver=function(e){return s({event:e,index:r,rowData:p})}),c&&(h.onContextMenu=function(e){return c({event:e,index:r,rowData:p})})),n.createElement("div",oe()({},h,{className:t,key:i,role:"row",style:f}),o)}Tt.propTypes={},Mt.propTypes=null,At.propTypes=null;var zt=function(e){function t(){return G()(this,t),V()(this,(t.__proto__||H()(t)).apply(this,arguments))}return Z()(t,e),t}(n.Component);zt.defaultProps={cellDataGetter:function(e){var t=e.dataKey,o=e.rowData;return"function"==typeof o.get?o.get(t):o[t]},cellRenderer:function(e){var t=e.cellData;return null==t?"":String(t)},defaultSortDirection:Rt.ASC,flexGrow:0,flexShrink:1,headerRenderer:Mt,style:{}};zt.propTypes={};var Nt=function(e){function t(e){G()(this,t);var o=V()(this,(t.__proto__||H()(t)).call(this,e));return o.state={scrollbarWidth:0},o._createColumn=o._createColumn.bind(o),o._createRow=o._createRow.bind(o),o._onScroll=o._onScroll.bind(o),o._onSectionRendered=o._onSectionRendered.bind(o),o._setRef=o._setRef.bind(o),o}return Z()(t,e),B()(t,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,o=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:o}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,o=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:o,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,o=void 0===t?0:t,n=e.rowIndex,r=void 0===n?0:n;this.Grid&&this.Grid.recomputeGridSize({rowIndex:r,columnIndex:o})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"componentDidMount",value:function(){this._setScrollbarWidth()}},{key:"componentDidUpdate",value:function(){this._setScrollbarWidth()}},{key:"render",value:function(){var e=this,t=this.props,o=t.children,r=t.className,i=t.disableHeader,a=t.gridClassName,l=t.gridStyle,u=t.headerHeight,s=t.headerRowRenderer,c=t.height,p=t.id,f=t.noRowsRenderer,h=t.rowClassName,d=t.rowStyle,m=t.scrollToIndex,g=t.style,v=t.width,j=this.state.scrollbarWidth,b=i?c:c-u,y="function"==typeof h?h({index:-1}):h,_="function"==typeof d?d({index:-1}):d;return this._cachedColumnStyles=[],n.Children.toArray(o).forEach(function(t,o){var n=e._getFlexStyleForColumn(t,t.props.style);e._cachedColumnStyles[o]=oe()({},n,{overflow:"hidden"})}),n.createElement("div",{"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-colcount":n.Children.toArray(o).length,"aria-rowcount":this.props.rowCount,className:re()("ReactVirtualized__Table",r),id:p,role:"grid",style:g},!i&&s({className:re()("ReactVirtualized__Table__headerRow",y),columns:this._getHeaderColumns(),style:oe()({height:u,overflow:"hidden",paddingRight:j,width:v},_)}),n.createElement(Le,oe()({},this.props,{autoContainerWidth:!0,className:re()("ReactVirtualized__Table__Grid",a),cellRenderer:this._createRow,columnWidth:v,columnCount:1,height:b,id:void 0,noContentRenderer:f,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,role:"rowgroup",scrollbarWidth:j,scrollToRow:m,style:oe()({},l,{overflowX:"hidden"})})))}},{key:"_createColumn",value:function(e){var t=e.column,o=e.columnIndex,r=e.isScrolling,i=e.parent,a=e.rowData,l=e.rowIndex,u=this.props.onColumnClick,s=t.props,c=s.cellDataGetter,p=s.cellRenderer,f=s.className,h=s.columnData,d=s.dataKey,m=s.id,g=p({cellData:c({columnData:h,dataKey:d,rowData:a}),columnData:h,columnIndex:o,dataKey:d,isScrolling:r,parent:i,rowData:a,rowIndex:l}),v=this._cachedColumnStyles[o],j="string"==typeof g?g:null;return n.createElement("div",{"aria-colindex":o+1,"aria-describedby":m,className:re()("ReactVirtualized__Table__rowColumn",f),key:"Row"+l+"-Col"+o,onClick:function(e){u&&u({columnData:h,dataKey:d,event:e})},role:"gridcell",style:v,title:j},g)}},{key:"_createHeader",value:function(e){var t=e.column,o=e.index,r=this.props,i=r.headerClassName,a=r.headerStyle,l=r.onHeaderClick,u=r.sort,s=r.sortBy,c=r.sortDirection,p=t.props,f=p.columnData,h=p.dataKey,d=p.defaultSortDirection,m=p.disableSort,g=p.headerRenderer,v=p.id,j=p.label,b=!m&&u,y=re()("ReactVirtualized__Table__headerColumn",i,t.props.headerClassName,{ReactVirtualized__Table__sortableHeaderColumn:b}),_=this._getFlexStyleForColumn(t,oe()({},a,t.props.headerStyle)),O=g({columnData:f,dataKey:h,disableSort:m,label:j,sortBy:s,sortDirection:c}),C=void 0,S=void 0,w=void 0,x=void 0,k=void 0;if(b||l){var E=s!==h?d:c===Rt.DESC?Rt.ASC:Rt.DESC,I=function(e){b&&u({defaultSortDirection:d,event:e,sortBy:h,sortDirection:E}),l&&l({columnData:f,dataKey:h,event:e})};k=t.props["aria-label"]||j||h,x="none",w=0,C=I,S=function(e){"Enter"!==e.key&&" "!==e.key||I(e)}}return s===h&&(x=c===Rt.ASC?"ascending":"descending"),n.createElement("div",{"aria-label":k,"aria-sort":x,className:y,id:v,key:"Header-Col"+o,onClick:C,onKeyDown:S,role:"columnheader",style:_,tabIndex:w},O)}},{key:"_createRow",value:function(e){var t=this,o=e.rowIndex,r=e.isScrolling,i=e.key,a=e.parent,l=e.style,u=this.props,s=u.children,c=u.onRowClick,p=u.onRowDoubleClick,f=u.onRowRightClick,h=u.onRowMouseOver,d=u.onRowMouseOut,m=u.rowClassName,g=u.rowGetter,v=u.rowRenderer,j=u.rowStyle,b=this.state.scrollbarWidth,y="function"==typeof m?m({index:o}):m,_="function"==typeof j?j({index:o}):j,O=g({index:o}),C=n.Children.toArray(s).map(function(e,n){return t._createColumn({column:e,columnIndex:n,isScrolling:r,parent:a,rowData:O,rowIndex:o,scrollbarWidth:b})}),S=re()("ReactVirtualized__Table__row",y),w=oe()({},l,{height:this._getRowHeight(o),overflow:"hidden",paddingRight:b},_);return v({className:S,columns:C,index:o,isScrolling:r,key:i,onRowClick:c,onRowDoubleClick:p,onRowRightClick:f,onRowMouseOver:h,onRowMouseOut:d,rowData:O,style:w})}},{key:"_getFlexStyleForColumn",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=e.props.flexGrow+" "+e.props.flexShrink+" "+e.props.width+"px",n=oe()({},t,{flex:o,msFlex:o,WebkitFlex:o});return e.props.maxWidth&&(n.maxWidth=e.props.maxWidth),e.props.minWidth&&(n.minWidth=e.props.minWidth),n}},{key:"_getHeaderColumns",value:function(){var e=this,t=this.props,o=t.children;return(t.disableHeader?[]:n.Children.toArray(o)).map(function(t,o){return e._createHeader({column:t,index:o})})}},{key:"_getRowHeight",value:function(e){var t=this.props.rowHeight;return"function"==typeof t?t({index:e}):t}},{key:"_onScroll",value:function(e){var t=e.clientHeight,o=e.scrollHeight,n=e.scrollTop;(0,this.props.onScroll)({clientHeight:t,scrollHeight:o,scrollTop:n})}},{key:"_onSectionRendered",value:function(e){var t=e.rowOverscanStartIndex,o=e.rowOverscanStopIndex,n=e.rowStartIndex,r=e.rowStopIndex;(0,this.props.onRowsRendered)({overscanStartIndex:t,overscanStopIndex:o,startIndex:n,stopIndex:r})}},{key:"_setRef",value:function(e){this.Grid=e}},{key:"_setScrollbarWidth",value:function(){if(this.Grid){var e=Object(Pe.findDOMNode)(this.Grid),t=e.clientWidth||0,o=(e.offsetWidth||0)-t;this.setState({scrollbarWidth:o})}}}]),t}(n.PureComponent);Nt.defaultProps={disableHeader:!1,estimatedRowSize:30,headerHeight:0,headerStyle:{},noRowsRenderer:function(){return null},onRowsRendered:function(){return null},onScroll:function(){return null},overscanIndicesGetter:Te,overscanRowCount:10,rowRenderer:At,headerRowRenderer:Lt,rowStyle:{},scrollToAlignment:"auto",scrollToIndex:-1,style:{}};Nt.propTypes={};var Pt=[],Dt=null,Ft=null;function Ht(){Ft&&(Ft=null,document.body&&null!=Dt&&(document.body.style.pointerEvents=Dt),Dt=null)}function Wt(){Ht(),Pt.forEach(function(e){return e.__resetIsScrolling()})}function Gt(e){e.currentTarget===window&&null==Dt&&document.body&&(Dt=document.body.style.pointerEvents,document.body.style.pointerEvents="none"),function(){Ft&&we(Ft);var e=0;Pt.forEach(function(t){e=Math.max(e,t.props.scrollingResetTimeInterval)}),Ft=xe(Wt,e)}(),Pt.forEach(function(t){t.props.scrollElement===e.currentTarget&&t.__handleWindowScrollEvent()})}function qt(e,t){Pt.some(function(e){return e.props.scrollElement===t})||t.addEventListener("scroll",Gt),Pt.push(e)}function Bt(e,t){(Pt=Pt.filter(function(t){return t!==e})).length||(t.removeEventListener("scroll",Gt),Ft&&(we(Ft),Ht()))}var Ut=function(e){return e===window},Vt=function(e){return e.getBoundingClientRect()};function Kt(e,t){if(e){if(Ut(e)){var o=window,n=o.innerHeight,r=o.innerWidth;return{height:"number"==typeof n?n:0,width:"number"==typeof r?r:0}}return Vt(e)}return{height:t.serverHeight,width:t.serverWidth}}function Zt(e){return Ut(e)&&document.documentElement?{top:"scrollY"in window?window.scrollY:document.documentElement.scrollTop,left:"scrollX"in window?window.scrollX:document.documentElement.scrollLeft}:{top:e.scrollTop,left:e.scrollLeft}}var $t=function(){return"undefined"!=typeof window?window:void 0},Yt=function(e){function t(){var e,o,n,r;G()(this,t);for(var i=arguments.length,a=Array(i),l=0;l<i;l++)a[l]=arguments[l];return o=n=V()(this,(e=t.__proto__||H()(t)).call.apply(e,[this].concat(a))),n._window=$t(),n._isMounted=!1,n._positionFromTop=0,n._positionFromLeft=0,n.state=oe()({},Kt(n.props.scrollElement,n.props),{isScrolling:!1,scrollLeft:0,scrollTop:0}),n._registerChild=function(e){!e||e instanceof Element||console.warn("WindowScroller registerChild expects to be passed Element or null"),n._child=e,n.updatePosition()},n._onChildScroll=function(e){var t=e.scrollTop;if(n.state.scrollTop!==t){var o=n.props.scrollElement;o&&("function"==typeof o.scrollTo?o.scrollTo(0,t+n._positionFromTop):o.scrollTop=t+n._positionFromTop)}},n._registerResizeListener=function(e){e===window?window.addEventListener("resize",n._onResize,!1):n._detectElementResize.addResizeListener(e,n._onResize)},n._unregisterResizeListener=function(e){e===window?window.removeEventListener("resize",n._onResize,!1):e&&n._detectElementResize.removeResizeListener(e,n._onResize)},n._onResize=function(){n.updatePosition()},n.__handleWindowScrollEvent=function(){if(n._isMounted){var e=n.props.onScroll,t=n.props.scrollElement;if(t){var o=Zt(t),r=Math.max(0,o.left-n._positionFromLeft),i=Math.max(0,o.top-n._positionFromTop);n.setState({isScrolling:!0,scrollLeft:r,scrollTop:i}),e({scrollLeft:r,scrollTop:i})}}},n.__resetIsScrolling=function(){n.setState({isScrolling:!1})},r=o,V()(n,r)}return Z()(t,e),B()(t,[{key:"updatePosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollElement,t=this.props.onResize,o=this.state,n=o.height,r=o.width,i=this._child||Pe.findDOMNode(this);if(i instanceof Element&&e){var a=function(e,t){if(Ut(t)&&document.documentElement){var o=document.documentElement,n=Vt(e),r=Vt(o);return{top:n.top-r.top,left:n.left-r.left}}var i=Zt(t),a=Vt(e),l=Vt(t);return{top:a.top+i.top-l.top,left:a.left+i.left-l.left}}(i,e);this._positionFromTop=a.top,this._positionFromLeft=a.left}var l=Kt(e,this.props);n===l.height&&r===l.width||(this.setState({height:l.height,width:l.width}),t({height:l.height,width:l.width}))}},{key:"componentDidMount",value:function(){var e=this.props.scrollElement;this._detectElementResize=Object(Ae.a)(),this.updatePosition(e),e&&(qt(this,e),this._registerResizeListener(e)),this._isMounted=!0}},{key:"componentDidUpdate",value:function(e,t){var o=this.props.scrollElement,n=e.scrollElement;n!==o&&null!=n&&null!=o&&(this.updatePosition(o),Bt(this,n),qt(this,o),this._unregisterResizeListener(n),this._registerResizeListener(o))}},{key:"componentWillUnmount",value:function(){var e=this.props.scrollElement;e&&(Bt(this,e),this._unregisterResizeListener(e)),this._isMounted=!1}},{key:"render",value:function(){var e=this.props.children,t=this.state,o=t.isScrolling,n=t.scrollTop,r=t.scrollLeft,i=t.height,a=t.width;return e({onChildScroll:this._onChildScroll,registerChild:this._registerChild,height:i,isScrolling:o,scrollLeft:r,scrollTop:n,width:a})}}]),t}(n.PureComponent);Yt.defaultProps={onResize:function(){},onScroll:function(){},scrollingResetTimeInterval:150,scrollElement:$t(),serverHeight:0,serverWidth:0},Yt.propTypes=null;
/**
 * A filtered down version of the emoij data we use.
 *
 * This is significantly filtered. The original data sources are in the `emojibase-data` node module.
 * - `emojibase-data/en/data.json`
 * Transformation applied: data.map(({emoji, group}) => {emoji, group});
 *
 * - `emojibase-data/meta/groups.json`
 * Transformation applied: Manually copying/pasting out the groups.
 *
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
var Jt=[{name:"Smileys & People",icon:
/*
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Smileys & People",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r.a.createElement("svg",{className:y()("emojiGroup-icon","emojiGroup-smileysAndPeople",t),viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(i.m)(e)),r.a.createElement("path",{fill:"currentColor",d:"M12,4 C7.58168889,4 4,7.58168889 4,12 C4,16.4181333 7.58168889,20 12,20 C16.4183111,20 20,16.4181333 20,12 C20,7.58168889 16.4183111,4 12,4 Z M12,18.6444444 C8.33631816,18.6444444 5.35555556,15.6636818 5.35555556,12 C5.35555556,8.33631816 8.33631816,5.35555556 12,5.35555556 C15.6636818,5.35555556 18.6444444,8.33631816 18.6444444,12 C18.6444444,15.6636818 15.6636818,18.6444444 12,18.6444444 Z M10.7059556,10.2024889 C10.7059556,9.51253333 10.1466667,8.95324444 9.45671111,8.95324444 C8.76675556,8.95324444 8.20746667,9.51253333 8.20746667,10.2024889 C8.20746667,10.8924444 8.76675556,11.4517333 9.45671111,11.4517333 C10.1466667,11.4517333 10.7059556,10.8924444 10.7059556,10.2024889 Z M14.5432889,8.95306667 C13.8533333,8.95306667 13.2940444,9.51235556 13.2940444,10.2023111 C13.2940444,10.8922667 13.8533333,11.4515556 14.5432889,11.4515556 C15.2332444,11.4515556 15.7925333,10.8922667 15.7925333,10.2023111 C15.7925333,9.51235556 15.2332444,8.95306667 14.5432889,8.95306667 Z M14.7397333,14.1898667 C14.5767111,14.0812444 14.3564444,14.1256889 14.2471111,14.2883556 C14.2165333,14.3336889 13.4823111,15.4012444 11.9998222,15.4012444 C10.5198222,15.4012444 9.7856,14.3374222 9.75271111,14.2885333 C9.64444444,14.1256889 9.42471111,14.0803556 9.2608,14.1884444 C9.09688889,14.2963556 9.05155556,14.5169778 9.15964444,14.6810667 C9.19804444,14.7393778 10.1242667,16.1125333 11.9998222,16.1125333 C13.8752,16.1125333 14.8014222,14.7395556 14.84,14.6810667 C14.9477333,14.5173333 14.9027556,14.2983111 14.7397333,14.1898667 Z"}))}()},{name:"Animals & Nature",icon:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Animals & Nature",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r.a.createElement("svg",{className:y()("emojiGroup-icon","emojiGroup-animalsAndNature",t),viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(i.m)(e)),r.a.createElement("path",{fill:"currentColor",d:"M10.8815789,7.36973684 L7.77236842,9.00263158 L9.36052632,7.36973684 L9.36052632,3.96973684 L9.36052632,3.5 L8.91315789,3.5 L8.91315789,4.43947368 L7.97368421,5.42368421 L7.97368421,5.02105263 L7.52631579,5.02105263 L7.52631579,5.89342105 L6.58684211,6.87763158 L6.58684211,6.54210526 L6.13947368,6.54210526 L6.13947368,7.34736842 L5.2,8.33157895 L5.2,8.06315789 L4.75263158,8.06315789 L4.75263158,8.80131579 L4.55131579,9.00263158 L2.40394737,9.00263158 L2.40394737,8.06315789 L1.93421053,8.06315789 L1.93421053,9.00263158 L1.93421053,9.04736842 L1.93421053,9.69605263 L3.85789474,9.69605263 L1.93421053,11.7092105 L1.93421053,13.3868421 L3.58947368,13.3868421 L4.73026316,12.2013158 L4.73026316,13.3868421 L9.40526316,13.3868421 L10.8815789,16.7421053 L13.5434211,20.5 L14.5947368,20.5 L20.4328947,10.9710526 L10.8815789,7.36973684 Z M13.5434211,20.5 L13.5434211,18.9565789 L11.9328947,16.7421053 L11.9328947,13.3868421 L15.8026316,13.3868421 L15.8026316,16.7421053 L13.5434211,20.5 Z M7.97368421,19.4039474 L9.47236842,15.6236842 L9.98684211,16.7421053 L9.00263158,19.4039474 L8.28684211,20.5 L7.21315789,20.5 L7.97368421,19.4039474 Z M20.4105263,13.3868421 L22.0657895,16.7421053 L22.0657895,19.4039474 L21.4171053,20.5 L20.4105263,20.5 L21.0368421,19.4039474 L21.0368421,16.7421053 L19.2026316,15.5565789 L20.4105263,13.3868421 Z"}))}()},{name:"Food & Drink",icon:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Food & Drink",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r.a.createElement("svg",{className:y()("emojiGroup-icon","emojiGroup-foodAndDrink",t),viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(i.m)(e)),r.a.createElement("path",{fill:"currentColor",d:"M13.4404507,12.0850076 C14.050232,11.7279399 14.624055,11.6426962 15.1651887,11.5101788 C15.6655864,11.3874682 16.167493,11.2205013 16.9656149,10.4223794 L19.8603784,7.5276159 C20.0469589,7.33952668 20.046456,7.03652199 19.8593726,6.84918714 C19.6717863,6.66185228 19.3697874,6.6623552 19.182704,6.84969005 L16.8119752,9.22016743 C16.6248918,9.40750228 16.3211327,9.40725082 16.1343008,9.22067034 C15.9472174,9.03358694 15.9472174,8.72982788 16.1343008,8.54274449 L16.1353066,8.54199012 C16.4682346,8.20881068 18.5050296,6.17226711 18.5050296,6.17226711 C18.6926159,5.9846808 18.692113,5.68217902 18.5050296,5.49509563 C18.3179462,5.30776077 18.0149415,5.30750932 17.8278581,5.49484417 C17.8278581,5.49484417 15.7910631,7.53138774 15.4581351,7.86456718 L15.4571293,7.86532155 C15.2700459,8.0526564 14.9665383,8.0526564 14.7794549,7.865573 C14.5923715,7.67874106 14.5926229,7.374982 14.7797063,7.18789861 L17.1501837,4.81742123 C17.33777,4.62983492 17.33777,4.32783605 17.1506866,4.14075266 C16.9633518,3.95316635 16.6605985,3.95316635 16.4732637,4.14024975 L13.5782487,7.03451034 C12.7801268,7.83263224 12.6129085,8.33428732 12.4904493,8.83493656 C12.3576804,9.37607026 12.2724368,9.95014471 11.9151176,10.5599259 L5.50424235,4.14879926 C5.31665604,3.9614644 5.01415426,3.96171586 4.82707087,4.1485478 C3.1692203,5.80639837 6.6114039,9.99817284 8.76009693,12.1473688 C9.14080158,12.5280734 9.45210432,12.7606704 9.73122068,12.9145616 L4.28040514,18.3646227 C3.90623835,18.7387895 3.90674126,19.3453018 4.2806566,19.7194686 C4.65457193,20.093384 5.26083277,20.0938869 5.63525102,19.7197201 L11.7916526,13.5633185 C12.1939825,13.7297825 12.6295046,13.9842561 13.0406354,14.395387 L18.3654714,19.720223 C18.7381295,20.0931325 19.3456476,20.0943898 19.7200658,19.720223 C20.0927239,19.347062 20.0932268,18.7385381 19.7205688,18.3653771 L13.4404507,12.0850076 Z"}))}()},{name:"Travel & Places",icon:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Travel & Places",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r.a.createElement("svg",{className:y()("emojiGroup-icon","emojiGroup-travelAndPlaces",t),viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(i.m)(e)),r.a.createElement("path",{fill:"currentColor",d:"M19.7629331,6.05594053 L17.6103818,4.60289782 C17.5216833,4.54282474 17.4166562,4.51077233 17.3094116,4.51077233 L13.5425476,4.51077233 L13.5425476,2.53803704 C13.5425476,2.24069548 13.3018521,2 13.0045105,2 L10.9952879,2 C10.6981479,2 10.4572509,2.24089706 10.4572509,2.53803704 L10.4572509,4.51097392 L6.54725967,4.51097392 C6.25011969,4.51097392 6.00922263,4.75187098 6.00922263,5.04901096 L6.00922263,7.9548948 C6.00922263,8.25203477 6.25011969,8.49293184 6.54725967,8.49293184 L10.4574524,8.49293184 L10.4574524,10.0177397 L6.69058838,10.0177397 C6.58314225,10.0177397 6.47851833,10.0497921 6.38961824,10.1098652 L4.2370669,11.5627063 C4.0886985,11.6628953 4,11.8298098 4,12.0088195 C4,12.187426 4.0886985,12.3547436 4.2370669,12.454731 L6.38941666,13.9077737 C6.47851833,13.9676452 6.58314225,13.9998992 6.69058838,13.9998992 L10.4574524,13.9998992 L10.4574524,21.0492125 C10.4574524,21.3463525 10.6983495,21.5872496 10.9954895,21.5872496 L13.0047121,21.5872496 C13.3020537,21.5872496 13.5427491,21.3463525 13.5427491,21.0492125 L13.5427491,13.9998992 L17.4527403,13.9998992 C17.7500819,13.9998992 17.990979,13.7590021 17.990979,13.4616606 L17.990979,10.5557767 C17.990979,10.2586368 17.7500819,10.0177397 17.4527403,10.0177397 L13.5425476,10.0177397 L13.5425476,8.49293184 L17.3094116,8.49293184 C17.4166562,8.49293184 17.5216833,8.46087943 17.6103818,8.40080635 L19.7629331,6.94776364 C19.9110999,6.84777624 20,6.68045861 20,6.50185209 C20,6.32324556 19.9110999,6.15592793 19.7629331,6.05594053 Z M16.9147033,12.9236235 L13.0045105,12.9236235 L10.9952879,12.9236235 L6.85528537,12.9236235 L5.5000126,12.0086179 L6.85528537,11.0938138 L10.9954895,11.0938138 L13.0047121,11.0938138 L16.9147033,11.0938138 L16.9147033,12.9236235 Z M17.1447146,7.41685775 L13.0045105,7.41685775 L10.9952879,7.41685775 L7.08529671,7.41685775 L7.08529671,5.58724959 L10.9952879,5.58724959 L13.0045105,5.58724959 L17.1447146,5.58724959 L18.4999874,6.50185209 L17.1447146,7.41685775 Z"}))}()},{name:"Activities",icon:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Activities",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r.a.createElement("svg",{className:y()("emojiGroup-icon","emojiGroup-activities",t),viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(i.m)(e)),r.a.createElement("path",{fill:"currentColor",d:"M16.7634901,8.64140896 C16.8282904,9.00807085 16.5809692,9.36033265 16.2157473,9.42495299 L12.7984297,10.0403761 L14.1120765,15.2595029 C14.1379966,15.3696635 14.1613967,15.6195048 14.1432166,15.7336254 L13.5470536,20.1911683 C13.4746932,20.6199305 13.0614111,20.9086519 12.6261689,20.8362916 C12.1909266,20.7637512 11.8982451,20.3609091 11.9706055,19.9294469 L12.5469684,15.5961047 C12.5469684,15.5921446 12.3487874,14.7906005 12.3487874,14.7906005 C12.3345674,14.8269607 8.49370764,20.6031904 8.49370764,20.6031904 C8.2721265,20.9801123 7.785044,21.113313 7.40038202,20.8944319 C7.01950007,20.6782508 6.88611938,20.1951283 7.10626051,19.8180263 L10.7890794,14.294338 L9.65039358,9.89259539 C9.56777315,9.52989353 9.83597453,9.15945162 10.2064164,9.0971713 L15.4385033,8.15540647 L9.05225051,3.94158483 L8.62474831,4.55304797 C8.48866761,4.76148904 8.21182619,4.81728933 8.00428513,4.67994863 C7.79566405,4.54422793 7.73626375,4.2668465 7.87234445,4.05822543 L8.6027882,3 L16.4655886,8.18402662 C16.4798086,8.19446667 16.7132698,8.3562875 16.7634901,8.64140896 Z M9.97727525,6.02995556 C10.714019,6.02995556 11.3125221,6.62179859 11.3127021,7.34990233 C11.3127021,8.08034608 10.714019,8.67110912 9.97727525,8.67110912 C9.23603145,8.67110912 8.63788838,8.08052608 8.63788838,7.34990233 C8.63788838,6.62179859 9.23603145,6.02995556 9.97727525,6.02995556 Z"}))}()},{name:"Objects",icon:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Objects",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r.a.createElement("svg",{className:y()("emojiGroup-icon","emojiGroup-objects",t),viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(i.m)(e)),r.a.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M17.4781671,6.08771565 C17.130662,5.41988765 16.6736986,4.86511431 16.1074207,4.42368331 C15.5409989,3.98239614 14.9002146,3.63474585 14.1856429,3.38073244 C13.4706397,3.1270067 12.7421161,3 11.9999281,3 C11.2577401,3 10.5292165,3.12686287 9.81435712,3.38073244 C9.09949778,3.63474585 8.45885723,3.98239614 7.89243548,4.42368331 C7.32615757,4.86511431 6.86905033,5.41974381 6.52140143,6.08771565 C6.17375253,6.75568749 6,7.47630311 6,8.24985017 C6,9.46080084 6.40244999,10.5076353 7.20706229,11.3904973 C7.55873857,11.7732434 7.84971653,12.1131266 8.08014,12.410003 C8.31070731,12.7067355 8.54314447,13.0798447 8.77745149,13.5290428 C9.0117585,13.9782409 9.14466193,14.3982404 9.1758741,14.7888975 C8.80866365,15.0076712 8.62513035,15.3279928 8.62513035,15.750006 C8.62513035,16.0391153 8.72279423,16.2891013 8.918122,16.499964 C8.72279423,16.7109706 8.62513035,16.9609567 8.62513035,17.2500659 C8.62513035,17.6564011 8.80104041,17.9726952 9.15257285,18.1995237 C9.05088159,18.3790304 9.00010788,18.5625644 9.00010788,18.7501259 C9.00010788,19.1094268 9.1230867,19.3870292 9.36918817,19.5820701 C9.61514581,19.7773987 9.91791823,19.8750629 10.2773616,19.8750629 C10.4335663,20.2188297 10.6680171,20.4922609 10.9804265,20.6953565 C11.2928358,20.8984522 11.6327176,21 11.9999281,21 C12.3671385,21 12.7070203,20.8984522 13.0194297,20.6953565 C13.3319829,20.4922609 13.5664337,20.2188297 13.7226384,19.8750629 C14.0819379,19.8750629 14.3848542,19.7773987 14.6308118,19.5820701 C14.8769133,19.3870292 14.9998921,19.1094268 14.9998921,18.7501259 C14.9998921,18.5625644 14.9489746,18.3790304 14.8472833,18.1995237 C15.1988158,17.9728391 15.3747258,17.6564011 15.3747258,17.2500659 C15.3747258,16.9609567 15.2769181,16.7109706 15.0817342,16.499964 C15.2769181,16.2891013 15.3747258,16.0391153 15.3747258,15.750006 C15.3747258,15.3279928 15.1911925,15.0078151 14.8238382,14.7888975 C14.8551942,14.3982404 14.9880977,13.9782409 15.2224047,13.5290428 C15.4569994,13.0798447 15.6892927,12.7067355 15.91986,12.410003 C16.1504273,12.1131266 16.4414053,11.7732434 16.7927939,11.3904973 C17.5974062,10.5076353 18,9.46080084 18,8.24985017 C18,7.47630311 17.8261036,6.75568749 17.4781671,6.08771565 L17.4781671,6.08771565 Z M15.702957,10.3591971 C15.6247108,10.4450668 15.5056156,10.5739434 15.3455273,10.7458268 C15.1854391,10.9177101 15.0663438,11.0464429 14.9882415,11.1324565 C13.9883014,12.3278729 13.4375577,13.4917894 13.3360103,14.6246374 L10.6639897,14.6246374 C10.5624423,13.4917894 10.0116986,12.3278729 9.01161467,11.1324565 C8.93351233,11.0465867 8.81441705,10.9177101 8.654185,10.7458268 C8.49409678,10.5739434 8.37485766,10.4452107 8.29675532,10.3591971 C7.76557312,9.74199915 7.4999101,9.03878763 7.4999101,8.24985017 C7.4999101,7.68730972 7.63468338,7.16389251 7.90422994,6.6794547 C8.17392034,6.1950169 8.52545278,5.79860639 8.9589711,5.48993551 C9.39248942,5.18126463 9.87289791,4.93918957 10.4003404,4.76342264 C10.9276391,4.58751189 11.460835,4.49977226 11.9999281,4.49977226 C12.5390212,4.49977226 13.0722171,4.58751189 13.5995158,4.76342264 C14.1268144,4.93918957 14.6072229,5.18126463 15.0408851,5.48993551 C15.4744034,5.79860639 15.8260797,6.19516074 16.0954824,6.6794547 C16.3648851,7.16389251 16.4998022,7.68730972 16.4998022,8.24985017 C16.4998022,9.03878763 16.234283,9.74199915 15.702957,10.3591971 L15.702957,10.3591971 Z M13.5410693,6.16674126 C13.1543994,6.05562304 12.7655718,6 12.375018,6 C12.2734596,6 12.185567,6.03286235 12.1113402,6.09897065 C12.0371134,6.16495109 12,6.24307909 12,6.33335464 C12,6.4236302 12.0371134,6.5017582 12.1113402,6.56773864 C12.185567,6.6335912 12.2734596,6.66670929 12.375018,6.66670929 C12.7813953,6.66670929 13.1953968,6.75353238 13.6175977,6.9270507 C14.0393671,7.10069689 14.2501079,7.34722844 14.2501079,7.66677322 C14.2501079,7.75704878 14.2870774,7.83530465 14.3614481,7.90115722 C14.4358187,7.96700978 14.5235675,8 14.624982,8 C14.7266842,8 14.814433,7.96713765 14.8888036,7.90115722 C14.9630304,7.83517678 15,7.75704878 15,7.66677322 C15,7.30554312 14.8532726,6.99303114 14.560537,6.72923726 C14.2678015,6.46544339 13.9277392,6.2777316 13.5410693,6.16674126 L13.5410693,6.16674126 Z"}))}()},{name:"Symbols",icon:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Symbols",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r.a.createElement("svg",{className:y()("emojiGroup-icon","emojiGroup-symbols",t),viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(i.m)(e)),r.a.createElement("path",{fill:"currentColor",d:"M19.1442646,4.83575742 C18.1650678,3.85559657 16.4547374,3.73144287 14.3690482,4.52537315 C13.5381247,4.85169622 12.7411635,5.25877639 11.9896,5.74077261 C11.2380365,5.25877639 10.4410753,4.85169622 9.61015178,4.52537315 C7.52446261,3.73144287 5.82718816,3.83926056 4.8349354,4.83575742 C3.84268265,5.83225429 3.73170701,7.52793255 4.52485642,9.61567516 C4.85085852,10.4474168 5.25753831,11.2451626 5.73906044,11.997466 C5.25753831,12.7497695 4.85085852,13.5475153 4.52485642,14.3792569 C3.73170701,16.4669995 3.83941866,18.165945 4.8349354,19.1591746 C5.4505037,19.7385409 6.27598921,20.04073 7.11972793,19.9955786 C7.98011582,19.972593 8.82930089,19.7943985 9.62647173,19.4695589 C10.4517722,19.142269 11.243226,18.73521 11.9896,18.2541594 C12.7411635,18.7361557 13.5381247,19.1432358 14.3690482,19.4695589 C15.1692842,19.7955535 16.0218905,19.9737698 16.885584,19.9955786 C17.7293227,20.04073 18.5548082,19.7385409 19.1703765,19.1591746 C20.1495733,18.1790138 20.2736049,16.4669995 19.4804555,14.3792569 C19.1461294,13.5464957 18.7306859,12.7487154 18.2401396,11.997466 C18.7216617,11.2451626 19.1283415,10.4474168 19.4543436,9.61567516 C20.247493,7.52793255 20.1495733,5.83225429 19.1442646,4.83575742 Z M5.74558842,9.15173236 C5.15480636,7.59654381 5.15807034,6.36154114 5.74558842,5.76037582 C6.11674164,5.43561893 6.60182527,5.2721976 7.09361601,5.30623463 C7.79440087,5.33070398 8.48521159,5.480066 9.13360934,5.74730701 C9.70769152,5.97226391 10.2631967,6.24209666 10.7949799,6.55430611 C9.18669216,7.76170948 7.7580096,9.19179863 6.55179379,10.8016698 C6.24050431,10.2735668 5.97094753,9.72190607 5.74558842,9.15173236 Z M11.9896,7.30902996 C12.8957173,7.95068414 13.7452817,8.66882467 14.5289837,9.45558222 C15.3149674,10.2400558 16.0324016,11.0904566 16.6734247,11.997466 C16.0324016,12.9044755 15.3149674,13.7548763 14.5289837,14.5393498 C13.7452817,15.3261074 12.8957173,16.0442479 11.9896,16.6859021 C11.0834827,16.0442479 10.2339183,15.3261074 9.45021631,14.5393498 C8.66423258,13.7548763 7.94679839,12.9044755 7.30577532,11.997466 C8.59381135,10.1831894 10.1771079,8.59833411 11.9896,7.30902996 Z M9.1466653,18.247625 C7.59300638,18.8389888 6.35921842,18.8357215 5.75864438,18.247625 C5.15807034,17.6595285 5.15480636,16.411457 5.75864438,14.8562685 C5.98338002,14.2816211 6.25294737,13.725569 6.56484974,13.1932623 C7.77126614,14.8029593 9.19992209,16.2330218 10.8080359,17.4406259 C10.2762527,17.7528354 9.72074748,18.0226681 9.1466653,18.247625 Z M18.2336116,14.8431997 C18.8243937,16.3983882 18.8211297,17.6333909 18.2336116,18.2345562 C17.6460935,18.8357215 16.3992496,18.8389888 14.8455907,18.2345562 C14.2715085,18.0095993 13.7160033,17.7397666 13.1842201,17.4275571 C13.9922383,16.8232472 14.7558043,16.1615663 15.4690126,15.4476322 C16.1822445,14.7337217 16.8432746,13.9694039 17.4469902,13.1605902 C17.753106,13.6998229 18.0161333,14.2624472 18.2336116,14.8431997 Z M18.2336116,9.15173236 C18.0088759,9.72637974 17.7393086,10.2824319 17.4274062,10.8147386 C16.2209564,9.20507058 14.7923049,7.7750125 13.1842201,6.56737492 C13.7160033,6.25516547 14.2715085,5.98533272 14.8455907,5.76037582 C15.4931609,5.48865125 16.184001,5.33485713 16.885584,5.30623463 C17.3773747,5.2721976 17.8624584,5.43561893 18.2336116,5.76037582 C18.8211297,6.36154114 18.8243937,7.59654381 18.2336116,9.15173236 Z M11.9896,14.284508 C13.2514561,14.284508 14.2743925,13.2605644 14.2743925,11.997466 C14.2743925,10.7343676 13.2514561,9.71042404 11.9896,9.71042404 C10.7277439,9.71042404 9.70480748,10.7343676 9.70480748,11.997466 C9.70480748,12.6040273 9.945526,13.1857453 10.3740077,13.6146489 C10.8024894,14.0435525 11.3836353,14.284508 11.9896,14.284508 Z M11.9896,11.0173052 C12.5303955,11.0173052 12.9687968,11.4561381 12.9687968,11.997466 C12.9687968,12.5387939 12.5303955,12.9776269 11.9896,12.9776269 C11.4488045,12.9776269 11.0104032,12.5387939 11.0104032,11.997466 C11.0104032,11.4561381 11.4488045,11.0173052 11.9896,11.0173052 Z"}))}()},{name:"Flags",icon:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Flags",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return r.a.createElement("svg",{className:y()("emojiGroup-icon","emojiGroup-flags",t),viewBox:"0 0 24 24"},r.a.createElement("title",null,Object(i.m)(e)),r.a.createElement("path",{fill:"currentColor",d:"M6.32484076,15.8895966 L6.35881104,19.3205945 C6.35881104,19.6942675 6.05307856,20 5.67940552,20 C5.30573248,20 5,19.6942675 5,19.3205945 L5,13.104034 L5,5.63057325 L5,4.67940552 C5,4.30573248 5.30573248,4 5.67940552,4 C6.01910828,4 6.29087049,4.23779193 6.32484076,4.57749469 L6.32484076,15.8895966 Z M18.2144374,12.7643312 C18.2823779,12.866242 18.3163482,12.9681529 18.3163482,13.0700637 C18.3163482,13.4437367 18.0106157,13.7494692 17.6369427,13.7494692 C17.5690021,13.7494692 17.5010616,13.7494692 17.433121,13.7154989 C16.7197452,13.5116773 15.9384289,13.4097665 15.089172,13.4097665 C13.9681529,13.4097665 12.881104,13.5796178 11.7261146,13.7494692 C10.5711253,13.9193206 9.34819533,14.089172 8.15923567,14.089172 C7.98938429,14.089172 7.78556263,14.089172 7.61571125,14.089172 L7.61571125,5.29087049 C7.78556263,5.29087049 7.98938429,5.29087049 8.15923567,5.29087049 C9.28025478,5.29087049 10.3673036,5.12101911 11.522293,4.95116773 C12.6772824,4.78131635 13.9002123,4.61146497 15.089172,4.61146497 C16.07431,4.61146497 16.9575372,4.74734607 17.8067941,4.985138 C18.0106157,5.05307856 18.1804671,5.18895966 18.2484076,5.39278132 C18.3163482,5.59660297 18.3163482,5.80042463 18.2144374,5.97027601 C17.670913,6.88747346 17.1273885,7.90658174 16.5498938,9.06157113 C17.1273885,10.3184713 17.670913,11.5414013 18.2144374,12.7643312 Z"}))}()}],Xt=[{emoji:"😀",group:0},{emoji:"😁",group:0},{emoji:"😂",group:0},{emoji:"🤣",group:0},{emoji:"😃",group:0},{emoji:"😄",group:0},{emoji:"😅",group:0},{emoji:"😆",group:0},{emoji:"😉",group:0},{emoji:"😊",group:0},{emoji:"😋",group:0},{emoji:"😎",group:0},{emoji:"😍",group:0},{emoji:"😘",group:0},{emoji:"😗",group:0},{emoji:"😙",group:0},{emoji:"😚",group:0},{emoji:"☺️",group:0},{emoji:"🙂",group:0},{emoji:"🤗",group:0},{emoji:"🤩",group:0},{emoji:"🤔",group:0},{emoji:"🤨",group:0},{emoji:"😐️",group:0},{emoji:"😑",group:0},{emoji:"😶",group:0},{emoji:"🙄",group:0},{emoji:"😏",group:0},{emoji:"😣",group:0},{emoji:"😥",group:0},{emoji:"😮",group:0},{emoji:"🤐",group:0},{emoji:"😯",group:0},{emoji:"😪",group:0},{emoji:"😫",group:0},{emoji:"😴",group:0},{emoji:"😌",group:0},{emoji:"😛",group:0},{emoji:"😜",group:0},{emoji:"😝",group:0},{emoji:"🤤",group:0},{emoji:"😒",group:0},{emoji:"😓",group:0},{emoji:"😔",group:0},{emoji:"😕",group:0},{emoji:"🙃",group:0},{emoji:"🤑",group:0},{emoji:"😲",group:0},{emoji:"☹️",group:0},{emoji:"🙁",group:0},{emoji:"😖",group:0},{emoji:"😞",group:0},{emoji:"😟",group:0},{emoji:"😤",group:0},{emoji:"😢",group:0},{emoji:"😭",group:0},{emoji:"😦",group:0},{emoji:"😧",group:0},{emoji:"😨",group:0},{emoji:"😩",group:0},{emoji:"🤯",group:0},{emoji:"😬",group:0},{emoji:"😰",group:0},{emoji:"😱",group:0},{emoji:"😳",group:0},{emoji:"🤪",group:0},{emoji:"😵",group:0},{emoji:"😡",group:0},{emoji:"😠",group:0},{emoji:"🤬",group:0},{emoji:"😷",group:0},{emoji:"🤒",group:0},{emoji:"🤕",group:0},{emoji:"🤢",group:0},{emoji:"🤮",group:0},{emoji:"🤧",group:0},{emoji:"😇",group:0},{emoji:"🤠",group:0},{emoji:"🤡",group:0},{emoji:"🤥",group:0},{emoji:"🤫",group:0},{emoji:"🤭",group:0},{emoji:"🧐",group:0},{emoji:"🤓",group:0},{emoji:"😈",group:0},{emoji:"👿",group:0},{emoji:"👹",group:0},{emoji:"👺",group:0},{emoji:"💀",group:0},{emoji:"☠️",group:0},{emoji:"👻",group:0},{emoji:"👽️",group:0},{emoji:"👾",group:0},{emoji:"🤖",group:0},{emoji:"💩",group:0},{emoji:"😺",group:0},{emoji:"😸",group:0},{emoji:"😹",group:0},{emoji:"😻",group:0},{emoji:"😼",group:0},{emoji:"😽",group:0},{emoji:"🙀",group:0},{emoji:"😿",group:0},{emoji:"😾",group:0},{emoji:"🙈",group:0},{emoji:"🙉",group:0},{emoji:"🙊",group:0},{emoji:"👶",group:0},{emoji:"🧒",group:0},{emoji:"👦",group:0},{emoji:"👧",group:0},{emoji:"🧑",group:0},{emoji:"👨",group:0},{emoji:"👩",group:0},{emoji:"🧓",group:0},{emoji:"👴",group:0},{emoji:"👵",group:0},{emoji:"👨‍⚕️",group:0},{emoji:"👩‍⚕️",group:0},{emoji:"👨‍🎓",group:0},{emoji:"👩‍🎓",group:0},{emoji:"👨‍🏫",group:0},{emoji:"👩‍🏫",group:0},{emoji:"👨‍⚖️",group:0},{emoji:"👩‍⚖️",group:0},{emoji:"👨‍🌾",group:0},{emoji:"👩‍🌾",group:0},{emoji:"👨‍🍳",group:0},{emoji:"👩‍🍳",group:0},{emoji:"👨‍🔧",group:0},{emoji:"👩‍🔧",group:0},{emoji:"👨‍🏭",group:0},{emoji:"👩‍🏭",group:0},{emoji:"👨‍💼",group:0},{emoji:"👩‍💼",group:0},{emoji:"👨‍🔬",group:0},{emoji:"👩‍🔬",group:0},{emoji:"👨‍💻",group:0},{emoji:"👩‍💻",group:0},{emoji:"👨‍🎤",group:0},{emoji:"👩‍🎤",group:0},{emoji:"👨‍🎨",group:0},{emoji:"👩‍🎨",group:0},{emoji:"👨‍✈️",group:0},{emoji:"👩‍✈️",group:0},{emoji:"👨‍🚀",group:0},{emoji:"👩‍🚀",group:0},{emoji:"👨‍🚒",group:0},{emoji:"👩‍🚒",group:0},{emoji:"👮",group:0},{emoji:"👮‍♂️",group:0},{emoji:"👮‍♀️",group:0},{emoji:"🕵️",group:0},{emoji:"🕵️‍♂️",group:0},{emoji:"🕵️‍♀️",group:0},{emoji:"💂",group:0},{emoji:"💂‍♂️",group:0},{emoji:"💂‍♀️",group:0},{emoji:"👷",group:0},{emoji:"👷‍♂️",group:0},{emoji:"👷‍♀️",group:0},{emoji:"🤴",group:0},{emoji:"👸",group:0},{emoji:"👳",group:0},{emoji:"👳‍♂️",group:0},{emoji:"👳‍♀️",group:0},{emoji:"👲",group:0},{emoji:"🧕",group:0},{emoji:"🧔",group:0},{emoji:"👱",group:0},{emoji:"👱‍♂️",group:0},{emoji:"👱‍♀️",group:0},{emoji:"🤵",group:0},{emoji:"👰",group:0},{emoji:"🤰",group:0},{emoji:"🤱",group:0},{emoji:"👼",group:0},{emoji:"🎅",group:0},{emoji:"🤶",group:0},{emoji:"🧙",group:0},{emoji:"🧙‍♀️",group:0},{emoji:"🧙‍♂️",group:0},{emoji:"🧚",group:0},{emoji:"🧚‍♀️",group:0},{emoji:"🧚‍♂️",group:0},{emoji:"🧛",group:0},{emoji:"🧛‍♀️",group:0},{emoji:"🧛‍♂️",group:0},{emoji:"🧜",group:0},{emoji:"🧜‍♀️",group:0},{emoji:"🧜‍♂️",group:0},{emoji:"🧝",group:0},{emoji:"🧝‍♀️",group:0},{emoji:"🧝‍♂️",group:0},{emoji:"🧞",group:0},{emoji:"🧞‍♀️",group:0},{emoji:"🧞‍♂️",group:0},{emoji:"🧟",group:0},{emoji:"🧟‍♀️",group:0},{emoji:"🧟‍♂️",group:0},{emoji:"🙍",group:0},{emoji:"🙍‍♂️",group:0},{emoji:"🙍‍♀️",group:0},{emoji:"🙎",group:0},{emoji:"🙎‍♂️",group:0},{emoji:"🙎‍♀️",group:0},{emoji:"🙅",group:0},{emoji:"🙅‍♂️",group:0},{emoji:"🙅‍♀️",group:0},{emoji:"🙆",group:0},{emoji:"🙆‍♂️",group:0},{emoji:"🙆‍♀️",group:0},{emoji:"💁",group:0},{emoji:"💁‍♂️",group:0},{emoji:"💁‍♀️",group:0},{emoji:"🙋",group:0},{emoji:"🙋‍♂️",group:0},{emoji:"🙋‍♀️",group:0},{emoji:"🙇",group:0},{emoji:"🙇‍♂️",group:0},{emoji:"🙇‍♀️",group:0},{emoji:"🤦",group:0},{emoji:"🤦‍♂️",group:0},{emoji:"🤦‍♀️",group:0},{emoji:"🤷",group:0},{emoji:"🤷‍♂️",group:0},{emoji:"🤷‍♀️",group:0},{emoji:"💆",group:0},{emoji:"💆‍♂️",group:0},{emoji:"💆‍♀️",group:0},{emoji:"💇",group:0},{emoji:"💇‍♂️",group:0},{emoji:"💇‍♀️",group:0},{emoji:"🚶",group:0},{emoji:"🚶‍♂️",group:0},{emoji:"🚶‍♀️",group:0},{emoji:"🏃",group:0},{emoji:"🏃‍♂️",group:0},{emoji:"🏃‍♀️",group:0},{emoji:"💃",group:0},{emoji:"🕺",group:0},{emoji:"👯",group:0},{emoji:"👯‍♂️",group:0},{emoji:"👯‍♀️",group:0},{emoji:"🧖",group:0},{emoji:"🧖‍♀️",group:0},{emoji:"🧖‍♂️",group:0},{emoji:"🧗",group:0},{emoji:"🧗‍♀️",group:0},{emoji:"🧗‍♂️",group:0},{emoji:"🧘",group:0},{emoji:"🧘‍♀️",group:0},{emoji:"🧘‍♂️",group:0},{emoji:"🛀",group:0},{emoji:"🛌",group:0},{emoji:"🕴️",group:0},{emoji:"🗣️",group:0},{emoji:"👤",group:0},{emoji:"👥",group:0},{emoji:"🤺",group:0},{emoji:"🏇",group:0},{emoji:"⛷️",group:0},{emoji:"🏂️",group:0},{emoji:"🏌️",group:0},{emoji:"🏌️‍♂️",group:0},{emoji:"🏌️‍♀️",group:0},{emoji:"🏄️",group:0},{emoji:"🏄‍♂️",group:0},{emoji:"🏄‍♀️",group:0},{emoji:"🚣",group:0},{emoji:"🚣‍♂️",group:0},{emoji:"🚣‍♀️",group:0},{emoji:"🏊️",group:0},{emoji:"🏊‍♂️",group:0},{emoji:"🏊‍♀️",group:0},{emoji:"⛹️",group:0},{emoji:"⛹️‍♂️",group:0},{emoji:"⛹️‍♀️",group:0},{emoji:"🏋️",group:0},{emoji:"🏋️‍♂️",group:0},{emoji:"🏋️‍♀️",group:0},{emoji:"🚴",group:0},{emoji:"🚴‍♂️",group:0},{emoji:"🚴‍♀️",group:0},{emoji:"🚵",group:0},{emoji:"🚵‍♂️",group:0},{emoji:"🚵‍♀️",group:0},{emoji:"🏎️",group:0},{emoji:"🏍️",group:0},{emoji:"🤸",group:0},{emoji:"🤸‍♂️",group:0},{emoji:"🤸‍♀️",group:0},{emoji:"🤼",group:0},{emoji:"🤼‍♂️",group:0},{emoji:"🤼‍♀️",group:0},{emoji:"🤽",group:0},{emoji:"🤽‍♂️",group:0},{emoji:"🤽‍♀️",group:0},{emoji:"🤾",group:0},{emoji:"🤾‍♂️",group:0},{emoji:"🤾‍♀️",group:0},{emoji:"🤹",group:0},{emoji:"🤹‍♂️",group:0},{emoji:"🤹‍♀️",group:0},{emoji:"👫",group:0},{emoji:"👬",group:0},{emoji:"👭",group:0},{emoji:"💏",group:0},{emoji:"👩‍❤️‍💋‍👨",group:0},{emoji:"👨‍❤️‍💋‍👨",group:0},{emoji:"👩‍❤️‍💋‍👩",group:0},{emoji:"💑",group:0},{emoji:"👩‍❤️‍👨",group:0},{emoji:"👨‍❤️‍👨",group:0},{emoji:"👩‍❤️‍👩",group:0},{emoji:"👪️",group:0},{emoji:"👨‍👩‍👦",group:0},{emoji:"👨‍👩‍👧",group:0},{emoji:"👨‍👩‍👧‍👦",group:0},{emoji:"👨‍👩‍👦‍👦",group:0},{emoji:"👨‍👩‍👧‍👧",group:0},{emoji:"👨‍👨‍👦",group:0},{emoji:"👨‍👨‍👧",group:0},{emoji:"👨‍👨‍👧‍👦",group:0},{emoji:"👨‍👨‍👦‍👦",group:0},{emoji:"👨‍👨‍👧‍👧",group:0},{emoji:"👩‍👩‍👦",group:0},{emoji:"👩‍👩‍👧",group:0},{emoji:"👩‍👩‍👧‍👦",group:0},{emoji:"👩‍👩‍👦‍👦",group:0},{emoji:"👩‍👩‍👧‍👧",group:0},{emoji:"👨‍👦",group:0},{emoji:"👨‍👦‍👦",group:0},{emoji:"👨‍👧",group:0},{emoji:"👨‍👧‍👦",group:0},{emoji:"👨‍👧‍👧",group:0},{emoji:"👩‍👦",group:0},{emoji:"👩‍👦‍👦",group:0},{emoji:"👩‍👧",group:0},{emoji:"👩‍👧‍👦",group:0},{emoji:"👩‍👧‍👧",group:0},{emoji:"🤳",group:0},{emoji:"💪",group:0},{emoji:"👈️",group:0},{emoji:"👉️",group:0},{emoji:"☝️",group:0},{emoji:"👆️",group:0},{emoji:"🖕",group:0},{emoji:"👇️",group:0},{emoji:"✌️",group:0},{emoji:"🤞",group:0},{emoji:"🖖",group:0},{emoji:"🤘",group:0},{emoji:"🤙",group:0},{emoji:"🖐️",group:0},{emoji:"✋",group:0},{emoji:"👌",group:0},{emoji:"👍️",group:0},{emoji:"👎️",group:0},{emoji:"✊",group:0},{emoji:"👊",group:0},{emoji:"🤛",group:0},{emoji:"🤜",group:0},{emoji:"🤚",group:0},{emoji:"👋",group:0},{emoji:"🤟",group:0},{emoji:"✍️",group:0},{emoji:"👏",group:0},{emoji:"👐",group:0},{emoji:"🙌",group:0},{emoji:"🤲",group:0},{emoji:"🙏",group:0},{emoji:"🤝",group:0},{emoji:"💅",group:0},{emoji:"👂️",group:0},{emoji:"👃",group:0},{emoji:"👣",group:0},{emoji:"👀",group:0},{emoji:"👁️",group:0},{emoji:"👁️‍🗨️",group:0},{emoji:"🧠",group:0},{emoji:"👅",group:0},{emoji:"👄",group:0},{emoji:"💋",group:0},{emoji:"💘",group:0},{emoji:"❤️",group:0},{emoji:"💓",group:0},{emoji:"💔",group:0},{emoji:"💕",group:0},{emoji:"💖",group:0},{emoji:"💗",group:0},{emoji:"💙",group:0},{emoji:"💚",group:0},{emoji:"💛",group:0},{emoji:"🧡",group:0},{emoji:"💜",group:0},{emoji:"🖤",group:0},{emoji:"💝",group:0},{emoji:"💞",group:0},{emoji:"💟",group:0},{emoji:"❣️",group:0},{emoji:"💌",group:0},{emoji:"💤",group:0},{emoji:"💢",group:0},{emoji:"💣️",group:0},{emoji:"💥",group:0},{emoji:"💦",group:0},{emoji:"💨",group:0},{emoji:"💫",group:0},{emoji:"💬",group:0},{emoji:"🗨️",group:0},{emoji:"🗯️",group:0},{emoji:"💭",group:0},{emoji:"🕳️",group:0},{emoji:"👓️",group:0},{emoji:"🕶️",group:0},{emoji:"👔",group:0},{emoji:"👕",group:0},{emoji:"👖",group:0},{emoji:"🧣",group:0},{emoji:"🧤",group:0},{emoji:"🧥",group:0},{emoji:"🧦",group:0},{emoji:"👗",group:0},{emoji:"👘",group:0},{emoji:"👙",group:0},{emoji:"👚",group:0},{emoji:"👛",group:0},{emoji:"👜",group:0},{emoji:"👝",group:0},{emoji:"🛍️",group:0},{emoji:"🎒",group:0},{emoji:"👞",group:0},{emoji:"👟",group:0},{emoji:"👠",group:0},{emoji:"👡",group:0},{emoji:"👢",group:0},{emoji:"👑",group:0},{emoji:"👒",group:0},{emoji:"🎩",group:0},{emoji:"🎓️",group:0},{emoji:"🧢",group:0},{emoji:"⛑️",group:0},{emoji:"📿",group:0},{emoji:"💄",group:0},{emoji:"💍",group:0},{emoji:"💎",group:0},{emoji:"🐵",group:1},{emoji:"🐒",group:1},{emoji:"🦍",group:1},{emoji:"🐶",group:1},{emoji:"🐕️",group:1},{emoji:"🐩",group:1},{emoji:"🐺",group:1},{emoji:"🦊",group:1},{emoji:"🐱",group:1},{emoji:"🐈️",group:1},{emoji:"🦁",group:1},{emoji:"🐯",group:1},{emoji:"🐅",group:1},{emoji:"🐆",group:1},{emoji:"🐴",group:1},{emoji:"🐎",group:1},{emoji:"🦄",group:1},{emoji:"🦓",group:1},{emoji:"🦌",group:1},{emoji:"🐮",group:1},{emoji:"🐂",group:1},{emoji:"🐃",group:1},{emoji:"🐄",group:1},{emoji:"🐷",group:1},{emoji:"🐖",group:1},{emoji:"🐗",group:1},{emoji:"🐽",group:1},{emoji:"🐏",group:1},{emoji:"🐑",group:1},{emoji:"🐐",group:1},{emoji:"🐪",group:1},{emoji:"🐫",group:1},{emoji:"🦒",group:1},{emoji:"🐘",group:1},{emoji:"🦏",group:1},{emoji:"🐭",group:1},{emoji:"🐁",group:1},{emoji:"🐀",group:1},{emoji:"🐹",group:1},{emoji:"🐰",group:1},{emoji:"🐇",group:1},{emoji:"🐿️",group:1},{emoji:"🦔",group:1},{emoji:"🦇",group:1},{emoji:"🐻",group:1},{emoji:"🐨",group:1},{emoji:"🐼",group:1},{emoji:"🐾",group:1},{emoji:"🦃",group:1},{emoji:"🐔",group:1},{emoji:"🐓",group:1},{emoji:"🐣",group:1},{emoji:"🐤",group:1},{emoji:"🐥",group:1},{emoji:"🐦️",group:1},{emoji:"🐧",group:1},{emoji:"🕊️",group:1},{emoji:"🦅",group:1},{emoji:"🦆",group:1},{emoji:"🦉",group:1},{emoji:"🐸",group:1},{emoji:"🐊",group:1},{emoji:"🐢",group:1},{emoji:"🦎",group:1},{emoji:"🐍",group:1},{emoji:"🐲",group:1},{emoji:"🐉",group:1},{emoji:"🦕",group:1},{emoji:"🦖",group:1},{emoji:"🐳",group:1},{emoji:"🐋",group:1},{emoji:"🐬",group:1},{emoji:"🐟️",group:1},{emoji:"🐠",group:1},{emoji:"🐡",group:1},{emoji:"🦈",group:1},{emoji:"🐙",group:1},{emoji:"🐚",group:1},{emoji:"🦀",group:1},{emoji:"🦐",group:1},{emoji:"🦑",group:1},{emoji:"🐌",group:1},{emoji:"🦋",group:1},{emoji:"🐛",group:1},{emoji:"🐜",group:1},{emoji:"🐝",group:1},{emoji:"🐞",group:1},{emoji:"🦗",group:1},{emoji:"🕷️",group:1},{emoji:"🕸️",group:1},{emoji:"🦂",group:1},{emoji:"💐",group:1},{emoji:"🌸",group:1},{emoji:"💮",group:1},{emoji:"🏵️",group:1},{emoji:"🌹",group:1},{emoji:"🥀",group:1},{emoji:"🌺",group:1},{emoji:"🌻",group:1},{emoji:"🌼",group:1},{emoji:"🌷",group:1},{emoji:"🌱",group:1},{emoji:"🌲",group:1},{emoji:"🌳",group:1},{emoji:"🌴",group:1},{emoji:"🌵",group:1},{emoji:"🌾",group:1},{emoji:"🌿",group:1},{emoji:"☘️",group:1},{emoji:"🍀",group:1},{emoji:"🍁",group:1},{emoji:"🍂",group:1},{emoji:"🍃",group:1},{emoji:"🍇",group:2},{emoji:"🍈",group:2},{emoji:"🍉",group:2},{emoji:"🍊",group:2},{emoji:"🍋",group:2},{emoji:"🍌",group:2},{emoji:"🍍",group:2},{emoji:"🍎",group:2},{emoji:"🍏",group:2},{emoji:"🍐",group:2},{emoji:"🍑",group:2},{emoji:"🍒",group:2},{emoji:"🍓",group:2},{emoji:"🥝",group:2},{emoji:"🍅",group:2},{emoji:"🥥",group:2},{emoji:"🥑",group:2},{emoji:"🍆",group:2},{emoji:"🥔",group:2},{emoji:"🥕",group:2},{emoji:"🌽",group:2},{emoji:"🌶️",group:2},{emoji:"🥒",group:2},{emoji:"🥦",group:2},{emoji:"🍄",group:2},{emoji:"🥜",group:2},{emoji:"🌰",group:2},{emoji:"🍞",group:2},{emoji:"🥐",group:2},{emoji:"🥖",group:2},{emoji:"🥨",group:2},{emoji:"🥞",group:2},{emoji:"🧀",group:2},{emoji:"🍖",group:2},{emoji:"🍗",group:2},{emoji:"🥩",group:2},{emoji:"🥓",group:2},{emoji:"🍔",group:2},{emoji:"🍟",group:2},{emoji:"🍕",group:2},{emoji:"🌭",group:2},{emoji:"🥪",group:2},{emoji:"🌮",group:2},{emoji:"🌯",group:2},{emoji:"🥙",group:2},{emoji:"🥚",group:2},{emoji:"🍳",group:2},{emoji:"🥘",group:2},{emoji:"🍲",group:2},{emoji:"🥣",group:2},{emoji:"🥗",group:2},{emoji:"🍿",group:2},{emoji:"🥫",group:2},{emoji:"🍱",group:2},{emoji:"🍘",group:2},{emoji:"🍙",group:2},{emoji:"🍚",group:2},{emoji:"🍛",group:2},{emoji:"🍜",group:2},{emoji:"🍝",group:2},{emoji:"🍠",group:2},{emoji:"🍢",group:2},{emoji:"🍣",group:2},{emoji:"🍤",group:2},{emoji:"🍥",group:2},{emoji:"🍡",group:2},{emoji:"🥟",group:2},{emoji:"🥠",group:2},{emoji:"🥡",group:2},{emoji:"🍦",group:2},{emoji:"🍧",group:2},{emoji:"🍨",group:2},{emoji:"🍩",group:2},{emoji:"🍪",group:2},{emoji:"🎂",group:2},{emoji:"🍰",group:2},{emoji:"🥧",group:2},{emoji:"🍫",group:2},{emoji:"🍬",group:2},{emoji:"🍭",group:2},{emoji:"🍮",group:2},{emoji:"🍯",group:2},{emoji:"🍼",group:2},{emoji:"🥛",group:2},{emoji:"☕️",group:2},{emoji:"🍵",group:2},{emoji:"🍶",group:2},{emoji:"🍾",group:2},{emoji:"🍷",group:2},{emoji:"🍸️",group:2},{emoji:"🍹",group:2},{emoji:"🍺",group:2},{emoji:"🍻",group:2},{emoji:"🥂",group:2},{emoji:"🥃",group:2},{emoji:"🥤",group:2},{emoji:"🥢",group:2},{emoji:"🍽️",group:2},{emoji:"🍴",group:2},{emoji:"🥄",group:2},{emoji:"🔪",group:2},{emoji:"🏺",group:2},{emoji:"🌍️",group:3},{emoji:"🌎️",group:3},{emoji:"🌏️",group:3},{emoji:"🌐",group:3},{emoji:"🗺️",group:3},{emoji:"🗾",group:3},{emoji:"🏔️",group:3},{emoji:"⛰️",group:3},{emoji:"🌋",group:3},{emoji:"🗻",group:3},{emoji:"🏕️",group:3},{emoji:"🏖️",group:3},{emoji:"🏜️",group:3},{emoji:"🏝️",group:3},{emoji:"🏞️",group:3},{emoji:"🏟️",group:3},{emoji:"🏛️",group:3},{emoji:"🏗️",group:3},{emoji:"🏘️",group:3},{emoji:"🏙️",group:3},{emoji:"🏚️",group:3},{emoji:"🏠️",group:3},{emoji:"🏡",group:3},{emoji:"🏢",group:3},{emoji:"🏣",group:3},{emoji:"🏤",group:3},{emoji:"🏥",group:3},{emoji:"🏦",group:3},{emoji:"🏨",group:3},{emoji:"🏩",group:3},{emoji:"🏪",group:3},{emoji:"🏫",group:3},{emoji:"🏬",group:3},{emoji:"🏭️",group:3},{emoji:"🏯",group:3},{emoji:"🏰",group:3},{emoji:"💒",group:3},{emoji:"🗼",group:3},{emoji:"🗽",group:3},{emoji:"⛪️",group:3},{emoji:"🕌",group:3},{emoji:"🕍",group:3},{emoji:"⛩️",group:3},{emoji:"🕋",group:3},{emoji:"⛲️",group:3},{emoji:"⛺️",group:3},{emoji:"🌁",group:3},{emoji:"🌃",group:3},{emoji:"🌄",group:3},{emoji:"🌅",group:3},{emoji:"🌆",group:3},{emoji:"🌇",group:3},{emoji:"🌉",group:3},{emoji:"♨️",group:3},{emoji:"🌌",group:3},{emoji:"🎠",group:3},{emoji:"🎡",group:3},{emoji:"🎢",group:3},{emoji:"💈",group:3},{emoji:"🎪",group:3},{emoji:"🎭️",group:3},{emoji:"🖼️",group:3},{emoji:"🎨",group:3},{emoji:"🎰",group:3},{emoji:"🚂",group:3},{emoji:"🚃",group:3},{emoji:"🚄",group:3},{emoji:"🚅",group:3},{emoji:"🚆",group:3},{emoji:"🚇️",group:3},{emoji:"🚈",group:3},{emoji:"🚉",group:3},{emoji:"🚊",group:3},{emoji:"🚝",group:3},{emoji:"🚞",group:3},{emoji:"🚋",group:3},{emoji:"🚌",group:3},{emoji:"🚍️",group:3},{emoji:"🚎",group:3},{emoji:"🚐",group:3},{emoji:"🚑️",group:3},{emoji:"🚒",group:3},{emoji:"🚓",group:3},{emoji:"🚔️",group:3},{emoji:"🚕",group:3},{emoji:"🚖",group:3},{emoji:"🚗",group:3},{emoji:"🚘️",group:3},{emoji:"🚙",group:3},{emoji:"🚚",group:3},{emoji:"🚛",group:3},{emoji:"🚜",group:3},{emoji:"🚲️",group:3},{emoji:"🛴",group:3},{emoji:"🛵",group:3},{emoji:"🚏",group:3},{emoji:"🛣️",group:3},{emoji:"🛤️",group:3},{emoji:"⛽️",group:3},{emoji:"🚨",group:3},{emoji:"🚥",group:3},{emoji:"🚦",group:3},{emoji:"🚧",group:3},{emoji:"🛑",group:3},{emoji:"⚓️",group:3},{emoji:"⛵️",group:3},{emoji:"🛶",group:3},{emoji:"🚤",group:3},{emoji:"🛳️",group:3},{emoji:"⛴️",group:3},{emoji:"🛥️",group:3},{emoji:"🚢",group:3},{emoji:"✈️",group:3},{emoji:"🛩️",group:3},{emoji:"🛫",group:3},{emoji:"🛬",group:3},{emoji:"💺",group:3},{emoji:"🚁",group:3},{emoji:"🚟",group:3},{emoji:"🚠",group:3},{emoji:"🚡",group:3},{emoji:"🛰️",group:3},{emoji:"🚀",group:3},{emoji:"🛸",group:3},{emoji:"🛎️",group:3},{emoji:"🚪",group:3},{emoji:"🛏️",group:3},{emoji:"🛋️",group:3},{emoji:"🚽",group:3},{emoji:"🚿",group:3},{emoji:"🛁",group:3},{emoji:"⌛️",group:3},{emoji:"⏳️",group:3},{emoji:"⌚️",group:3},{emoji:"⏰",group:3},{emoji:"⏱️",group:3},{emoji:"⏲️",group:3},{emoji:"🕰️",group:3},{emoji:"🕛️",group:3},{emoji:"🕧️",group:3},{emoji:"🕐️",group:3},{emoji:"🕜️",group:3},{emoji:"🕑️",group:3},{emoji:"🕝️",group:3},{emoji:"🕒️",group:3},{emoji:"🕞️",group:3},{emoji:"🕓️",group:3},{emoji:"🕟️",group:3},{emoji:"🕔️",group:3},{emoji:"🕠️",group:3},{emoji:"🕕️",group:3},{emoji:"🕡️",group:3},{emoji:"🕖️",group:3},{emoji:"🕢️",group:3},{emoji:"🕗️",group:3},{emoji:"🕣️",group:3},{emoji:"🕘️",group:3},{emoji:"🕤️",group:3},{emoji:"🕙️",group:3},{emoji:"🕥️",group:3},{emoji:"🕚️",group:3},{emoji:"🕦️",group:3},{emoji:"🌑",group:3},{emoji:"🌒",group:3},{emoji:"🌓",group:3},{emoji:"🌔",group:3},{emoji:"🌕️",group:3},{emoji:"🌖",group:3},{emoji:"🌗",group:3},{emoji:"🌘",group:3},{emoji:"🌙",group:3},{emoji:"🌚",group:3},{emoji:"🌛",group:3},{emoji:"🌜️",group:3},{emoji:"🌡️",group:3},{emoji:"☀️",group:3},{emoji:"🌝",group:3},{emoji:"🌞",group:3},{emoji:"⭐️",group:3},{emoji:"🌟",group:3},{emoji:"🌠",group:3},{emoji:"☁️",group:3},{emoji:"⛅️",group:3},{emoji:"⛈️",group:3},{emoji:"🌤️",group:3},{emoji:"🌥️",group:3},{emoji:"🌦️",group:3},{emoji:"🌧️",group:3},{emoji:"🌨️",group:3},{emoji:"🌩️",group:3},{emoji:"🌪️",group:3},{emoji:"🌫️",group:3},{emoji:"🌬️",group:3},{emoji:"🌀",group:3},{emoji:"🌈",group:3},{emoji:"🌂",group:3},{emoji:"☂️",group:3},{emoji:"☔️",group:3},{emoji:"⛱️",group:3},{emoji:"⚡️",group:3},{emoji:"❄️",group:3},{emoji:"☃️",group:3},{emoji:"⛄️",group:3},{emoji:"☄️",group:3},{emoji:"🔥",group:3},{emoji:"💧",group:3},{emoji:"🌊",group:3},{emoji:"🎃",group:4},{emoji:"🎄",group:4},{emoji:"🎆",group:4},{emoji:"🎇",group:4},{emoji:"✨",group:4},{emoji:"🎈",group:4},{emoji:"🎉",group:4},{emoji:"🎊",group:4},{emoji:"🎋",group:4},{emoji:"🎍",group:4},{emoji:"🎎",group:4},{emoji:"🎏",group:4},{emoji:"🎐",group:4},{emoji:"🎑",group:4},{emoji:"🎀",group:4},{emoji:"🎁",group:4},{emoji:"🎗️",group:4},{emoji:"🎟️",group:4},{emoji:"🎫",group:4},{emoji:"🎖️",group:4},{emoji:"🏆️",group:4},{emoji:"🏅",group:4},{emoji:"🥇",group:4},{emoji:"🥈",group:4},{emoji:"🥉",group:4},{emoji:"⚽️",group:4},{emoji:"⚾️",group:4},{emoji:"🏀",group:4},{emoji:"🏐",group:4},{emoji:"🏈",group:4},{emoji:"🏉",group:4},{emoji:"🎾",group:4},{emoji:"🎱",group:4},{emoji:"🎳",group:4},{emoji:"🏏",group:4},{emoji:"🏑",group:4},{emoji:"🏒",group:4},{emoji:"🏓",group:4},{emoji:"🏸",group:4},{emoji:"🥊",group:4},{emoji:"🥋",group:4},{emoji:"🥅",group:4},{emoji:"🎯",group:4},{emoji:"⛳️",group:4},{emoji:"⛸️",group:4},{emoji:"🎣",group:4},{emoji:"🎽",group:4},{emoji:"🎿",group:4},{emoji:"🛷",group:4},{emoji:"🥌",group:4},{emoji:"🎮️",group:4},{emoji:"🕹️",group:4},{emoji:"🎲",group:4},{emoji:"♠️",group:4},{emoji:"♥️",group:4},{emoji:"♦️",group:4},{emoji:"♣️",group:4},{emoji:"🃏",group:4},{emoji:"🀄️",group:4},{emoji:"🎴",group:4},{emoji:"🔇",group:5},{emoji:"🔈️",group:5},{emoji:"🔉",group:5},{emoji:"🔊",group:5},{emoji:"📢",group:5},{emoji:"📣",group:5},{emoji:"📯",group:5},{emoji:"🔔",group:5},{emoji:"🔕",group:5},{emoji:"🎼",group:5},{emoji:"🎵",group:5},{emoji:"🎶",group:5},{emoji:"🎙️",group:5},{emoji:"🎚️",group:5},{emoji:"🎛️",group:5},{emoji:"🎤",group:5},{emoji:"🎧️",group:5},{emoji:"📻️",group:5},{emoji:"🎷",group:5},{emoji:"🎸",group:5},{emoji:"🎹",group:5},{emoji:"🎺",group:5},{emoji:"🎻",group:5},{emoji:"🥁",group:5},{emoji:"📱",group:5},{emoji:"📲",group:5},{emoji:"☎️",group:5},{emoji:"📞",group:5},{emoji:"📟️",group:5},{emoji:"📠",group:5},{emoji:"🔋",group:5},{emoji:"🔌",group:5},{emoji:"💻️",group:5},{emoji:"🖥️",group:5},{emoji:"🖨️",group:5},{emoji:"⌨️",group:5},{emoji:"🖱️",group:5},{emoji:"🖲️",group:5},{emoji:"💽",group:5},{emoji:"💾",group:5},{emoji:"💿️",group:5},{emoji:"📀",group:5},{emoji:"🎥",group:5},{emoji:"🎞️",group:5},{emoji:"📽️",group:5},{emoji:"🎬️",group:5},{emoji:"📺️",group:5},{emoji:"📷️",group:5},{emoji:"📸",group:5},{emoji:"📹️",group:5},{emoji:"📼",group:5},{emoji:"🔍️",group:5},{emoji:"🔎",group:5},{emoji:"🔬",group:5},{emoji:"🔭",group:5},{emoji:"📡",group:5},{emoji:"🕯️",group:5},{emoji:"💡",group:5},{emoji:"🔦",group:5},{emoji:"🏮",group:5},{emoji:"📔",group:5},{emoji:"📕",group:5},{emoji:"📖",group:5},{emoji:"📗",group:5},{emoji:"📘",group:5},{emoji:"📙",group:5},{emoji:"📚️",group:5},{emoji:"📓",group:5},{emoji:"📒",group:5},{emoji:"📃",group:5},{emoji:"📜",group:5},{emoji:"📄",group:5},{emoji:"📰",group:5},{emoji:"🗞️",group:5},{emoji:"📑",group:5},{emoji:"🔖",group:5},{emoji:"🏷️",group:5},{emoji:"💰️",group:5},{emoji:"💴",group:5},{emoji:"💵",group:5},{emoji:"💶",group:5},{emoji:"💷",group:5},{emoji:"💸",group:5},{emoji:"💳️",group:5},{emoji:"💹",group:5},{emoji:"💱",group:5},{emoji:"💲",group:5},{emoji:"✉️",group:5},{emoji:"📧",group:5},{emoji:"📨",group:5},{emoji:"📩",group:5},{emoji:"📤️",group:5},{emoji:"📥️",group:5},{emoji:"📦️",group:5},{emoji:"📫️",group:5},{emoji:"📪️",group:5},{emoji:"📬️",group:5},{emoji:"📭️",group:5},{emoji:"📮",group:5},{emoji:"🗳️",group:5},{emoji:"✏️",group:5},{emoji:"✒️",group:5},{emoji:"🖋️",group:5},{emoji:"🖊️",group:5},{emoji:"🖌️",group:5},{emoji:"🖍️",group:5},{emoji:"📝",group:5},{emoji:"💼",group:5},{emoji:"📁",group:5},{emoji:"📂",group:5},{emoji:"🗂️",group:5},{emoji:"📅",group:5},{emoji:"📆",group:5},{emoji:"🗒️",group:5},{emoji:"🗓️",group:5},{emoji:"📇",group:5},{emoji:"📈",group:5},{emoji:"📉",group:5},{emoji:"📊",group:5},{emoji:"📋️",group:5},{emoji:"📌",group:5},{emoji:"📍",group:5},{emoji:"📎",group:5},{emoji:"🖇️",group:5},{emoji:"📏",group:5},{emoji:"📐",group:5},{emoji:"✂️",group:5},{emoji:"🗃️",group:5},{emoji:"🗄️",group:5},{emoji:"🗑️",group:5},{emoji:"🔒️",group:5},{emoji:"🔓️",group:5},{emoji:"🔏",group:5},{emoji:"🔐",group:5},{emoji:"🔑",group:5},{emoji:"🗝️",group:5},{emoji:"🔨",group:5},{emoji:"⛏️",group:5},{emoji:"⚒️",group:5},{emoji:"🛠️",group:5},{emoji:"🗡️",group:5},{emoji:"⚔️",group:5},{emoji:"🔫",group:5},{emoji:"🏹",group:5},{emoji:"🛡️",group:5},{emoji:"🔧",group:5},{emoji:"🔩",group:5},{emoji:"⚙️",group:5},{emoji:"🗜️",group:5},{emoji:"⚗️",group:5},{emoji:"⚖️",group:5},{emoji:"🔗",group:5},{emoji:"⛓️",group:5},{emoji:"💉",group:5},{emoji:"💊",group:5},{emoji:"🚬",group:5},{emoji:"⚰️",group:5},{emoji:"⚱️",group:5},{emoji:"🗿",group:5},{emoji:"🛢️",group:5},{emoji:"🔮",group:5},{emoji:"🛒",group:5},{emoji:"🏧",group:6},{emoji:"🚮",group:6},{emoji:"🚰",group:6},{emoji:"♿️",group:6},{emoji:"🚹️",group:6},{emoji:"🚺️",group:6},{emoji:"🚻",group:6},{emoji:"🚼️",group:6},{emoji:"🚾",group:6},{emoji:"🛂",group:6},{emoji:"🛃",group:6},{emoji:"🛄",group:6},{emoji:"🛅",group:6},{emoji:"⚠️",group:6},{emoji:"🚸",group:6},{emoji:"⛔️",group:6},{emoji:"🚫",group:6},{emoji:"🚳",group:6},{emoji:"🚭️",group:6},{emoji:"🚯",group:6},{emoji:"🚱",group:6},{emoji:"🚷",group:6},{emoji:"📵",group:6},{emoji:"🔞",group:6},{emoji:"☢️",group:6},{emoji:"☣️",group:6},{emoji:"⬆️",group:6},{emoji:"↗️",group:6},{emoji:"➡️",group:6},{emoji:"↘️",group:6},{emoji:"⬇️",group:6},{emoji:"↙️",group:6},{emoji:"⬅️",group:6},{emoji:"↖️",group:6},{emoji:"↕️",group:6},{emoji:"↔️",group:6},{emoji:"↩️",group:6},{emoji:"↪️",group:6},{emoji:"⤴️",group:6},{emoji:"⤵️",group:6},{emoji:"🔃",group:6},{emoji:"🔄",group:6},{emoji:"🔙",group:6},{emoji:"🔚",group:6},{emoji:"🔛",group:6},{emoji:"🔜",group:6},{emoji:"🔝",group:6},{emoji:"🛐",group:6},{emoji:"⚛️",group:6},{emoji:"🕉️",group:6},{emoji:"✡️",group:6},{emoji:"☸️",group:6},{emoji:"☯️",group:6},{emoji:"✝️",group:6},{emoji:"☦️",group:6},{emoji:"☪️",group:6},{emoji:"☮️",group:6},{emoji:"🕎",group:6},{emoji:"🔯",group:6},{emoji:"♈️",group:6},{emoji:"♉️",group:6},{emoji:"♊️",group:6},{emoji:"♋️",group:6},{emoji:"♌️",group:6},{emoji:"♍️",group:6},{emoji:"♎️",group:6},{emoji:"♏️",group:6},{emoji:"♐️",group:6},{emoji:"♑️",group:6},{emoji:"♒️",group:6},{emoji:"♓️",group:6},{emoji:"⛎",group:6},{emoji:"🔀",group:6},{emoji:"🔁",group:6},{emoji:"🔂",group:6},{emoji:"▶️",group:6},{emoji:"⏩️",group:6},{emoji:"⏭️",group:6},{emoji:"⏯️",group:6},{emoji:"◀️",group:6},{emoji:"⏪️",group:6},{emoji:"⏮️",group:6},{emoji:"🔼",group:6},{emoji:"⏫",group:6},{emoji:"🔽",group:6},{emoji:"⏬",group:6},{emoji:"⏸️",group:6},{emoji:"⏹️",group:6},{emoji:"⏺️",group:6},{emoji:"⏏️",group:6},{emoji:"🎦",group:6},{emoji:"🔅",group:6},{emoji:"🔆",group:6},{emoji:"📶",group:6},{emoji:"📳",group:6},{emoji:"📴",group:6},{emoji:"♀️",group:6},{emoji:"♂️",group:6},{emoji:"⚕️",group:6},{emoji:"♻️",group:6},{emoji:"⚜️",group:6},{emoji:"🔱",group:6},{emoji:"📛",group:6},{emoji:"🔰",group:6},{emoji:"⭕️",group:6},{emoji:"✅",group:6},{emoji:"☑️",group:6},{emoji:"✔️",group:6},{emoji:"✖️",group:6},{emoji:"❌",group:6},{emoji:"❎",group:6},{emoji:"➕",group:6},{emoji:"➖",group:6},{emoji:"➗",group:6},{emoji:"➰",group:6},{emoji:"➿",group:6},{emoji:"〽️",group:6},{emoji:"✳️",group:6},{emoji:"✴️",group:6},{emoji:"❇️",group:6},{emoji:"‼️",group:6},{emoji:"⁉️",group:6},{emoji:"❓️",group:6},{emoji:"❔",group:6},{emoji:"❕",group:6},{emoji:"❗️",group:6},{emoji:"〰️",group:6},{emoji:"©️",group:6},{emoji:"®️",group:6},{emoji:"™️",group:6},{emoji:"#️⃣",group:6},{emoji:"*️⃣",group:6},{emoji:"0️⃣",group:6},{emoji:"1️⃣",group:6},{emoji:"2️⃣",group:6},{emoji:"3️⃣",group:6},{emoji:"4️⃣",group:6},{emoji:"5️⃣",group:6},{emoji:"6️⃣",group:6},{emoji:"7️⃣",group:6},{emoji:"8️⃣",group:6},{emoji:"9️⃣",group:6},{emoji:"🔟",group:6},{emoji:"💯",group:6},{emoji:"🔠",group:6},{emoji:"🔡",group:6},{emoji:"🔢",group:6},{emoji:"🔣",group:6},{emoji:"🔤",group:6},{emoji:"🅰️",group:6},{emoji:"🆎",group:6},{emoji:"🅱️",group:6},{emoji:"🆑",group:6},{emoji:"🆒",group:6},{emoji:"🆓",group:6},{emoji:"ℹ️",group:6},{emoji:"🆔",group:6},{emoji:"Ⓜ️",group:6},{emoji:"🆕",group:6},{emoji:"🆖",group:6},{emoji:"🅾️",group:6},{emoji:"🆗",group:6},{emoji:"🅿️",group:6},{emoji:"🆘",group:6},{emoji:"🆙",group:6},{emoji:"🆚",group:6},{emoji:"🈁",group:6},{emoji:"🈂️",group:6},{emoji:"🈷️",group:6},{emoji:"🈶",group:6},{emoji:"🈯️",group:6},{emoji:"🉐",group:6},{emoji:"🈹",group:6},{emoji:"🈚️",group:6},{emoji:"🈲",group:6},{emoji:"🉑",group:6},{emoji:"🈸",group:6},{emoji:"🈴",group:6},{emoji:"🈳",group:6},{emoji:"㊗️",group:6},{emoji:"㊙️",group:6},{emoji:"🈺",group:6},{emoji:"🈵",group:6},{emoji:"▪️",group:6},{emoji:"▫️",group:6},{emoji:"◻️",group:6},{emoji:"◼️",group:6},{emoji:"◽️",group:6},{emoji:"◾️",group:6},{emoji:"⬛️",group:6},{emoji:"⬜️",group:6},{emoji:"🔶",group:6},{emoji:"🔷",group:6},{emoji:"🔸",group:6},{emoji:"🔹",group:6},{emoji:"🔺",group:6},{emoji:"🔻",group:6},{emoji:"💠",group:6},{emoji:"🔘",group:6},{emoji:"🔲",group:6},{emoji:"🔳",group:6},{emoji:"⚪️",group:6},{emoji:"⚫️",group:6},{emoji:"🔴",group:6},{emoji:"🔵",group:6},{emoji:"🏁",group:7},{emoji:"🚩",group:7},{emoji:"🎌",group:7},{emoji:"🏴",group:7},{emoji:"🏳️",group:7},{emoji:"🏳️‍🌈",group:7},{emoji:"🇦🇨",group:7},{emoji:"🇦🇩",group:7},{emoji:"🇦🇪",group:7},{emoji:"🇦🇫",group:7},{emoji:"🇦🇬",group:7},{emoji:"🇦🇮",group:7},{emoji:"🇦🇱",group:7},{emoji:"🇦🇲",group:7},{emoji:"🇦🇴",group:7},{emoji:"🇦🇶",group:7},{emoji:"🇦🇷",group:7},{emoji:"🇦🇸",group:7},{emoji:"🇦🇹",group:7},{emoji:"🇦🇺",group:7},{emoji:"🇦🇼",group:7},{emoji:"🇦🇽",group:7},{emoji:"🇦🇿",group:7},{emoji:"🇧🇦",group:7},{emoji:"🇧🇧",group:7},{emoji:"🇧🇩",group:7},{emoji:"🇧🇪",group:7},{emoji:"🇧🇫",group:7},{emoji:"🇧🇬",group:7},{emoji:"🇧🇭",group:7},{emoji:"🇧🇮",group:7},{emoji:"🇧🇯",group:7},{emoji:"🇧🇱",group:7},{emoji:"🇧🇲",group:7},{emoji:"🇧🇳",group:7},{emoji:"🇧🇴",group:7},{emoji:"🇧🇶",group:7},{emoji:"🇧🇷",group:7},{emoji:"🇧🇸",group:7},{emoji:"🇧🇹",group:7},{emoji:"🇧🇻",group:7},{emoji:"🇧🇼",group:7},{emoji:"🇧🇾",group:7},{emoji:"🇧🇿",group:7},{emoji:"🇨🇦",group:7},{emoji:"🇨🇨",group:7},{emoji:"🇨🇩",group:7},{emoji:"🇨🇫",group:7},{emoji:"🇨🇬",group:7},{emoji:"🇨🇭",group:7},{emoji:"🇨🇮",group:7},{emoji:"🇨🇰",group:7},{emoji:"🇨🇱",group:7},{emoji:"🇨🇲",group:7},{emoji:"🇨🇳",group:7},{emoji:"🇨🇴",group:7},{emoji:"🇨🇵",group:7},{emoji:"🇨🇷",group:7},{emoji:"🇨🇺",group:7},{emoji:"🇨🇻",group:7},{emoji:"🇨🇼",group:7},{emoji:"🇨🇽",group:7},{emoji:"🇨🇾",group:7},{emoji:"🇨🇿",group:7},{emoji:"🇩🇪",group:7},{emoji:"🇩🇬",group:7},{emoji:"🇩🇯",group:7},{emoji:"🇩🇰",group:7},{emoji:"🇩🇲",group:7},{emoji:"🇩🇴",group:7},{emoji:"🇩🇿",group:7},{emoji:"🇪🇦",group:7},{emoji:"🇪🇨",group:7},{emoji:"🇪🇪",group:7},{emoji:"🇪🇬",group:7},{emoji:"🇪🇭",group:7},{emoji:"🇪🇷",group:7},{emoji:"🇪🇸",group:7},{emoji:"🇪🇹",group:7},{emoji:"🇪🇺",group:7},{emoji:"🇫🇮",group:7},{emoji:"🇫🇯",group:7},{emoji:"🇫🇰",group:7},{emoji:"🇫🇲",group:7},{emoji:"🇫🇴",group:7},{emoji:"🇫🇷",group:7},{emoji:"🇬🇦",group:7},{emoji:"🇬🇧",group:7},{emoji:"🇬🇩",group:7},{emoji:"🇬🇪",group:7},{emoji:"🇬🇫",group:7},{emoji:"🇬🇬",group:7},{emoji:"🇬🇭",group:7},{emoji:"🇬🇮",group:7},{emoji:"🇬🇱",group:7},{emoji:"🇬🇲",group:7},{emoji:"🇬🇳",group:7},{emoji:"🇬🇵",group:7},{emoji:"🇬🇶",group:7},{emoji:"🇬🇷",group:7},{emoji:"🇬🇸",group:7},{emoji:"🇬🇹",group:7},{emoji:"🇬🇺",group:7},{emoji:"🇬🇼",group:7},{emoji:"🇬🇾",group:7},{emoji:"🇭🇰",group:7},{emoji:"🇭🇲",group:7},{emoji:"🇭🇳",group:7},{emoji:"🇭🇷",group:7},{emoji:"🇭🇹",group:7},{emoji:"🇭🇺",group:7},{emoji:"🇮🇨",group:7},{emoji:"🇮🇩",group:7},{emoji:"🇮🇪",group:7},{emoji:"🇮🇱",group:7},{emoji:"🇮🇲",group:7},{emoji:"🇮🇳",group:7},{emoji:"🇮🇴",group:7},{emoji:"🇮🇶",group:7},{emoji:"🇮🇷",group:7},{emoji:"🇮🇸",group:7},{emoji:"🇮🇹",group:7},{emoji:"🇯🇪",group:7},{emoji:"🇯🇲",group:7},{emoji:"🇯🇴",group:7},{emoji:"🇯🇵",group:7},{emoji:"🇰🇪",group:7},{emoji:"🇰🇬",group:7},{emoji:"🇰🇭",group:7},{emoji:"🇰🇮",group:7},{emoji:"🇰🇲",group:7},{emoji:"🇰🇳",group:7},{emoji:"🇰🇵",group:7},{emoji:"🇰🇷",group:7},{emoji:"🇰🇼",group:7},{emoji:"🇰🇾",group:7},{emoji:"🇰🇿",group:7},{emoji:"🇱🇦",group:7},{emoji:"🇱🇧",group:7},{emoji:"🇱🇨",group:7},{emoji:"🇱🇮",group:7},{emoji:"🇱🇰",group:7},{emoji:"🇱🇷",group:7},{emoji:"🇱🇸",group:7},{emoji:"🇱🇹",group:7},{emoji:"🇱🇺",group:7},{emoji:"🇱🇻",group:7},{emoji:"🇱🇾",group:7},{emoji:"🇲🇦",group:7},{emoji:"🇲🇨",group:7},{emoji:"🇲🇩",group:7},{emoji:"🇲🇪",group:7},{emoji:"🇲🇫",group:7},{emoji:"🇲🇬",group:7},{emoji:"🇲🇭",group:7},{emoji:"🇲🇰",group:7},{emoji:"🇲🇱",group:7},{emoji:"🇲🇲",group:7},{emoji:"🇲🇳",group:7},{emoji:"🇲🇴",group:7},{emoji:"🇲🇵",group:7},{emoji:"🇲🇶",group:7},{emoji:"🇲🇷",group:7},{emoji:"🇲🇸",group:7},{emoji:"🇲🇹",group:7},{emoji:"🇲🇺",group:7},{emoji:"🇲🇻",group:7},{emoji:"🇲🇼",group:7},{emoji:"🇲🇽",group:7},{emoji:"🇲🇾",group:7},{emoji:"🇲🇿",group:7},{emoji:"🇳🇦",group:7},{emoji:"🇳🇨",group:7},{emoji:"🇳🇪",group:7},{emoji:"🇳🇫",group:7},{emoji:"🇳🇬",group:7},{emoji:"🇳🇮",group:7},{emoji:"🇳🇱",group:7},{emoji:"🇳🇴",group:7},{emoji:"🇳🇵",group:7},{emoji:"🇳🇷",group:7},{emoji:"🇳🇺",group:7},{emoji:"🇳🇿",group:7},{emoji:"🇴🇲",group:7},{emoji:"🇵🇦",group:7},{emoji:"🇵🇪",group:7},{emoji:"🇵🇫",group:7},{emoji:"🇵🇬",group:7},{emoji:"🇵🇭",group:7},{emoji:"🇵🇰",group:7},{emoji:"🇵🇱",group:7},{emoji:"🇵🇲",group:7},{emoji:"🇵🇳",group:7},{emoji:"🇵🇷",group:7},{emoji:"🇵🇸",group:7},{emoji:"🇵🇹",group:7},{emoji:"🇵🇼",group:7},{emoji:"🇵🇾",group:7},{emoji:"🇶🇦",group:7},{emoji:"🇷🇪",group:7},{emoji:"🇷🇴",group:7},{emoji:"🇷🇸",group:7},{emoji:"🇷🇺",group:7},{emoji:"🇷🇼",group:7},{emoji:"🇸🇦",group:7},{emoji:"🇸🇧",group:7},{emoji:"🇸🇨",group:7},{emoji:"🇸🇩",group:7},{emoji:"🇸🇪",group:7},{emoji:"🇸🇬",group:7},{emoji:"🇸🇭",group:7},{emoji:"🇸🇮",group:7},{emoji:"🇸🇯",group:7},{emoji:"🇸🇰",group:7},{emoji:"🇸🇱",group:7},{emoji:"🇸🇲",group:7},{emoji:"🇸🇳",group:7},{emoji:"🇸🇴",group:7},{emoji:"🇸🇷",group:7},{emoji:"🇸🇸",group:7},{emoji:"🇸🇹",group:7},{emoji:"🇸🇻",group:7},{emoji:"🇸🇽",group:7},{emoji:"🇸🇾",group:7},{emoji:"🇸🇿",group:7},{emoji:"🇹🇦",group:7},{emoji:"🇹🇨",group:7},{emoji:"🇹🇩",group:7},{emoji:"🇹🇫",group:7},{emoji:"🇹🇬",group:7},{emoji:"🇹🇭",group:7},{emoji:"🇹🇯",group:7},{emoji:"🇹🇰",group:7},{emoji:"🇹🇱",group:7},{emoji:"🇹🇲",group:7},{emoji:"🇹🇳",group:7},{emoji:"🇹🇴",group:7},{emoji:"🇹🇷",group:7},{emoji:"🇹🇹",group:7},{emoji:"🇹🇻",group:7},{emoji:"🇹🇼",group:7},{emoji:"🇹🇿",group:7},{emoji:"🇺🇦",group:7},{emoji:"🇺🇬",group:7},{emoji:"🇺🇲",group:7},{emoji:"🇺🇳",group:7},{emoji:"🇺🇸",group:7},{emoji:"🇺🇾",group:7},{emoji:"🇺🇿",group:7},{emoji:"🇻🇦",group:7},{emoji:"🇻🇨",group:7},{emoji:"🇻🇪",group:7},{emoji:"🇻🇬",group:7},{emoji:"🇻🇮",group:7},{emoji:"🇻🇳",group:7},{emoji:"🇻🇺",group:7},{emoji:"🇼🇫",group:7},{emoji:"🇼🇸",group:7},{emoji:"🇽🇰",group:7},{emoji:"🇾🇪",group:7},{emoji:"🇾🇹",group:7},{emoji:"🇿🇦",group:7},{emoji:"🇿🇲",group:7},{emoji:"🇿🇼",group:7},{emoji:"🏴󠁧󠁢󠁥󠁮󠁧󠁿",group:7},{emoji:"🏴󠁧󠁢󠁳󠁣󠁴󠁿",group:7},{emoji:"🏴󠁧󠁢󠁷󠁬󠁳󠁿",group:7}],Qt=function(e){function t(e){var o;return Object(a.a)(this,t),(o=Object(u.a)(this,Object(s.a)(t).call(this,e))).state={id:e.id,descriptionID:e.descriptionID,titleID:e.titleID},o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e,t=this.props.additionalClassRoot,o=y()("richEditor-menu","richEditorFlyout",(e={},Object(f.a)(e,t,!!t),Object(f.a)(e,"isHidden",!this.props.isVisible),e));o+=this.props.className?" ".concat(this.props.className):"";var n=y()("richEditorFlyout-header",Object(f.a)({},t+"-header",!!t)),i=y()("richEditorFlyout-body",Object(f.a)({},t+"-body",!!t)),a=y()("richEditorFlyout-footer",Object(f.a)({},t+"-footer",!!t)),l=this.props.alertMessage?r.a.createElement("span",{"aria-live":"assertive",role:"alert",className:"sr-only"},this.props.alertMessage):null,u=this.props.accessibleDescription?r.a.createElement("div",{id:this.state.descriptionID,className:"sr-only"},this.props.accessibleDescription):null;return r.a.createElement("div",{id:this.state.id,"aria-describedby":this.state.descriptionID,"aria-labelledby":this.state.titleID,className:o,role:"dialog","aria-hidden":!this.props.isVisible,style:Object(k.a)(!!this.props.renderAbove,!!this.props.renderLeft,this.props.legacyMode)},l,r.a.createElement("div",{className:n},r.a.createElement("h2",{id:this.props.titleID,tabIndex:-1,className:"richEditorFlyout-title",ref:this.props.titleRef},this.props.title),u,r.a.createElement(T.a,{onClick:this.props.onCloseClick,className:"richEditor-close",legacyMode:this.props.legacyMode}),this.props.additionalHeaderContent&&this.props.additionalHeaderContent),r.a.createElement("div",{className:i},this.props.body&&this.props.body),r.a.createElement("div",{className:a},this.props.footer&&this.props.footer))}}]),t}(r.a.Component),eo=Object(h.b)(Qt),to=o(45),oo=o(24),no=function(e){function t(e){var o;return Object(a.a)(this,t),o=Object(u.a)(this,Object(s.a)(t).call(this,e)),Object(f.a)(Object(p.a)(Object(p.a)(o)),"emojiChar",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"quill",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"domButton",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"insertEmojiBlot",function(e){var t=o.quill.getSelection(!0);o.quill.insertEmbed(t.index,"emoji",{emojiChar:o.emojiChar},to.a.sources.USER),o.quill.setSelection(t.index+1,0,to.a.sources.SILENT),o.props.closeMenuHandler(e)}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"handleKeyPress",function(e){switch(e.key){case"ArrowDown":e.stopPropagation(),e.preventDefault(),o.props.onKeyDown();break;case"ArrowUp":e.stopPropagation(),e.preventDefault(),o.props.onKeyUp();break;case"ArrowRight":e.stopPropagation(),e.preventDefault(),o.props.onKeyRight();break;case"ArrowLeft":e.stopPropagation(),e.preventDefault(),o.props.onKeyLeft()}}),o.emojiChar=e.emojiData.emoji,o.quill=e.quill,o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e=this,t=y()("richEditor-button","richEditor-insertEmoji","emojiChar-"+this.emojiChar);return r.a.createElement("button",{ref:function(t){e.domButton=t},onKeyDown:this.handleKeyPress,style:this.props.style,className:t,type:"button",onClick:this.insertEmojiBlot},r.a.createElement("span",{className:"safeEmoji",dangerouslySetInnerHTML:{__html:Object(oo.a)(this.emojiChar)}}))}},{key:"componentDidUpdate",value:function(){this.checkFocus()}},{key:"componentDidMount",value:function(){this.checkFocus()}},{key:"checkFocus",value:function(){this.domButton&&this.props.activeIndex===this.props.index&&this.domButton.focus()}}]),t}(r.a.Component),ro=Object(h.b)(no),io=function(e){function t(){var e,o;Object(a.a)(this,t);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return o=Object(u.a)(this,(e=Object(s.a)(t)).call.apply(e,[this].concat(r))),Object(f.a)(Object(p.a)(Object(p.a)(o)),"handleClick",function(e){e.preventDefault(),e.stopPropagation(),o.props.navigateToGroup(o.props.groupIndex)}),o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e=this.props,t=e.name,o=e.icon,n=(e.groupIndex,e.isSelected),a=y()("richEditor-button","emojiGroup",{isSelected:n});return r.a.createElement("button",{type:"button",onClick:this.handleClick,"aria-current":n,"aria-label":Object(i.m)("Jump to emoji category: ")+Object(i.m)(t),title:Object(i.m)(t),className:a},o,r.a.createElement("span",{className:"sr-only"},Object(i.m)("Jump to emoji category: ")+Object(i.m)(t)))}}]),t}(r.a.Component),ao=7,lo=7,uo={},so={};Xt.forEach(function(e,t){var o=e.group;o in uo||(uo[o]=Math.floor(t/ao),so[o]=t)});var co=Xt.length-1,po=function(e){function t(e){var o;return Object(a.a)(this,t),o=Object(u.a)(this,Object(s.a)(t).call(this,e)),Object(f.a)(Object(p.a)(Object(p.a)(o)),"categoryPickerID",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"gridEl",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"lastRowIndex",o.getRowFromIndex(Xt.length)),Object(f.a)(Object(p.a)(Object(p.a)(o)),"handleOnSectionRendered",function(e){var t=e.rowStartIndex,n=0;Object.values(uo).map(function(e,o){t>=e&&(n=o)}),o.setState({rowStartIndex:e.rowStartIndex,selectedGroupIndex:n,alertMessage:Object(i.m)("In emoji category: ")+Object(i.m)(Jt[n].name),title:Object(i.m)(Jt[n].name)})}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"scrollToCategory",function(e){var t=so[e];o.setState({activeIndex:t,scrollToRow:o.getRowFromIndex(t),alertMessage:Object(i.m)("Jumped to emoji category: ")+Object(i.m)(Jt[e].name)})}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"cellRenderer",function(e){var t=e.columnIndex,n=e.rowIndex,i=e.style,a=n*lo+t,l=Xt[a];return l?r.a.createElement(ro,{activeIndex:o.state.activeIndex,style:i,closeMenuHandler:o.props.closeMenuHandler,key:"emoji-"+l.emoji,emojiData:l,index:a,onKeyUp:o.jumpRowUp,onKeyDown:o.jumpRowDown,onKeyLeft:o.jumpIndexLeft,onKeyRight:o.jumpIndexRight}):null}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"focusOnCategories",function(){var e=document.getElementById(o.categoryPickerID);if(e){var t=e.querySelector(".richEditor-button");t instanceof HTMLElement&&t.focus()}}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"jumpIndex",function(e){var t,n=o.keepEmojiIndexInBounds(o.state.activeIndex+e),r=o.getRowFromIndex(n);r>=o.state.rowStartIndex+lo?t=o.keepRowIndexInBounds(r-lo+1):r<o.state.rowStartIndex&&(t=r),o.setState({activeIndex:n,scrollToRow:t},function(){o.gridEl.forceUpdate()})}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"jumpRowDown",function(){o.jumpIndex(ao)}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"jumpRowUp",function(){o.jumpIndex(-ao)}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"jumpIndexRight",function(){o.jumpIndex(1)}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"jumpIndexLeft",function(){o.jumpIndex(-1)}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"handleKeyDown",function(e){if(o.props.isVisible)switch(e.code){case"PageUp":e.preventDefault(),e.stopImmediatePropagation(),o.jumpIndex(-lo*ao);break;case"PageDown":e.preventDefault(),e.stopImmediatePropagation(),o.jumpIndex(lo*ao);break;case"Home":e.preventDefault(),e.stopImmediatePropagation(),o.jumpIndex(-co),e.stopImmediatePropagation();break;case"End":e.preventDefault(),e.stopImmediatePropagation(),o.jumpIndex(co)}}),o.state={id:e.id,contentID:e.contentID,activeIndex:0,title:Object(i.m)("Emojis"),scrollToRow:0,rowStartIndex:0,selectedGroupIndex:0},o.categoryPickerID="emojiPicker-categories-"+e.editorID,o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e=this,t=[Object(i.m)("Insert an emoji in your message."),Object(i.m)("richEditor.emoji.pagingInstructions")].join(" "),o=r.a.createElement("button",{type:"button",className:"accessibility-jumpTo",onClick:this.focusOnCategories},Object(i.m)("Jump past emoji list, to emoji categories.")),n=r.a.createElement("div",{id:this.categoryPickerID,className:"emojiGroups","aria-label":Object(i.m)("Emoji Categories"),tabIndex:-1},Object.values(Jt).map(function(t,o){var n=t.name,i=t.icon,a=e.state.selectedGroupIndex===o;return r.a.createElement(io,{key:o,name:n,icon:i,isSelected:a,navigateToGroup:e.scrollToCategory,groupIndex:o})})),a=r.a.createElement(Ne,null,function(t){var o=t.height,n=t.width;return r.a.createElement(Le,{containerRole:"",cellRenderer:e.cellRenderer,columnCount:ao,columnWidth:36,rowCount:e.lastRowIndex+1,rowHeight:36,height:o,width:n,overscanRowCount:20,tabIndex:-1,scrollToAlignment:"start",scrollToRow:e.state.scrollToRow,"aria-readonly":void 0,"aria-label":"",role:"",onSectionRendered:e.handleOnSectionRendered,ref:function(t){e.gridEl=t}})});return r.a.createElement(eo,{id:this.state.id,descriptionID:this.descriptionID,titleID:this.titleID,title:this.state.title,titleRef:this.props.initialFocusRef,accessibleDescription:t,alertMessage:this.state.alertMessage,additionalHeaderContent:o,body:a,footer:n,additionalClassRoot:"insertEmoji",onCloseClick:this.props.closeMenuHandler,isVisible:this.props.isVisible,renderAbove:this.props.renderAbove,renderLeft:this.props.renderLeft})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown,!1)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown,!1)}},{key:"getRowFromIndex",value:function(e){return Math.floor(e/ao)}},{key:"keepRowIndexInBounds",value:function(e){return e<0?0:e>this.lastRowIndex-1?this.lastRowIndex-1:e}},{key:"keepEmojiIndexInBounds",value:function(e){return e<0?0:e>co?co:e}},{key:"descriptionID",get:function(){return this.state.id+"-description"}},{key:"titleID",get:function(){return this.state.id+"-title"}}]),t}(r.a.PureComponent),fo=Object(h.b)(po),ho=function(e){function t(e){var o;return Object(a.a)(this,t),(o=Object(u.a)(this,Object(s.a)(t).call(this,e))).state={id:Object(j.b)(e,"emojiPopover")},o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e=this,t=Object(A.g)();return r.a.createElement(x,{id:this.state.id,className:"emojiPicker",buttonClassName:"richEditor-button richEditor-embedButton",onVisibilityChange:D.c,disabled:this.props.disabled,name:Object(i.m)("Emoji Picker"),buttonContents:t,buttonBaseClass:_.a.ICON,renderAbove:this.props.renderAbove,renderLeft:this.props.renderLeft,openAsModal:!1},function(t){return r.a.createElement(fo,Object(g.a)({},t,{renderAbove:e.props.renderAbove,renderLeft:e.props.renderLeft,contentID:t.id}))})}}]),t}(r.a.Component),mo=o(49),go=o(68),vo=o(15),jo=o(73),bo=o(17),yo=function(e){function t(){return Object(a.a)(this,t),Object(u.a)(this,Object(s.a)(t).apply(this,arguments))}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){return this.hasPermission()?this.props.children:this.props.fallback||null}},{key:"componentDidMount",value:function(){this.props.currentUser.status===vo.LoadStatus.PENDING&&this.props.usersActions.getMe()}},{key:"componentDidCatch",value:function(e,t){Object(bo.g)(e,t)}},{key:"hasPermission",value:function(){var e=this.props,t=e.currentUser,o=e.permission;return Array.isArray(o)||(o=[o]),t.status===vo.LoadStatus.SUCCESS&&!!t.data&&(t.data.isAdmin||this.arrayContainsOneOf(o,t.data.permissions))}},{key:"arrayContainsOneOf",value:function(e,t){return e.some(function(e){return t.indexOf(e)>=0})}}]),t}(r.a.Component),_o=Object(mo.b)(go.a.mapStateToProps,jo.a.mapDispatch)(yo),Oo=o(89),Co=function(e){function t(e){var o;return Object(a.a)(this,t),o=Object(u.a)(this,Object(s.a)(t).call(this,e)),Object(f.a)(Object(p.a)(Object(p.a)(o)),"embedModule",void 0),Object(f.a)(Object(p.a)(Object(p.a)(o)),"clearInput",function(){o.setState({url:""})}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"buttonKeyDownHandler",function(e){Oo.a.match(e.nativeEvent,"enter")&&(e.preventDefault(),e.stopPropagation(),o.state.isInputValid&&o.submitUrl())}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"buttonClickHandler",function(e){e.preventDefault(),o.submitUrl()}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"inputChangeHandler",function(e){var t=e.target.value,n=Object(i.i)(o.normalizeUrl(t));o.setState({url:t,isInputValid:n})}),o.embedModule=e.quill.getModule("embed/insertion"),o.state={id:Object(j.b)(e,"embedPopover"),url:"",isInputValid:!1},o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){var e=this,t=Object(i.m)("Insert Media"),o=Object(A.f)(),n=this.props.legacyMode;return r.a.createElement(x,{id:this.state.id,className:"embedDialogue",onClose:this.clearInput,buttonClassName:"richEditor-button richEditor-embedButton",onVisibilityChange:D.c,disabled:this.props.disabled,name:Object(i.m)("Embed"),buttonContents:o,buttonBaseClass:_.a.CUSTOM,renderAbove:!!this.props.renderAbove,renderLeft:!!this.props.renderLeft,openAsModal:!n&&!!this.props.openAsModal},function(o){var a=o.initialFocusRef,l=o.closeMenuHandler,u=o.isVisible,s=r.a.createElement(r.a.Fragment,null,r.a.createElement("p",{id:e.descriptionID,className:"insertMedia-description richEditor-popoverDescription"},Object(i.m)("Paste the URL of the media you want.")),r.a.createElement("input",{className:y()("InputBox",{inputText:!e.props.legacyMode}),placeholder:Object(i.m)("http://"),value:e.state.url,onChange:e.inputChangeHandler,onKeyDown:e.buttonKeyDownHandler,"aria-labelledby":e.titleID,"aria-describedby":e.descriptionID,ref:a})),c=r.a.createElement(r.a.Fragment,null,n?r.a.createElement("input",{type:"button",className:"Button Primary insertMedia-insert",value:"Insert",disabled:!e.state.isInputValid,"aria-label":"Insert Media",onClick:e.buttonClickHandler}):r.a.createElement(_.b,{className:"insertMedia-insert",disabled:!e.state.isInputValid,onClick:e.buttonClickHandler},Object(i.m)("Insert")));return r.a.createElement(eo,{id:o.id,descriptionID:e.descriptionID,titleID:e.titleID,title:t,body:s,footer:c,additionalClassRoot:"insertMedia",onCloseClick:l,isVisible:u,renderAbove:!!e.props.renderAbove,renderLeft:!!e.props.renderLeft})})}},{key:"submitUrl",value:function(){this.clearInput(),this.embedModule.scrapeMedia(this.normalizeUrl(this.state.url))}},{key:"normalizeUrl",value:function(e){return e.match(/^https?:\/\//)?e:"http://"+e}},{key:"titleID",get:function(){return this.state.id+"-title"}},{key:"descriptionID",get:function(){return this.state.id+"-description"}}]),t}(r.a.PureComponent),So=Object(h.b)(Co),wo=function(e){function t(){var e,o;Object(a.a)(this,t);for(var n=arguments.length,i=new Array(n),l=0;l<n;l++)i[l]=arguments[l];return o=Object(u.a)(this,(e=Object(s.a)(t)).call.apply(e,[this].concat(i))),Object(f.a)(Object(p.a)(Object(p.a)(o)),"inputRef",r.a.createRef()),Object(f.a)(Object(p.a)(Object(p.a)(o)),"isMimeTypeImage",function(e){return e.startsWith("image/")}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"onFakeButtonClick",function(e){o.inputRef&&o.inputRef.current&&o.inputRef.current.click()}),Object(f.a)(Object(p.a)(Object(p.a)(o)),"onInputChange",function(){var e=o.inputRef&&o.inputRef.current&&o.inputRef.current.files&&o.inputRef.current.files[0],t=o.props.quill&&o.props.quill.getModule("embed/insertion");e&&t&&("image"===o.props.type&&Object(bo.e)(e)?t.createImageEmbed(e):t.createFileEmbed(e))}),o}return Object(c.a)(t,e),Object(l.a)(t,[{key:"render",value:function(){return r.a.createElement("button",{className:"richEditor-button richEditor-embedButton richEditor-buttonUpload",type:"button","aria-pressed":"false",disabled:this.props.disabled,onClick:this.onFakeButtonClick},this.icon,r.a.createElement("input",{ref:this.inputRef,onChange:this.onInputChange,className:"richEditor-upload",type:"file",accept:this.inputAccepts}))}},{key:"icon",get:function(){switch(this.props.type){case"file":return Object(A.a)();case"image":return Object(A.j)()}}},{key:"inputAccepts",get:function(){var e=this;switch(this.props.type){case"file":return this.props.allowedMimeTypes.filter(function(t){return!e.isMimeTypeImage(t)}).join(", ");case"image":return this.props.allowedMimeTypes.filter(this.isMimeTypeImage).join(",")}}}]),t}(r.a.Component),xo=Object(h.b)(wo);
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function ko(e){var t=e.isMobile,o=e.isLoading,n=e.legacyMode,a=Object(i.g)("upload.allowedExtensions");return r.a.createElement("div",{className:"richEditor-embedBar",ref:e.barRef},r.a.createElement("ul",{className:"richEditor-menuItems richEditor-inlineMenuItems",role:"menubar","aria-label":Object(i.m)("Inline Level Formatting Menu")},t&&r.a.createElement("li",{className:"richEditor-menuItem",role:"menuitem"},r.a.createElement(P,{disabled:o})),!t&&r.a.createElement("li",{className:"richEditor-menuItem u-richEditorHiddenOnMobile",role:"menuitem"},r.a.createElement(ho,{disabled:o,renderAbove:n})),r.a.createElement(_o,{permission:"uploads.add"},r.a.createElement("li",{className:"richEditor-menuItem",role:"menuitem"},r.a.createElement(xo,{disabled:o,type:"image",allowedMimeTypes:a}))),r.a.createElement("li",{className:"richEditor-menuItem",role:"menuitem"},r.a.createElement(So,{disabled:o})),r.a.createElement(_o,{permission:"uploads.add"},r.a.createElement("li",{className:"richEditor-menuItem",role:"menuitem"},r.a.createElement(xo,{disabled:o,type:"file",allowedMimeTypes:a})))))}o.d(t,"a",function(){return ko})},function(e,t,o){"use strict";var n=o(52),r=o(45),i=o(554),a=o(584),l=o(539),u=o(585),s=o(2),c=o(3),p=o(6),f=o(8),h=o(1),d=o(13),m=o(9),g=o(7),v=o.n(g),j=o(18),b=o(67),y=function(e){function t(){return Object(c.a)(this,t),Object(f.a)(this,Object(h.a)(t).apply(this,arguments))}return Object(m.a)(t,e),Object(p.a)(t,[{key:"format",value:function(e,o){e!==_.blotName||o?Object(d.a)(Object(h.a)(t.prototype),"format",this).call(this,e,o):this.replaceWith(v.a.create(this.statics.scope))}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():Object(d.a)(Object(h.a)(t.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(e,o){return this.parent.isolate(this.offset(this.parent),this.length()),e===this.parent.statics.blotName?(this.parent.replaceWith(e,o),this):(this.parent.unwrap(),Object(d.a)(Object(h.a)(t.prototype),"replaceWith",this).call(this,e,o))}}],[{key:"formats",value:function(e){return e.tagName===this.tagName?void 0:Object(d.a)(Object(h.a)(t),"formats",this).call(this,e)}}]),t}(j.c);y.blotName="list-item",y.tagName="LI";var _=function(e){function t(e){var o;Object(c.a)(this,t),o=Object(f.a)(this,Object(h.a)(t).call(this,e));var n=function(t){if(t.target.parentNode===e){var n=o.statics.formats(e),r=v.a.find(t.target);"checked"===n?r.format("list","unchecked"):"unchecked"===n&&r.format("list","checked")}};return e.addEventListener("touchstart",n),e.addEventListener("mousedown",n),o}return Object(m.a)(t,e),Object(p.a)(t,null,[{key:"create",value:function(e){var o="ordered"===e?"OL":"UL",n=Object(d.a)(Object(h.a)(t),"create",this).call(this,o);return"checked"!==e&&"unchecked"!==e||n.setAttribute("data-checked","checked"===e),n}},{key:"formats",value:function(e){return"OL"===e.tagName?"ordered":"UL"===e.tagName?e.hasAttribute("data-checked")?"true"===e.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}}]),Object(p.a)(t,[{key:"format",value:function(e,t){this.children.length>0&&this.children.tail.format(e,t)}},{key:"formats",value:function(){return Object(s.a)({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(e,o){if(e instanceof y)Object(d.a)(Object(h.a)(t.prototype),"insertBefore",this).call(this,e,o);else{var n=null==o?this.length():o.offset(this),r=this.split(n);r.parent.insertBefore(e,r)}}},{key:"optimize",value:function(e){Object(d.a)(Object(h.a)(t.prototype),"optimize",this).call(this,e);var o=this.next;null!=o&&o.prev===this&&o.statics.blotName===this.statics.blotName&&o.domNode.tagName===this.domNode.tagName&&o.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(o.moveChildren(this),o.remove())}},{key:"replace",value:function(e){if(e.statics.blotName!==this.statics.blotName){var o=v.a.create(this.statics.defaultChild);e.moveChildren(o),this.appendChild(o)}Object(d.a)(Object(h.a)(t.prototype),"replace",this).call(this,e)}}]),t}(b.a);_.blotName="list",_.scope=v.a.Scope.BLOCK_BLOT,_.tagName=["OL","UL"],_.defaultChild="list-item",_.allowedChildren=[y];var O=new(function(e){function t(){return Object(c.a)(this,t),Object(f.a)(this,Object(h.a)(t).apply(this,arguments))}return Object(m.a)(t,e),Object(p.a)(t,[{key:"add",value:function(e,o){if("+1"===o||"-1"===o){var n=this.value(e)||0;o="+1"===o?n+1:n-1}return 0===o?(this.remove(e),!0):Object(d.a)(Object(h.a)(t.prototype),"add",this).call(this,e,o)}},{key:"canAdd",value:function(e,o){return Object(d.a)(Object(h.a)(t.prototype),"canAdd",this).call(this,e,o)||Object(d.a)(Object(h.a)(t.prototype),"canAdd",this).call(this,e,parseInt(o))}},{key:"value",value:function(e){return parseInt(Object(d.a)(Object(h.a)(t.prototype),"value",this).call(this,e))||void 0}}]),t}(v.a.Attributor.Class))("indent","ql-indent",{scope:v.a.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]}),C=o(540),S=o(92),w=o(24),x=function(e){function t(){return Object(c.a)(this,t),Object(f.a)(this,Object(h.a)(t).apply(this,arguments))}return Object(m.a)(t,e),Object(p.a)(t,null,[{key:"create",value:function(e){var o=Object(d.a)(Object(h.a)(t),"create",this).call(this,e);return Object(w.j)()?(o.innerHTML=e.emojiChar,o.classList.add("nativeEmoji")):o.innerHTML=Object(w.a)(e.emojiChar),Object(w.l)(o,"data",e),o}},{key:"value",value:function(e){return Object(w.f)(e,"data")}}]),t}(S.a);Object(s.a)(x,"className","safeEmoji"),Object(s.a)(x,"blotName","emoji"),Object(s.a)(x,"tagName","span");var k=o(685),E=o(614),I=o(5),L=function(e){function t(){return Object(c.a)(this,t),Object(f.a)(this,Object(h.a)(t).apply(this,arguments))}return Object(m.a)(t,e),Object(p.a)(t,null,[{key:"create",value:function(e){var o=Object(d.a)(Object(h.a)(t),"create",this).call(this,e);return o.textContent="@"+e.name,o.dataset.userid=e.userID.toString(),o.dataset.username=e.name,o.href=Object(I.j)(e.name),o}},{key:"value",value:function(e){return{name:e.dataset.username,userID:parseInt(e.dataset.userid||"",10)}}}]),t}(S.a);Object(s.a)(L,"blotName","mention"),Object(s.a)(L,"className","atMention"),Object(s.a)(L,"tagName","a");var R=o(57),T=o(34),M=o(538),A=function(e){function t(e){var o;return Object(c.a)(this,t),o=Object(f.a)(this,Object(h.a)(t).call(this,e)),e.setAttribute("role","combobox"),e.setAttribute("aria-haspopup","listbox"),o}return Object(m.a)(t,e),Object(p.a)(t,[{key:"optimize",value:function(e){Object(d.a)(Object(h.a)(t.prototype),"optimize",this).call(this,e),0===this.children.length&&this.remove()}},{key:"replaceWith",value:function(e,o){var n="string"==typeof e?v.a.create(e,o):e;return Object(d.a)(Object(h.a)(t.prototype),"replaceWith",this).call(this,n)}}]),t}(b.a);Object(s.a)(A,"blotName","mention-combobox"),Object(s.a)(A,"className","atMentionComboBox"),Object(s.a)(A,"tagName","span"),Object(s.a)(A,"scope",v.a.Scope.BLOCK),Object(s.a)(A,"allowedChildren",Object(n.a)(b.a.allowedChildren).concat([M.a,R.a,S.a,T.a]));var z=o(533),N=o(528),P=o(87),D=o(23),F=o(82),H=o(11),W=o(89),G=o(14),q=o.n(G),B=o(16),U=o(65),V=o(550),K=o(90),Z=function(){function e(t){var o=this;Object(c.a)(this,e),this.quill=t,Object(s.a)(this,"bindings",{}),Object(s.a)(this,"handleMultilineEnter",function(e){var t=o.quill.getLine(e.index),n=Object(H.a)(t,1)[0],r=n.getWrapper();return n!==r.children.tail||(""!==n.domNode.textContent||(!n.prev||(Object(U.h)(o.quill,e),!1)))}),Object(s.a)(this,"handleCodeBlockEnter",function(e){var t=o.quill.getLine(e.index),n=Object(H.a)(t,1)[0].domNode.textContent;return!/\n\n\n$/.test(n)||(Object(U.h)(o.quill,e,2),!1)}),Object(s.a)(this,"handleBlockStartDelete",function(e){var t=o.quill.getLine(e.index),n=Object(H.a)(t,1)[0];return!Object(U.k)(n,o.quill)||(Object(U.n)(o.quill),!1)}),Object(s.a)(this,"clearFirstPositionMultiLineBlot",function(e){var t=o.quill.getLine(e.index),n=Object(H.a)(t,1)[0],r=o.quill.getSelection(),i=e.index<r.index,a=e.index+e.length>r.index+r.length,l=0===r.index,u=l,s=n instanceof V.a||n instanceof P.a||n instanceof K.a;if((i||a||u)&&s){var c=new q.a,p=e;l&&(c=c.insert("\n"),p.length+=1),o.quill.updateContents(c,B.a.sources.USER),o.quill.setSelection(p),Object(U.n)(o.quill),o.quill.setSelection(p)}return!0}),Object(s.a)(this,"transformTextToLink",function(e){var t=o.quill.getLine(e.index)[0].offset(),n=o.quill.getText(t,e.index).match(/\s([^\s]+)$|^([^\s]+)$/);if(!n)return!0;var i=n[1]||n[2],a=t+(n.index||0),u=i.length;n[1]&&(a+=1);var s=o.quill.scroll.descendants(function(e){return e instanceof l.a},a,u).length>0;return Object(I.i)(i)&&!s&&o.quill.formatText(a,u,{link:i},r.a.sources.USER),!0}),Object(s.a)(this,"transformLinkOnlyLineToEmbed",function(e){var t=o.quill.getLine(e.index)[0];if(e.index<t.offset()+t.length()-1)return!0;if("block"!==t.statics.blotName)return!0;var n=t.children.head.statics.blotName;if(t.children.length>1||!["text","link"].includes(n))return!0;var i=t.domNode.textContent||"";if(i=i.trim(),Object(I.i)(i)){var a=o.quill.getModule("embed/insertion"),l=t.offset();return o.quill.deleteText(l,t.length(),r.a.sources.USER),o.quill.insertText(l,"\n",r.a.sources.USER),o.quill.setSelection(l,0,r.a.sources.USER),a.scrapeMedia(i),!1}return!0}),this.resetDefaultBindings(),this.addBlockNewLineHandlers(),this.addBlockArrowKeyHandlers(),this.addBlockBackspaceHandlers(),this.addLinkTransformKeyboardBindings(),this.overwriteFormatHandlers()}return Object(p.a)(e,[{key:"handleMultiLineBackspace",value:function(e){var t=this.quill.getLine(e.index),o=Object(H.a)(t,1)[0];if(o.prev||o.next)return!0;if(""!==o.getWrapper().domNode.textContent)return!0;var n=(new q.a).retain(e.index).retain(1,Object(s.a)({},o.constructor.blotName,!1));return this.quill.updateContents(n,B.a.sources.USER),!1}},{key:"handleCodeBlockBackspace",value:function(e){var t=this.quill.getLine(e.index);if("\n"!==Object(H.a)(t,1)[0].domNode.textContent)return!0;var o=(new q.a).retain(e.index).retain(1,Object(s.a)({},P.a.blotName,!1));return this.quill.updateContents(o,B.a.sources.USER),!1}},{key:"insertNewlineBeforeRange",value:function(e){return 0===e.index&&Object(U.j)(this.quill),!0}},{key:"insertNewlineAfterRange",value:function(e){return e.index+1===this.quill.scroll.length()&&Object(U.i)(this.quill),!0}},{key:"resetDefaultBindings",value:function(){this.bindings.tab=!1,this.bindings["indent codeBlock"]=!1,this.bindings["outdent codeBlock"]=!1,this.bindings["remove tab"]=!1,this.bindings["code exit"]=!1}},{key:"addLinkTransformKeyboardBindings",value:function(){this.bindings["transform text to embed"]={key:W.a.keys.ENTER,collapsed:!0,handler:this.transformLinkOnlyLineToEmbed}}},{key:"addBlockNewLineHandlers",value:function(){this.bindings["MutliLine Enter"]={key:W.a.keys.ENTER,collapsed:!0,format:["spoiler-line","blockquote-line"],handler:this.handleMultilineEnter},this.bindings["CodeBlock Enter"]={key:W.a.keys.ENTER,collapsed:!0,format:[P.a.blotName],handler:this.handleCodeBlockEnter}}},{key:"overwriteFormatHandlers",value:function(){this.bindings.bold=this.makeFormatHandler("bold"),this.bindings.italic=this.makeFormatHandler("italic"),this.bindings.underline=this.makeFormatHandler("underline")}},{key:"makeFormatHandler",value:function(e){var t=this;return{key:e[0].toUpperCase(),shortKey:!0,handler:function(o,n){Object(U.m)(t.quill,P.a,o)||Object(U.m)(t.quill,C.a,o)||t.quill.format(e,!n.format[e],r.a.sources.USER)}}}},{key:"addBlockBackspaceHandlers",value:function(){this.bindings["Block Backspace With Selection"]={key:W.a.keys.BACKSPACE,collapsed:!1,handler:this.clearFirstPositionMultiLineBlot},this.bindings["Block Delete"]={key:W.a.keys.BACKSPACE,offset:0,collapsed:!0,format:e.MULTI_LINE_BLOTS,handler:this.handleBlockStartDelete},this.bindings["MultiLine Backspace"]={key:W.a.keys.BACKSPACE,collapsed:!0,format:["spoiler-line","blockquote-line"],handler:this.handleMultiLineBackspace},this.bindings["CodeBlock Backspace"]={key:W.a.keys.BACKSPACE,collapsed:!0,format:[P.a.blotName],handler:this.handleCodeBlockBackspace}}},{key:"addBlockArrowKeyHandlers",value:function(){var t={collapsed:!0,format:e.MULTI_LINE_BLOTS};this.bindings["Block Up"]=Object(D.a)({},t,{key:W.a.keys.UP,handler:this.insertNewlineBeforeRange}),this.bindings["Block Left"]=Object(D.a)({},t,{key:W.a.keys.LEFT,handler:this.insertNewlineBeforeRange}),this.bindings["Block Down"]=Object(D.a)({},t,{key:W.a.keys.DOWN,handler:this.insertNewlineAfterRange}),this.bindings["Block Right"]=Object(D.a)({},t,{key:W.a.keys.RIGHT,handler:this.insertNewlineAfterRange})}}]),e}();Object(s.a)(Z,"MULTI_LINE_BLOTS",[z.c.blotName,N.c.blotName,P.a.blotName]);
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
var $=function(e){function t(e,o){var n;Object(c.a)(this,t);var r=Object(D.a)({},o,{placeholder:"Create a new post...",scrollingContainer:"body"});(n=Object(f.a)(this,Object(h.a)(t).call(this,e,r))).quill.root.classList.add("richEditor-text"),n.quill.root.classList.add("userContent"),n.addModule("embed/insertion"),n.addModule("embed/focus");var i=new Z(n.quill);return n.options.modules.keyboard.bindings=Object(D.a)({},n.options.modules.keyboard.bindings,i.bindings),n}return Object(m.a)(t,e),t}(F.a),Y=o(10),J=o(46),X=o(589),Q=function(e){function t(e){var o,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(Object(c.a)(this,t),o=Object(f.a)(this,Object(h.a)(t).call(this,e,n)),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"lastSelection",{index:0,length:0}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"editorRoot",void 0),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"formWrapper",void 0),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"escapeMobileFullScreen",function(e){var t=window.getComputedStyle(o.editorRoot).getPropertyValue("position");if(o.editorRoot.classList.contains("isFocused")&&"fixed"===t&&W.a.match(e,{key:W.a.keys.ESCAPE,shiftKey:!1})){o.quill.root.focus();var n=new X.a(o.formWrapper).getNext();n&&(n.focus(),o.editorRoot.classList.toggle("isFocused",!1))}}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"handleTab",function(){var e=document.activeElement;o.quill.root.contains(document.activeElement)&&(e=o.quill.root);o.getEmbedBlotForFocusedElement(),K.a;var t=new X.a(o.formWrapper,void 0,[o.quill.root]).getNext(e,!1,!1);return!!t&&(t===o.quill.root?o.focusFirstLine():t.focus(),!0)}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"handleShiftTab",function(){var e=document.activeElement;o.quill.root.contains(document.activeElement)&&(e=o.quill.root);var t=new X.a(o.formWrapper,void 0,[o.quill.root]).getNext(e,!0,!1);return!!t&&(t===o.quill.root?o.focusLastLine():t.focus(),!0)}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"handleArrowKeyFromEmbed",function(e,t){var n=t.offset(o.quill.scroll),r=n+t.length()===o.quill.scroll.length(),i=0===n,a=[W.a.keys.LEFT,W.a.keys.UP].includes(e),l=[W.a.keys.RIGHT,W.a.keys.DOWN].includes(e);if(i&&a)return Object(U.j)(o.quill),!0;if(r&&l)return Object(U.i)(o.quill),!0;var u=o.getNextBlotFromArrowKey(t,e);return!!u&&(o.arrowToBlot(u),!0)}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"handleDeleteOnQuill",function(){var e=o.quill.getSelection(),t=e&&o.quill.getLine(e.index)[0];if(!t)return!1;var n=t.prev,r=""===t.domNode.textContent;return!!(n instanceof K.a&&r)&&(t.remove(),n.focus(),!0)}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"keyDownListener",function(e){if(o.editorRoot.contains(document.activeElement)){var t=o.getEmbedBlotForFocusedElement(),n=o.quill.getLine(o.lastSelection.index),r=Object(H.a)(n,1)[0],i=o.getNextBlotFromArrowKey(r,e.keyCode);if(o.isKeyCodeArrowKey(e.keyCode)&&!e.shiftKey&&!o.inActiveMention)if(t instanceof K.a){if(o.handleArrowKeyFromEmbed(e.keyCode,t))return e.preventDefault(),void e.stopPropagation()}else o.quill.hasFocus()&&i instanceof K.a&&(e.preventDefault(),e.stopPropagation(),i.focus());if(o.isKeyCodeDelete(e.keyCode))if(t instanceof K.a)e.preventDefault(),e.stopPropagation(),t.remove(),Object(U.c)();else if(o.quill.hasFocus()){if(o.handleDeleteOnQuill())return e.preventDefault(),void e.stopPropagation()}W.a.match(e,W.a.keys.ENTER)&&t instanceof K.a&&(e.preventDefault(),e.stopPropagation(),t.insertNewlineAfter())}}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"tabListener",function(e){if(o.formWrapper.contains(document.activeElement)){var t=!1;W.a.match(e,{key:W.a.keys.TAB,shiftKey:!0})?t=o.handleShiftTab():W.a.match(e,{key:W.a.keys.TAB,shiftKey:!1})&&(t=o.handleTab()),t&&(e.preventDefault(),e.stopPropagation())}}),o.editorRoot=o.quill.root.closest(".richEditor"),o.formWrapper=o.editorRoot.closest(".FormWrapper")||o.editorRoot.closest(".FormTitleWrapper"),!o.editorRoot)throw new Error("Cannot initialize the EmbedFocusModule without an editorRoot (.richEditor class)");if(!o.formWrapper)throw new Error("Cannot initialize the EmbedFocusModule without a FormWrapper (.FormWrapper or .FormTitleWrapper class)");return o.quill.root.setAttribute("tabindex",0),e.on("selection-change",function(e,t,n){e&&n!==r.a.sources.SILENT&&(o.lastSelection=e,o.editorRoot.classList.toggle("isFocused",!0))}),o.setupEmbedClickHandler(),o.setupMobileHandler(),o.quill.root.addEventListener("keydown",o.keyDownListener),o.formWrapper.addEventListener("keydown",o.tabListener),o.editorRoot.addEventListener("keydown",o.escapeMobileFullScreen),o}return Object(m.a)(t,e),Object(p.a)(t,[{key:"focusLastLine",value:function(){var e=this.quill.scroll.length()-1,t=Object(U.d)(this.quill,e,K.a);t?t.focus():(this.quill.focus(),this.quill.setSelection(e,0,r.a.sources.USER))}},{key:"focusFirstLine",value:function(){var e=this.quill.scroll.children.head;e instanceof K.a?e.focus():(this.quill.focus(),this.quill.setSelection(0,0,r.a.sources.USER))}},{key:"arrowToBlot",value:function(e){if(e instanceof K.a)e.focus();else{var t=e.offset(this.quill.scroll);this.quill.setSelection(t,0)}}},{key:"getEmbedBlotForFocusedElement",value:function(){if(document.activeElement instanceof Element){var e=document.activeElement;if(!e.classList.contains("js-embed")){var t=e.closest(".js-embed");if(!t)return;e=t}return v.a.find(e)}}},{key:"getNextBlotFromArrowKey",value:function(e,t){if(!e)return null;switch(t){case W.a.keys.DOWN:return e.next;case W.a.keys.UP:return e.prev;case W.a.keys.RIGHT:var o=e.offset()+e.length()-1;if(this.lastSelection.index===o)return e.next;break;case W.a.keys.LEFT:if(this.lastSelection.index===e.offset())return e.prev}}},{key:"setupEmbedClickHandler",value:function(){Object(w.b)("click","a",function(e,t){e.preventDefault(),e.stopPropagation()},this.quill.container),Object(w.b)("click",".js-embed",function(e,t){var o=v.a.find(t);o instanceof K.a&&o.focus()},this.quill.container)}},{key:"setupMobileHandler",value:function(){var e=this;Object(w.b)("click",".js-richText .richEditor-text",function(t,o){e.editorRoot.classList.toggle("isFocused",!0)},this.quill.container),Object(w.b)("click",".js-richEditor-next",function(t,o){var n=new X.a(e.formWrapper).getNext(o);n&&(n.focus(),e.editorRoot.classList.toggle("isFocused",!1))},this.editorRoot)}},{key:"isKeyCodeArrowKey",value:function(e){var t=W.a.keys;return[t.UP,t.DOWN,t.LEFT,t.RIGHT].includes(e)}},{key:"isKeyCodeDelete",value:function(e){var t=W.a.keys;return[t.BACKSPACE,t.DELETE].includes(e)}},{key:"inActiveMention",get:function(){var e={index:0,length:this.quill.scroll.length()-1};return Object(U.m)(this.quill,M.a,e)}}]),t}(J.a),ee=o(25),te=o(93),oe=o(17),ne=o(588),re=function(e){function t(e){var o,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};arguments.length>2&&arguments[2];return Object(c.a)(this,t),(o=Object(f.a)(this,Object(h.a)(t).call(this,e,n))).quill=e,Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"store",Object(te.a)()),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"createEmbed",function(e){var t=v.a.create("embed-external",e),n=o.state.lastGoodSelection||{index:o.quill.scroll.length(),length:0};Object(U.g)(o.quill,n.index,t),o.quill.update(r.a.sources.USER),t.focus()}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"pasteHandler",function(e){var t=Object(w.i)(e);t&&(Object(oe.e)(t)?o.createImageEmbed(t):o.createFileEmbed(t))}),Object(s.a)(Object(Y.a)(Object(Y.a)(o)),"dragHandler",function(e){var t=Object(w.g)(e);t&&(Object(oe.e)(t)?o.createImageEmbed(t):o.createFileEmbed(t))}),o.quill=e,o.setupImageUploads(),o}return Object(m.a)(t,e),Object(p.a)(t,[{key:"scrapeMedia",value:function(e){var t=new FormData;t.append("url",e);var o=ee.a.post("/media/scrape",t).then(function(e){return e.data});this.createEmbed({loaderData:{type:"link",link:e},dataPromise:o})}},{key:"createImageEmbed",value:function(e){var t=Object(ee.d)(e).then(function(e){return e.type="image",e});this.createEmbed({loaderData:{type:"image"},dataPromise:t})}},{key:"createFileEmbed",value:function(e){var t=new ne.a,o=Object(ee.d)(e,{onUploadProgress:t.emit}).then(function(e){return{url:e.url,type:"file",attributes:e}});this.createEmbed({loaderData:{type:"file",file:e,progressEventEmitter:t},dataPromise:o})}},{key:"setupImageUploads",value:function(){this.quill.root.addEventListener("drop",this.dragHandler,!1),this.quill.root.addEventListener("paste",this.pasteHandler,!1)}},{key:"state",get:function(){var e=Object(U.e)(this.quill);return this.store.getState().editor.instances[e]}}]),t}(J.a),ie=o(687),ae=o.n(ie),le=o(235),ue=o(19),se=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",ce=function(e){function t(e,o){var n;return Object(c.a)(this,t),n=Object(f.a)(this,Object(h.a)(t).call(this,e,Object(D.a)({},o))),Object(s.a)(Object(Y.a)(Object(Y.a)(n)),"EMBED_KEY","insert.embed-external"),Object(s.a)(Object(Y.a)(Object(Y.a)(n)),"EMBED_CONFIRM_KEY","insert.embed-external.loaderData.loaded"),Object(s.a)(Object(Y.a)(Object(Y.a)(n)),"CODE_KEY","attributes.codeBlock"),Object(s.a)(Object(Y.a)(Object(Y.a)(n)),"Z_KEYCODE",90),Object(s.a)(Object(Y.a)(Object(Y.a)(n)),"undoKeyboardListener",function(e){e.keyCode===n.Z_KEYCODE&&e[se]&&document.activeElement&&document.activeElement.classList.contains(ue.a)&&(e.shiftKey?n.redo():n.undo())}),document.addEventListener("keydown",n.undoKeyboardListener,!0),n}return Object(m.a)(t,e),Object(p.a)(t,[{key:"record",value:function(e,o){this.operationsContainKey(e.ops,this.EMBED_KEY)&&this.cutoff(),this.operationsContainKey(e.ops,this.CODE_KEY)&&this.cutoff();var n=this.quill.getContents().diff(o);this.actionIsEmbedCompletion(n,e)||Object(d.a)(Object(h.a)(t.prototype),"record",this).call(this,e,o)}},{key:"actionIsEmbedCompletion",value:function(e,t){var o=this.operationsContainKey(t.ops,this.EMBED_CONFIRM_KEY),n=this.operationsContainKey(e.ops,this.EMBED_CONFIRM_KEY);return o||n}},{key:"operationsContainKey",value:function(e,t){if(!e)return!1;var o=!0,n=!1,r=void 0;try{for(var i,a=e[Symbol.iterator]();!(o=(i=a.next()).done);o=!0){var l=i.value;if(ae()(l,t,!1))return!0}}catch(e){n=!0,r=e}finally{try{o||null==a.return||a.return()}finally{if(n)throw r}}return!1}}]),t}(le.a),pe=o(695),fe=o(615),he=o(590),de=function(e){function t(){return Object(c.a)(this,t),Object(f.a)(this,Object(h.a)(t).apply(this,arguments))}return Object(m.a)(t,e),Object(p.a)(t,[{key:"highlight",value:function(){var e=this;if(!this.quill.selection.composing){this.quill.update(r.a.sources.USER);var t=this.quill.getSelection(),o=this.quill.hasFocus();this.quill.scroll.descendants(P.a).forEach(function(t){t.highlight(e.options.highlight)}),this.quill.update(r.a.sources.SILENT),null!=t&&o&&this.quill.setSelection(t,r.a.sources.SILENT)}}}],[{key:"register",value:function(){Object(d.a)(Object(h.a)(t),"register",this).call(this),r.a.register(P.a)}}]),t}(o(236).b);
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function me(){r.a.register({"formats/blockquote/line":N.c,"formats/blockquote/content":N.a,"formats/blockquote/wrapper":N.b,"formats/spoiler/line":z.c,"formats/spoiler/content":z.a,"formats/spoiler/wrapper":z.b,"formats/codeBlock":P.a,"formats/header":he.a,"formats/list":_,"formats/list/item":y,"formats/embed-error":E.b,"formats/embed-loading":fe.a,"formats/embed-external":k.a,"formats/mention":L,"formats/mention-combobox":A,"formats/mention-autocomplete":M.a,"formats/bold":i.a,"formats/codeInline":C.a,"formats/italic":a.a,"formats/link":l.a,"formats/strike":u.a,"formats/emoji":x,"formats/indent":O,"themes/vanilla":$,"modules/embed/insertion":re,"modules/embed/focus":Q,"modules/history":ce,"modules/clipboard":pe.a,"modules/syntax":de},!0);["blots/inline","formats/bold","formats/italic","formats/strike","formats/link","formats/codeInline"].forEach(function(e){var t=r.a.import(e);t.order=Object(n.a)(t.order).concat([C.a.blotName])});["blots/block","formats/list/item","formats/header","formats/bold","formats/italic","formats/strike"].forEach(function(e){var t=r.a.import(e);t.allowedChildren=Object(n.a)(t.allowedChildren).concat([A])})}o.d(t,"a",function(){return me})},function(e,t,o){"use strict";var n=o(3),r=o(6),i=o(8),a=o(1),l=o(9),u=o(10),s=o(2),c=o(0),p=o.n(c),f=o(16),h=o(89),d=o(539),m=o(5),g=o(66),v=o(510),j=o(582),b=function(e){function t(e){var o;return Object(n.a)(this,t),o=Object(i.a)(this,Object(a.a)(t).call(this,e)),Object(s.a)(Object(u.a)(Object(u.a)(o)),"flyoutRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"nubRef",p.a.createRef()),o.state={flyoutHeight:null,flyoutWidth:null,nubHeight:null},o}return Object(l.a)(t,e),Object(r.a)(t,[{key:"render",value:function(){var e=this,t=this.props,o=t.isVisible,n=t.selection;return p.a.createElement(j.a,Object(g.a)({},this.state,{selectionIndex:n.index,selectionLength:n.length,isActive:o}),function(t){var n=t.x,r=t.y,i={visibility:"hidden",position:"absolute"},a={},l="richEditor-inlineToolbarContainer richEditor-toolbarContainer ";return n&&r&&o&&(i={position:"absolute",top:r?r.position:0,left:n?n.position:0,zIndex:5,visibility:"visible"},a={left:n?n.nubPosition:0,top:r?r.nubPosition:0},l+=r&&r.nubPointsDown?"isUp":"isDown"),p.a.createElement("div",{className:l,style:i,ref:e.flyoutRef},e.props.children,p.a.createElement("div",{style:a,className:"richEditor-nubPosition",ref:e.nubRef},p.a.createElement("div",{className:"richEditor-nub"})))})}},{key:"componentDidMount",value:function(){this.setState({flyoutWidth:this.flyoutRef.current?this.flyoutRef.current.offsetWidth:null,flyoutHeight:this.flyoutRef.current?this.flyoutRef.current.offsetHeight:null,nubHeight:this.nubRef.current?this.nubRef.current.offsetHeight:null})}}]),t}(p.a.PureComponent),y=Object(v.b)(b),_=o(524),O=function(e){function t(e){var o;return Object(n.a)(this,t),(o=Object(i.a)(this,Object(a.a)(t).call(this,e))).state={linkValue:"",cachedRange:{index:0,length:0},showFormatMenu:!1,showLinkMenu:!1},o}return Object(l.a)(t,e),Object(r.a)(t,[{key:"render",value:function(){return p.a.createElement("div",{className:"richEditor-menu insertLink likeDropDownContent",role:"dialog","aria-label":Object(m.m)("Insert Url")},p.a.createElement("input",{value:this.props.inputValue,onChange:this.props.onInputChange,ref:this.props.inputRef,onKeyDown:this.props.onInputKeyDown,className:"InputBox insertLink-input",placeholder:Object(m.m)("Paste or type a link…")}),p.a.createElement(_.a,{className:"richEditor-close",onClick:this.props.onCloseClick,legacyMode:this.props.legacyMode}))}}]),t}(p.a.PureComponent),C=Object(v.b)(O),S=o(65),w=o(87),x=o(553),k=o(616),E=o(516),I=o(22),L=o.n(I),R=function(e){function t(){var e,o;Object(n.a)(this,t);for(var r=arguments.length,l=new Array(r),c=0;c<r;c++)l[c]=arguments[c];return o=Object(i.a)(this,(e=Object(a.a)(t)).call.apply(e,[this].concat(l))),Object(s.a)(Object(u.a)(Object(u.a)(o)),"formatBold",function(e){e.preventDefault(),o.props.formatter.bold(o.props.lastGoodSelection)}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"formatItalic",function(e){e.preventDefault(),o.props.formatter.italic(o.props.lastGoodSelection)}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"formatStrike",function(e){e.preventDefault(),o.props.formatter.strike(o.props.lastGoodSelection)}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"formatCode",function(e){e.preventDefault(),o.props.formatter.codeInline(o.props.lastGoodSelection)}),o}return Object(l.a)(t,e),Object(r.a)(t,[{key:"render",value:function(){return p.a.createElement(k.a,{menuItemData:this.menuItemData,className:L()("richEditor-inlineToolbarContainer",this.props.className)})}},{key:"menuItemData",get:function(){var e=this.props,t=e.activeFormats,o=e.onLinkClick;return[{label:Object(m.m)("Format as Bold"),icon:E.c(),isActive:!0===t.bold,onClick:this.formatBold},{label:Object(m.m)("Format as Italic"),icon:E.k(),isActive:!0===t.italic,onClick:this.formatItalic},{label:Object(m.m)("Format as Strikethrough"),icon:E.o(),isActive:!0===t.strike,onClick:this.formatStrike},{label:Object(m.m)("Format as Inline Code"),icon:E.d(),isActive:!0===t.code,onClick:this.formatCode},{label:Object(m.m)("Format as Link"),icon:E.l(),isActive:"string"==typeof t.link,onClick:o}]}}]),t}(p.a.PureComponent),T=o(557),M=function(e){function t(e){var o;return Object(n.a)(this,t),o=Object(i.a)(this,Object(a.a)(t).call(this,e)),Object(s.a)(Object(u.a)(Object(u.a)(o)),"quill",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"formatter",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"linkInput",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"selfRef",p.a.createRef()),Object(s.a)(Object(u.a)(Object(u.a)(o)),"focusWatcher",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"ignoreLinkToolbarFocusRequirement",!1),Object(s.a)(Object(u.a)(Object(u.a)(o)),"handleFocusChange",function(e){o.setState({menuHasFocus:e})}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"commandKHandler",function(){var e=o.props.lastGoodSelection,t=Object(S.m)(o.quill,w.a,e);if(o.isOneLineOrLess&&!o.isLinkMenuVisible&&!t)if(Object(S.m)(o.quill,d.a,e))o.formatter.link(e),o.reset();else{var n=o.quill.getText(e.index,e.length);Object(m.i)(n)&&o.setState({inputValue:n}),o.toggleLinkMenu()}}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"toggleLinkMenu",function(e){e&&e.preventDefault(),"string"==typeof o.props.activeFormats.link?(o.setState({isLinkMenuOpen:!1}),o.formatter.link(o.props.lastGoodSelection)):(o.ignoreLinkToolbarFocusRequirement=!0,o.setState({isLinkMenuOpen:!0},function(){o.linkInput.current.focus(),o.ignoreLinkToolbarFocusRequirement=!1}))}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"escFunction",function(e){27===e.keyCode&&(o.isLinkMenuVisible?(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),o.clearLinkInput()):o.isFormatMenuVisible&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),o.cancel()))}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"onCloseClick",function(e){e.preventDefault(),o.clearLinkInput()}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"reset",function(){o.props.lastGoodSelection&&o.quill.setSelection(o.props.lastGoodSelection,f.a.sources.USER),o.setState({inputValue:""})}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"cancel",function(){var e=o.props.lastGoodSelection,t={index:e.index+e.length,length:0};o.setState({inputValue:""}),o.quill.setSelection(t)}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"onInputKeyDown",function(e){h.a.match(e.nativeEvent,"enter")&&(e.preventDefault(),o.formatter.link(o.props.lastGoodSelection,o.state.inputValue),o.clearLinkInput()),o.escFunction(e.nativeEvent)}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"onInputChange",function(e){o.setState({inputValue:e.target.value})}),o.quill=e.quill,o.formatter=new x.a(o.quill),o.state={inputValue:"",isLinkMenuOpen:!1,menuHasFocus:!1},o}return Object(l.a)(t,e),Object(r.a)(t,[{key:"componentDidUpdate",value:function(e){var t=this.props.lastGoodSelection,o=e.lastGoodSelection;o.index===t.index&&o.length===t.length||this.setState({isLinkMenuOpen:!1})}},{key:"render",value:function(){var e=this.props.activeFormats,t=this.isFormatMenuVisible?p.a.createElement("span",{"aria-live":"assertive",role:"alert",className:"sr-only"},Object(m.m)("Inline Menu Available")):null;return p.a.createElement("div",{ref:this.selfRef},p.a.createElement(y,{selection:this.props.lastGoodSelection,isVisible:this.isFormatMenuVisible},t,p.a.createElement(R,{formatter:this.formatter,onLinkClick:this.toggleLinkMenu,activeFormats:e,lastGoodSelection:this.props.lastGoodSelection,className:"likeDropDownContent"})),p.a.createElement(y,{selection:this.props.lastGoodSelection,isVisible:this.isLinkMenuVisible},p.a.createElement(C,{inputRef:this.linkInput,inputValue:this.state.inputValue,onInputChange:this.onInputChange,onInputKeyDown:this.onInputKeyDown,onCloseClick:this.onCloseClick})))}},{key:"componentDidMount",value:function(){this.quill.root.addEventListener("keydown",this.escFunction,!1),this.focusWatcher=new T.a(this.selfRef.current,this.handleFocusChange),this.focusWatcher.start(),this.quill.getModule("keyboard").addBinding({key:"k",metaKey:!0},{},this.commandKHandler)}},{key:"componentWillUnmount",value:function(){this.quill.root.removeEventListener("keydown",this.escFunction,!1),this.focusWatcher.stop()}},{key:"clearLinkInput",value:function(){this.quill.setSelection(this.props.lastGoodSelection),this.setState({isLinkMenuOpen:!1,inputValue:""})}},{key:"isLinkMenuVisible",get:function(){var e=Object(S.m)(this.quill,w.a,this.props.lastGoodSelection);return this.state.isLinkMenuOpen&&(this.hasFocus||this.ignoreLinkToolbarFocusRequirement)&&this.isOneLineOrLess&&!e&&this.selectionHasContent}},{key:"isFormatMenuVisible",get:function(){var e=this.props.lastGoodSelection.length>0,t=Object(S.m)(this.quill,w.a,this.props.lastGoodSelection);return!this.isLinkMenuVisible&&this.hasFocus&&e&&this.isOneLineOrLess&&!t&&this.selectionHasContent}},{key:"selectionHasContent",get:function(){var e=this.props.lastGoodSelection,t=this.quill.getText(e.index,e.length);return!!t&&"\n"!==t}},{key:"isOneLineOrLess",get:function(){var e=this.props.lastGoodSelection;return this.quill.getLines(e.index||0,e.length||0).length<=1}},{key:"hasFocus",get:function(){return this.state.menuHasFocus||this.quill.hasFocus()}}]),t}(p.a.PureComponent);t.a=Object(v.b)(M)},function(e,t,o){"use strict";var n=o(3),r=o(6),i=o(8),a=o(1),l=o(9),u=o(10),s=o(2),c=o(0),p=o.n(c),f=o(45),h=o(50),d=o.n(h),m=o(618),g=o.n(m),v=o(89),j=o(510),b=o(22),y=o.n(b),_=o(5),O=o(17);
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function C(e){var t=e.isActive,o=e.matchedString,n=e.mentionData,r=e.onClick,i=e.onMouseEnter,a=n.photoUrl,l=n.name,u=n.domID,s=y()("richEditor-menuItem","atMentionList-item",{isActive:t}),c=!1,f=Object(O.m)(l,o).map(function(e,t){return 0!==Intl.Collator("en",{usage:"search",sensitivity:"base",ignorePunctuation:!0,numeric:!0}).compare(e,o)||c?e:(c=!0,p.a.createElement("mark",{className:"atMentionList-mark",key:t},e))});return p.a.createElement("li",{id:u,className:s,role:"option","aria-selected":t,onClick:r,onMouseEnter:i},p.a.createElement("button",{type:"button",className:"atMentionList-suggestion"},p.a.createElement("span",{className:"atMentionList-user"},p.a.createElement("span",{className:"atMentionList-photoWrap"},p.a.createElement("img",{src:a,alt:l,className:"atMentionList-photo"})),p.a.createElement("span",{className:"atMentionList-userName"},f))))}function S(e){var t=e.loadingData,o=e.onMouseEnter,n=e.isActive,r=t.domID,i=y()("richEditor-menuItem","atMentionList-item","atMentionList-loader",{isActive:n});return p.a.createElement("li",{id:r,className:i,role:"option","aria-selected":n,onMouseEnter:o},p.a.createElement("button",{type:"button",className:"atMentionList-suggestion",disabled:!0},p.a.createElement("span",{className:"atMentionList-user atMentionList-loader"},p.a.createElement("span",{className:"PhotoWrap atMentionList-photoWrap"},p.a.createElement("img",{alt:name,className:"atMentionList-photo ProfilePhoto"})),p.a.createElement("span",{className:"atMentionList-userName"},Object(_.m)("Loading...")))))}function w(){var e=y()("richEditor-menuItem","atMentionList-item","atMentionList-spacer");return p.a.createElement("li",{"aria-hidden":"true",className:e,style:{visibility:"hidden"}},p.a.createElement("button",{type:"button",className:"atMentionList-suggestion"},p.a.createElement("span",{className:"atMentionList-user atMentionList-loader"},p.a.createElement("span",{className:"PhotoWrap atMentionList-photoWrap"},p.a.createElement("img",{alt:name,className:"atMentionList-photo ProfilePhoto"})),p.a.createElement("span",{className:"atMentionList-userName"}))))}var x=o(582),k=function(e){function t(e){var o;return Object(n.a)(this,t),o=Object(i.a)(this,Object(a.a)(t).call(this,e)),Object(s.a)(Object(u.a)(Object(u.a)(o)),"state",{flyoutWidth:null,flyoutHeight:null}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"flyoutRef",p.a.createRef()),o}return Object(l.a)(t,e),Object(r.a)(t,[{key:"render",value:function(){var e=this,t=this.props,o=t.activeItemId,n=t.id,r=t.onItemClick,i=t.matchedString,a=t.mentionProps,l=t.showLoader,u=this.props.mentionSelection,s=a.length>0||l,c=y()("richEditor-menu","atMentionList-items","likeDropDownContent"),f=s&&(!!u||this.hasFocusedElement);return p.a.createElement(x.a,{horizontalAlignment:"start",verticalAlignment:"below",flyoutWidth:this.state.flyoutWidth,flyoutHeight:this.state.flyoutHeight,isActive:f,selectionIndex:u?u.index:0,selectionLength:u?u.length:0},function(t){var u=t.x,f=t.y,h={visibility:"hidden",position:"absolute",zIndex:-1};u&&f&&(h={position:"absolute",top:f.position,left:u.position,zIndex:1,visibility:"visible"});var d=a.map(function(e){if(null==e.mentionData)return null;var t=e.mentionData.domID===o;return p.a.createElement(C,{mentionData:e.mentionData,key:e.mentionData.name,onMouseEnter:e.onMouseEnter,onClick:r,isActive:t,matchedString:i})});if(l){var m={domID:e.props.loaderID},g=m.domID===o;d.push(p.a.createElement(S,{loadingData:m,isActive:g,key:"Loading",matchedString:i}))}return p.a.createElement("span",{style:h,className:"atMentionList",ref:e.flyoutRef},p.a.createElement("ul",{id:n,"aria-label":Object(_.m)("@mention user suggestions"),className:c+(s?"":" isHidden"),role:"listbox"},s&&d),p.a.createElement("div",{className:c,style:{visibility:"hidden"}},p.a.createElement(w,null)))})}},{key:"componentDidMount",value:function(){this.setState({flyoutWidth:this.flyoutRef.current?this.flyoutRef.current.offsetWidth:null,flyoutHeight:this.flyoutRef.current?this.flyoutRef.current.offsetHeight:null})}},{key:"hasFocusedElement",get:function(){return!!this.flyoutRef.current&&(document.activeElement===this.flyoutRef.current||this.flyoutRef.current.contains(document.activeElement))}}]),t}(p.a.PureComponent),E=Object(j.b)(k),I=o(538),L=o(65),R=o(49),T=o(15),M=o(69),A=o(63),z=o(25),N=function(e){function t(e){var o;return Object(n.a)(this,t),o=Object(i.a)(this,Object(a.a)(t).call(this,e)),Object(s.a)(Object(u.a)(Object(u.a)(o)),"quill",void 0),Object(s.a)(Object(u.a)(Object(u.a)(o)),"ID",d()("mentionList-")),Object(s.a)(Object(u.a)(Object(u.a)(o)),"loaderID",d()("mentionList-noResults-")),Object(s.a)(Object(u.a)(Object(u.a)(o)),"comboBoxID",d()("mentionComboBox-")),Object(s.a)(Object(u.a)(Object(u.a)(o)),"isConvertingMention",!1),Object(s.a)(Object(u.a)(Object(u.a)(o)),"MENTION_COMPLETION_CHARACTERS",[".","!","?"," ","\n"]),Object(s.a)(Object(u.a)(Object(u.a)(o)),"onItemClick",function(e){e.preventDefault(),o.confirmActiveMention()}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"keyDownListener",function(e){var n=o.props,r=n.suggestions,i=n.activeSuggestionIndex,a=n.activeSuggestionID,l=n.isLoading,u=null!==o.props.mentionSelection;if(r&&r.status===T.LoadStatus.SUCCESS&&r.data)if(o.quill.hasFocus()&&u&&!o.hasApiResponse)v.a.match(e,v.a.keys.ENTER)&&o.cancelActiveMention();else if(o.quill.hasFocus()&&u){var s=i+1,c=i-1,p=Math.min(t.SUGGESTION_LIMIT,r.data.length),f=l?p:p-1,h=a===o.loaderID,d=function(e){return l&&e===f?o.loaderID:r.data[e].domID};switch(!0){case v.a.match(e,v.a.keys.DOWN):var m=i===f?0:s,g=d(m);o.props.suggestionActions.setActive(g,m),e.preventDefault(),e.stopPropagation();break;case v.a.match(e,v.a.keys.UP):var j=0===i?f:c,b=d(j);o.props.suggestionActions.setActive(b,j),e.preventDefault(),e.stopPropagation();break;case v.a.match(e,v.a.keys.ENTER):r.data.length>0&&!h?(o.confirmActiveMention(),e.preventDefault(),e.stopPropagation()):o.cancelActiveMention();break;case v.a.match(e,v.a.keys.TAB):h||(o.confirmActiveMention(),e.preventDefault(),e.stopPropagation());break;case v.a.match(e,v.a.keys.ESCAPE):o.cancelActiveMention(),e.preventDefault(),e.stopPropagation()}}}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"injectComboBoxAccessibility",function(){var e=o.state.autoCompleteBlot,t=o.props,n=t.activeSuggestionID;t.activeSuggestionIndex;e&&e.injectAccessibilityAttributes({ID:o.comboBoxID,activeItemID:n,suggestionListID:o.ID,activeItemIsLoader:n===o.loaderID})}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"onDocumentClick",function(e){o.quill.root.contains(e.target)||o.cancelActiveMention()}),Object(s.a)(Object(u.a)(Object(u.a)(o)),"onTextChange",function(e,t,n){if(n===f.a.sources.USER){var r=e.ops&&e.ops.length>0?e.ops[e.ops.length-1]:null;if(r&&r.insert&&o.MENTION_COMPLETION_CHARACTERS.includes(r.insert)){var i=o.props.suggestions,a=i&&i.status===T.LoadStatus.SUCCESS&&i.data?i.data:[];if(1===a.length&&o.props.lastSuccessfulUsername===a[0].name)return void window.requestAnimationFrame(function(){o.confirmActiveMention(r.insert)})}}}),o.quill=e.quill,o.state={autoCompleteBlot:null},o}return Object(l.a)(t,e),Object(r.a)(t,[{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.keyDownListener,!0),document.addEventListener("click",this.onDocumentClick,!1),this.quill.on("text-change",this.onTextChange)}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.keyDownListener,!0),document.removeEventListener("click",this.onDocumentClick,!1),this.quill.off("text-change",this.onTextChange)}},{key:"componentDidUpdate",value:function(e){var t=this.props.mentionSelection,o=e.mentionSelection;if(!g()(t,o)&&t){var n=this.quill.getText(t.index,t.length).replace("@","");this.props.suggestionActions.loadUsers(n)}var r=this.props.suggestions;if(r){var i=r&&r.status===T.LoadStatus.LOADING,a=r&&r.status===T.LoadStatus.SUCCESS&&r.data&&r.data.length>0;if(t&&(i||a)){if(!this.state.autoCompleteBlot){var l=this.createAutoCompleteBlot();this.setState({autoCompleteBlot:l})}return void this.injectComboBoxAccessibility()}}if(this.state.autoCompleteBlot){var u=this.quill.getSelection();this.cancelActiveMention(),this.quill.hasFocus()&&this.quill.setSelection(u)}}},{key:"render",value:function(){var e=this.props,t=e.suggestions,o=e.lastSuccessfulUsername,n=e.activeSuggestionID,r=e.isLoading,i=t&&t.status===T.LoadStatus.SUCCESS&&t.data?t.data:[];return p.a.createElement(E,{onItemClick:this.onItemClick,mentionProps:this.createMentionProps(i),matchedString:o||"",activeItemId:n,id:this.ID,loaderID:this.loaderID,showLoader:r})}},{key:"createAutoCompleteBlot",value:function(){var e=this.props,t=e.currentSelection,o=e.mentionSelection;return t&&o?(this.quill.formatText(o.index,o.length,"mention-autocomplete",!0,f.a.sources.API),this.quill.setSelection(t.index,0,f.a.sources.API),Object(L.d)(this.quill,t.index-1,I.a)):null}},{key:"createMentionProps",value:function(e){var o=this;return e.slice(0,t.SUGGESTION_LIMIT).map(function(e,t){return{mentionData:e,onMouseEnter:function(){o.props.suggestionActions.setActive(e.domID,t)}}})}},{key:"cancelActiveMention",value:function(){this.state.autoCompleteBlot&&!this.isConvertingMention&&(this.isConvertingMention=!0,this.state.autoCompleteBlot.cancel()),this.isConvertingMention=!1,this.setState({autoCompleteBlot:null})}},{key:"confirmActiveMention",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",t=this.state.autoCompleteBlot,o=this.props,n=o.suggestions,r=o.activeSuggestionIndex;if(t instanceof I.a&&!this.isConvertingMention&&n&&n.status===T.LoadStatus.SUCCESS&&n.data){this.isConvertingMention=!0;var i=n.data[r],a=t.offset(this.quill.scroll);t.finalize(i),this.quill.insertText(a+1,e,f.a.sources.SILENT),this.quill.setSelection(a+2,0,f.a.sources.SILENT),this.cancelActiveMention()}}},{key:"hasApiResponse",get:function(){var e=this.props.suggestions;return e&&e.status===T.LoadStatus.SUCCESS}}]),t}(p.a.Component);
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */Object(s.a)(N,"SUGGESTION_LIMIT",5);var P=Object(R.b)(M.a.mapStateToProps,function(e){return{suggestionActions:new A.a(e,z.a)}});t.a=P(Object(j.b)(N))},function(e,t,o){"use strict";o.r(t);var n=o(0),r=o.n(n),i=o(44),a=o.n(i),l=o(24),u=o(617),s=o(54);function c(){return null!==function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];e=e.map(function(e){return e.toLowerCase()});for(var t=s.a.permissions.bans||{},o=Object.keys(t),n=0;n<o.length;n++){var r=o[n],i=t[r];if(!(r in e)){if(i.except){var a="string"==typeof i.except?[i.except]:i.except,l=!1,u=!0,c=!1,f=void 0;try{for(var h,d=a[Symbol.iterator]();!(u=(h=d.next()).done);u=!0){var m=h.value;if(p(m)){l=!0;break}}}catch(e){c=!0,f=e}finally{try{u||null==d.return||d.return()}finally{if(c)throw f}}if(l)continue}return i.type=r,i}}return null}(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[])}function p(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=s.a.permissions.permissions||{};return"admin"===e?!!s.a.permissions.isAdmin:"!"===e.substr(0,1)?null:!0===o[e]||!(null===t||!o[e].indexOf||-1===o[e].indexOf(t))}
/**
 * <AUTHOR> (charrondev) Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function f(e){var t=Object(l.c)(e),o=t.closest("form").querySelector(".BodyBox");if(!o)throw new Error("Could not find the BodyBox to mount editor to.");var n=o.getAttribute("format")||"Rich";if("Rich"!==n)throw new Error("Unsupported initial editor format ".concat(n));a.a.render(r.a.createElement(u.a,{legacyTextArea:o,isPrimaryEditor:!0,legacyMode:!0,allowUpload:
/**
 * Permissions utility functions ported over from the server.
 *
 * @see {\Vanilla\Permsissions} for the server-side implementation.
 * @see {Gdn_Controller->renderMaster()} for the injection of permissions into the client.
 *
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof e&&(e=[e]),c(e))return!1;if(s.a.permissions.isAdmin)return!0;var o=!0,n=!1,r=void 0;try{for(var i,a=e[Symbol.iterator]();!(o=(i=a.next()).done);o=!0)if(!1===p(i.value,t))return!1}catch(e){n=!0,r=e}finally{try{o||null==a.return||a.return()}finally{if(n)throw r}}return!0}("uploads.add")}),t),t.classList.remove("isDisabled")}o.d(t,"default",function(){return f})}])]);
//# sourceMappingURL=async~mountEditor.min.js.map?67b75bea86986dd35cd9