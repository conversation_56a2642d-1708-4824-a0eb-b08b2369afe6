@charset "UTF-8";
@import url("https://fonts.googleapis.com/css?family=Open+Sans:400,400i,600,600i,700,700i");
.fade {
  opacity: 0;
  -webkit-transition: opacity .15s linear;
          transition: opacity .15s linear;
}

.fade.in {
  opacity: 1;
}

.collapse {
  display: none;
}

.collapse.in {
  display: block;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-timing-function: ease;
          transition-timing-function: ease;
  -webkit-transition-duration: .35s;
          transition-duration: .35s;
  -webkit-transition-property: height;
          transition-property: height;
}

.icon-vanillicon:before, .icheckbox.checked, .checkbox-painted-wrapper input:checked + label {
  display: inline-block;
  min-width: 1em;
  font-family: "vanillicon";
  font-variant: normal;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  text-align: center;
  text-decoration: inherit;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
}

@font-face {
  font-family: "vanillicon";
  src: url("/resources/fonts/vanillicon/vanillicon.eot");
  src: url("/resources/fonts/vanillicon/vanillicon.eot?#iefix") format("embedded-opentype"), url("/resources/fonts/vanillicon/vanillicon.woff") format("woff"), url("/resources/fonts/vanillicon/vanillicon.ttf") format("truetype"), url("/resources/fonts/vanillicon/vanillicon.svg?#vanillicon") format("svg");
  font-weight: normal;
  font-style: normal;
}

.icon-vanillicon-adjust:before {
  content: "\f100";
}

.icon-vanillicon-agree:before {
  content: "\f101";
}

.icon-vanillicon-align-center:before {
  content: "\f102";
}

.icon-vanillicon-align-justify:before {
  content: "\f103";
}

.icon-vanillicon-align-left:before {
  content: "\f104";
}

.icon-vanillicon-align-right:before {
  content: "\f105";
}

.icon-vanillicon-android:before {
  content: "\f1bf";
}

.icon-vanillicon-angry:before {
  content: "\f106";
}

.icon-vanillicon-apple:before {
  content: "\f1c0";
}

.icon-vanillicon-archive:before {
  content: "\f107";
}

.icon-vanillicon-arrow-down:before {
  content: "\f108";
}

.icon-vanillicon-arrow-left:before {
  content: "\f109";
}

.icon-vanillicon-arrow-right:before {
  content: "\f10a";
}

.icon-vanillicon-arrow-up:before {
  content: "\f10b";
}

.icon-vanillicon-ban:before {
  content: "\f10c";
}

.icon-vanillicon-bar-chart:before {
  content: "\f10d";
}

.icon-vanillicon-bell:before {
  content: "\f10e";
}

.icon-vanillicon-bold:before {
  content: "\f10f";
}

.icon-vanillicon-bookmark:before {
  content: "\f110";
}

.icon-vanillicon-bookmark-empty:before {
  content: "\f111";
}

.icon-vanillicon-bug:before {
  content: "\f112";
}

.icon-vanillicon-bullhorn:before {
  content: "\f113";
}

.icon-vanillicon-bullseye:before {
  content: "\f114";
}

.icon-vanillicon-calendar:before {
  content: "\f115";
}

.icon-vanillicon-camera:before {
  content: "\f116";
}

.icon-vanillicon-caret-down:before {
  content: "\f117";
}

.icon-vanillicon-caret-left:before {
  content: "\f118";
}

.icon-vanillicon-caret-right:before {
  content: "\f119";
}

.icon-vanillicon-caret-up:before {
  content: "\f11a";
}

.icon-vanillicon-certificate:before {
  content: "\f11b";
}

.icon-vanillicon-check:before {
  content: "\f11c";
}

.icon-vanillicon-check-empty:before {
  content: "\f11d";
}

.icon-vanillicon-check-sign:before {
  content: "\f11e";
}

.icon-vanillicon-chevron-down:before {
  content: "\f11f";
}

.icon-vanillicon-chevron-left:before {
  content: "\f120";
}

.icon-vanillicon-chevron-right:before {
  content: "\f121";
}

.icon-vanillicon-chevron-sign-down:before {
  content: "\f122";
}

.icon-vanillicon-chevron-sign-left:before {
  content: "\f123";
}

.icon-vanillicon-chevron-sign-right:before {
  content: "\f124";
}

.icon-vanillicon-chevron-sign-up:before {
  content: "\f125";
}

.icon-vanillicon-chevron-up:before {
  content: "\f126";
}

.icon-vanillicon-circle:before {
  content: "\f127";
}

.icon-vanillicon-circle-blank:before {
  content: "\f128";
}

.icon-vanillicon-cloud:before {
  content: "\f129";
}

.icon-vanillicon-cloud-download:before {
  content: "\f12a";
}

.icon-vanillicon-cloud-upload:before {
  content: "\f12b";
}

.icon-vanillicon-code:before {
  content: "\f12c";
}

.icon-vanillicon-cog:before {
  content: "\f12d";
}

.icon-vanillicon-cogs:before {
  content: "\f12e";
}

.icon-vanillicon-collapse:before {
  content: "\f12f";
}

.icon-vanillicon-collapse-top:before {
  content: "\f130";
}

.icon-vanillicon-comment:before {
  content: "\f131";
}

.icon-vanillicon-comments:before {
  content: "\f132";
}

.icon-vanillicon-compass:before {
  content: "\f133";
}

.icon-vanillicon-compose:before {
  content: "\f134";
}

.icon-vanillicon-crown:before {
  content: "\f135";
}

.icon-vanillicon-dashboard:before {
  content: "\f136";
}

.icon-vanillicon-discussion:before {
  content: "\f137";
}

.icon-vanillicon-dot-circle:before {
  content: "\f138";
}

.icon-vanillicon-dropbox:before {
  content: "\f139";
}

.icon-vanillicon-edit:before {
  content: "\f13a";
}

.icon-vanillicon-edit-sign:before {
  content: "\f13b";
}

.icon-vanillicon-ellipsis:before {
  content: "\f13c";
}

.icon-vanillicon-exclamation-sign:before {
  content: "\f13d";
}

.icon-vanillicon-expand:before {
  content: "\f13e";
}

.icon-vanillicon-external-link:before {
  content: "\f13f";
}

.icon-vanillicon-eye-close:before {
  content: "\f140";
}

.icon-vanillicon-eye-open:before {
  content: "\f141";
}

.icon-vanillicon-facebook:before {
  content: "\f142";
}

.icon-vanillicon-facebook-alt:before {
  content: "\f143";
}

.icon-vanillicon-facebook-alt-round:before {
  content: "\f1c1";
}

.icon-vanillicon-file:before {
  content: "\f144";
}

.icon-vanillicon-file-text:before {
  content: "\f145";
}

.icon-vanillicon-filter:before {
  content: "\f146";
}

.icon-vanillicon-flag:before {
  content: "\f147";
}

.icon-vanillicon-flame:before {
  content: "\f148";
}

.icon-vanillicon-flickr:before {
  content: "\f1c2";
}

.icon-vanillicon-folder-close:before {
  content: "\f149";
}

.icon-vanillicon-folder-open:before {
  content: "\f14a";
}

.icon-vanillicon-font:before {
  content: "\f14b";
}

.icon-vanillicon-frown:before {
  content: "\f14c";
}

.icon-vanillicon-gift:before {
  content: "\f14d";
}

.icon-vanillicon-github:before {
  content: "\f14e";
}

.icon-vanillicon-github-alt:before {
  content: "\f1c3";
}

.icon-vanillicon-github-octocat:before {
  content: "\f1c4";
}

.icon-vanillicon-globe:before {
  content: "\f14f";
}

.icon-vanillicon-google:before {
  content: "\f1c5";
}

.icon-vanillicon-google-plus:before {
  content: "\f150";
}

.icon-vanillicon-google-plus-alt:before {
  content: "\f151";
}

.icon-vanillicon-group:before {
  content: "\f152";
}

.icon-vanillicon-hand-down:before {
  content: "\f153";
}

.icon-vanillicon-hand-left:before {
  content: "\f154";
}

.icon-vanillicon-hand-right:before {
  content: "\f155";
}

.icon-vanillicon-hand-up:before {
  content: "\f156";
}

.icon-vanillicon-heart:before {
  content: "\f157";
}

.icon-vanillicon-home:before {
  content: "\f158";
}

.icon-vanillicon-inbox:before {
  content: "\f159";
}

.icon-vanillicon-indent-left:before {
  content: "\f15a";
}

.icon-vanillicon-indent-right:before {
  content: "\f15b";
}

.icon-vanillicon-info-sign:before {
  content: "\f15c";
}

.icon-vanillicon-instagram:before {
  content: "\f1be";
}

.icon-vanillicon-italic:before {
  content: "\f1ba";
}

.icon-vanillicon-jail:before {
  content: "\f15e";
}

.icon-vanillicon-key:before {
  content: "\f15f";
}

.icon-vanillicon-lightbulb:before {
  content: "\f160";
}

.icon-vanillicon-link:before {
  content: "\f161";
}

.icon-vanillicon-linkedin:before {
  content: "\f162";
}

.icon-vanillicon-linkedin-alt:before {
  content: "\f163";
}

.icon-vanillicon-linux:before {
  content: "\f1c6";
}

.icon-vanillicon-list-ol:before {
  content: "\f164";
}

.icon-vanillicon-list-ul:before {
  content: "\f165";
}

.icon-vanillicon-lock:before {
  content: "\f166";
}

.icon-vanillicon-lol:before {
  content: "\f167";
}

.icon-vanillicon-mail:before {
  content: "\f168";
}

.icon-vanillicon-mail-open:before {
  content: "\f169";
}

.icon-vanillicon-map-marker:before {
  content: "\f16a";
}

.icon-vanillicon-medium:before {
  content: "\f1c7";
}

.icon-vanillicon-meh:before {
  content: "\f16b";
}

.icon-vanillicon-menu:before {
  content: "\f16c";
}

.icon-vanillicon-minus-sign:before {
  content: "\f16d";
}

.icon-vanillicon-mobile-phone:before {
  content: "\f16e";
}

.icon-vanillicon-mod:before {
  content: "\f16f";
}

.icon-vanillicon-nib:before {
  content: "\f1bb";
}

.icon-vanillicon-note:before {
  content: "\f170";
}

.icon-vanillicon-ok:before {
  content: "\f171";
}

.icon-vanillicon-ok-circle:before {
  content: "\f172";
}

.icon-vanillicon-ok-sign:before {
  content: "\f173";
}

.icon-vanillicon-paper-clip:before {
  content: "\f174";
}

.icon-vanillicon-paragraph:before {
  content: "\f175";
}

.icon-vanillicon-paypal:before {
  content: "\f1c8";
}

.icon-vanillicon-pencil:before {
  content: "\f176";
}

.icon-vanillicon-permalink:before {
  content: "\f177";
}

.icon-vanillicon-picture:before {
  content: "\f178";
}

.icon-vanillicon-pinterest:before {
  content: "\f179";
}

.icon-vanillicon-pinterest-alt:before {
  content: "\f17a";
}

.icon-vanillicon-play-circle:before {
  content: "\f17b";
}

.icon-vanillicon-plus-sign:before {
  content: "\f17c";
}

.icon-vanillicon-poll:before {
  content: "\f17d";
}

.icon-vanillicon-preview:before {
  content: "\f17e";
}

.icon-vanillicon-pushpin:before {
  content: "\f17f";
}

.icon-vanillicon-question:before {
  content: "\f180";
}

.icon-vanillicon-question-sign:before {
  content: "\f181";
}

.icon-vanillicon-quote:before {
  content: "\f182";
}

.icon-vanillicon-reddit:before {
  content: "\f1c9";
}

.icon-vanillicon-reddit-alien:before {
  content: "\f1ca";
}

.icon-vanillicon-reddit-alt:before {
  content: "\f1cb";
}

.icon-vanillicon-refresh:before {
  content: "\f183";
}

.icon-vanillicon-remove:before {
  content: "\f184";
}

.icon-vanillicon-remove-sign:before {
  content: "\f185";
}

.icon-vanillicon-repeat:before {
  content: "\f186";
}

.icon-vanillicon-reply:before {
  content: "\f187";
}

.icon-vanillicon-reply-all:before {
  content: "\f188";
}

.icon-vanillicon-report:before {
  content: "\f189";
}

.icon-vanillicon-resize-full:before {
  content: "\f18a";
}

.icon-vanillicon-resize-small:before {
  content: "\f18b";
}

.icon-vanillicon-retweet:before {
  content: "\f18c";
}

.icon-vanillicon-robot:before {
  content: "\f1bc";
}

.icon-vanillicon-rss:before {
  content: "\f18d";
}

.icon-vanillicon-salesforce:before {
  content: "\f1bd";
}

.icon-vanillicon-search:before {
  content: "\f18e";
}

.icon-vanillicon-select:before {
  content: "\f18f";
}

.icon-vanillicon-share:before {
  content: "\f190";
}

.icon-vanillicon-share-alt:before {
  content: "\f1cc";
}

.icon-vanillicon-share-alt-square:before {
  content: "\f1cd";
}

.icon-vanillicon-shield:before {
  content: "\f191";
}

.icon-vanillicon-sign-blank:before {
  content: "\f192";
}

.icon-vanillicon-signin:before {
  content: "\f193";
}

.icon-vanillicon-signout:before {
  content: "\f194";
}

.icon-vanillicon-skull:before {
  content: "\f195";
}

.icon-vanillicon-skype:before {
  content: "\f1ce";
}

.icon-vanillicon-smile:before {
  content: "\f196";
}

.icon-vanillicon-source:before {
  content: "\f197";
}

.icon-vanillicon-spam:before {
  content: "\f198";
}

.icon-vanillicon-star:before {
  content: "\f199";
}

.icon-vanillicon-star-empty:before {
  content: "\f19a";
}

.icon-vanillicon-star-half-empty:before {
  content: "\f19b";
}

.icon-vanillicon-steam:before {
  content: "\f19c";
}

.icon-vanillicon-steam-alt:before {
  content: "\f1cf";
}

.icon-vanillicon-strikethrough:before {
  content: "\f19d";
}

.icon-vanillicon-subscript:before {
  content: "\f19e";
}

.icon-vanillicon-superscript:before {
  content: "\f19f";
}

.icon-vanillicon-table:before {
  content: "\f1a0";
}

.icon-vanillicon-tag:before {
  content: "\f1a1";
}

.icon-vanillicon-tags:before {
  content: "\f1a2";
}

.icon-vanillicon-th:before {
  content: "\f1a3";
}

.icon-vanillicon-th-large:before {
  content: "\f1a4";
}

.icon-vanillicon-th-list:before {
  content: "\f1a5";
}

.icon-vanillicon-thumbs-down:before {
  content: "\f1a6";
}

.icon-vanillicon-thumbs-up:before {
  content: "\f1a7";
}

.icon-vanillicon-ticket:before {
  content: "\f1a8";
}

.icon-vanillicon-time:before {
  content: "\f1a9";
}

.icon-vanillicon-trash:before {
  content: "\f1aa";
}

.icon-vanillicon-troll:before {
  content: "\f1ab";
}

.icon-vanillicon-trophy:before {
  content: "\f1ac";
}

.icon-vanillicon-tumblr:before {
  content: "\f1d0";
}

.icon-vanillicon-tumblr-alt:before {
  content: "\f1d1";
}

.icon-vanillicon-twitter:before {
  content: "\f1ad";
}

.icon-vanillicon-twitter-alt:before {
  content: "\f1ae";
}

.icon-vanillicon-underline:before {
  content: "\f1af";
}

.icon-vanillicon-undo:before {
  content: "\f1b0";
}

.icon-vanillicon-unlink:before {
  content: "\f1b1";
}

.icon-vanillicon-unlock:before {
  content: "\f1b2";
}

.icon-vanillicon-user:before {
  content: "\f1b3";
}

.icon-vanillicon-vcard:before {
  content: "\f1b4";
}

.icon-vanillicon-video:before {
  content: "\f1b5";
}

.icon-vanillicon-vimeo:before {
  content: "\f1d2";
}

.icon-vanillicon-vimeo-alt:before {
  content: "\f1d3";
}

.icon-vanillicon-vine:before {
  content: "\f1d4";
}

.icon-vanillicon-warn:before {
  content: "\f1b6";
}

.icon-vanillicon-warning-sign:before {
  content: "\f1b7";
}

.icon-vanillicon-windows:before {
  content: "\f1d5";
}

.icon-vanillicon-wordpress:before {
  content: "\f1d6";
}

.icon-vanillicon-wtf:before {
  content: "\f1b8";
}

.icon-vanillicon-yahoo:before {
  content: "\f1d7";
}

.icon-vanillicon-yelp:before {
  content: "\f1d8";
}

.icon-vanillicon-youtube:before {
  content: "\f1d9";
}

.icon-vanillicon-youtube-alt:before {
  content: "\f1da";
}

.icon-vanillicon-zendesk:before {
  content: "\f1b9";
}

.icon, .btn-icon, .bookmark, .search-wrap .search-icon-wrap,
.icon svg, .btn-icon svg, .bookmark svg, .search-wrap .search-icon-wrap svg {
  vertical-align: middle;
  display: inline-block;
  fill: currentColor;
}

.icon-9,
.icon-9 svg {
  height: 0.5625rem;
  width: 0.5625rem;
}

.icon-10,
.icon-10 svg {
  height: 0.625rem;
  width: 0.625rem;
}

.icon-11,
.icon-11 svg {
  height: 0.6875rem;
  width: 0.6875rem;
}

.icon-12,
.icon-12 svg {
  height: 0.75rem;
  width: 0.75rem;
}

.icon-16,
.icon-16 svg {
  height: 1rem;
  width: 1rem;
}

.icon-24,
.icon-24 svg {
  height: 1.5rem;
  width: 1.5rem;
}

.icon-30,
.icon-30 svg {
  height: 1.875rem;
  width: 1.875rem;
}

.icon-48,
.icon-48 svg {
  height: 3rem;
  width: 3rem;
}

.icon-text {
  width: 0.875rem;
  height: 0.875rem;
  color: #555a62;
  vertical-align: middle;
}

/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

[hidden],
template {
  display: none;
}

a {
  background-color: transparent;
}

a:active {
  outline: 0;
}

a:hover {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b,
strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

mark {
  background: #ff0;
  color: #000;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

img {
  border: 0;
}

svg:not(:root) {
  overflow: hidden;
}

figure {
  margin: 1em 40px;
}

hr {
  box-sizing: content-box;
  height: 0;
}

pre {
  overflow: auto;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled],
html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

input[type="search"] {
  -webkit-appearance: textfield;
  box-sizing: content-box;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.pull-xs-left {
  float: left !important;
}

.pull-xs-right {
  float: right !important;
}

.pull-xs-none {
  float: none !important;
}

@media (min-width: 544px) {
  .pull-sm-left {
    float: left !important;
  }
  .pull-sm-right {
    float: right !important;
  }
  .pull-sm-none {
    float: none !important;
  }
}

@media (min-width: 768px) {
  .pull-md-left {
    float: left !important;
  }
  .pull-md-right {
    float: right !important;
  }
  .pull-md-none {
    float: none !important;
  }
}

@media (min-width: 992px) {
  .pull-lg-left {
    float: left !important;
  }
  .pull-lg-right {
    float: right !important;
  }
  .pull-lg-none {
    float: none !important;
  }
}

@media (min-width: 1200px) {
  .pull-xl-left {
    float: left !important;
  }
  .pull-xl-right {
    float: right !important;
  }
  .pull-xl-none {
    float: none !important;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

.invisible {
  visibility: hidden !important;
}

.text-hide {
  font: "0/0" a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.text-justify {
  text-align: justify !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-xs-left {
  text-align: left !important;
}

.text-xs-right {
  text-align: right !important;
}

.text-xs-center {
  text-align: center !important;
}

@media (min-width: 544px) {
  .text-sm-left {
    text-align: left !important;
  }
  .text-sm-right {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important;
  }
  .text-md-right {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}

@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important;
  }
  .text-lg-right {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important;
  }
  .text-xl-right {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-normal {
  font-weight: normal;
}

.font-weight-bold {
  font-weight: bold;
}

.font-italic {
  font-style: italic;
}

.text-muted {
  color: #f6f9fb;
}

.text-primary {
  color: #0291db !important;
}

a.text-primary:focus, a.text-primary:hover {
  color: #0270a8;
}

.text-success {
  color: #6bc573 !important;
}

a.text-success:focus, a.text-success:hover {
  color: #47b651;
}

.text-info {
  color: #a1b8c6 !important;
}

a.text-info:focus, a.text-info:hover {
  color: #81a0b3;
}

.text-warning {
  color: #faae42 !important;
}

a.text-warning:focus, a.text-warning:hover {
  color: #f99910;
}

.text-danger {
  color: #fa2e1f !important;
}

a.text-danger:focus, a.text-danger:hover {
  color: #e11405;
}

.nav {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: inline-block;
}

.nav-link:focus, .nav-link:hover {
  text-decoration: none;
}

.nav-link.disabled {
  color: #f6f9fb;
}

.nav-link.disabled, .nav-link.disabled:focus, .nav-link.disabled:hover {
  color: #f6f9fb;
  cursor: not-allowed;
  background-color: transparent;
}

.nav-inline .nav-item {
  display: inline-block;
}

.nav-inline .nav-item + .nav-item,
.nav-inline .nav-link + .nav-link {
  margin-left: 1rem;
}

.nav-tabs {
  border-bottom: 1px solid #ddd;
}

.nav-tabs::after {
  content: "";
  display: table;
  clear: both;
}

.nav-tabs .nav-item {
  float: left;
  margin-bottom: -1px;
}

.nav-tabs .nav-item + .nav-item {
  margin-left: .2rem;
}

.nav-tabs .nav-link {
  display: block;
  padding: 5px 1em;
  border: 0.0625rem solid transparent;
  border-radius: 0.125rem 0.125rem 0 0;
}

.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
  border-color: #f4f6fb #f4f6fb #ddd;
}

.nav-tabs .nav-link.disabled, .nav-tabs .nav-link.disabled:focus, .nav-tabs .nav-link.disabled:hover {
  color: #f6f9fb;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .nav-link.active, .nav-tabs .file-upload input:focus ~ .nav-link.file-upload-choose, .file-upload .nav-tabs input:focus ~ .nav-link.file-upload-choose, .nav-tabs
.file-upload input:focus ~ .nav-link.file-upload-browse,
.file-upload .nav-tabs input:focus ~ .nav-link.file-upload-browse, .nav-tabs
.file-upload input:hover ~ .nav-link.file-upload-choose,
.file-upload .nav-tabs input:hover ~ .nav-link.file-upload-choose, .nav-tabs
.file-upload input:hover ~ .nav-link.file-upload-browse,
.file-upload .nav-tabs input:hover ~ .nav-link.file-upload-browse, .nav-tabs select.nav-link:focus, .nav-tabs .nav-link.drop-enabled, .nav-tabs .nav-link.active:focus, .nav-tabs .file-upload input:focus ~ .nav-link.file-upload-choose:focus, .file-upload .nav-tabs input:focus ~ .nav-link.file-upload-choose:focus, .nav-tabs
.file-upload input:focus ~ .nav-link.file-upload-browse:focus,
.file-upload .nav-tabs input:focus ~ .nav-link.file-upload-browse:focus, .nav-tabs
.file-upload input:hover ~ .nav-link.file-upload-choose:focus,
.file-upload .nav-tabs input:hover ~ .nav-link.file-upload-choose:focus, .nav-tabs
.file-upload input:hover ~ .nav-link.file-upload-browse:focus,
.file-upload .nav-tabs input:hover ~ .nav-link.file-upload-browse:focus, .nav-tabs select.nav-link:focus, .nav-tabs
select.nav-link.form-control:focus, .nav-tabs select.nav-link.ac_input:focus, .nav-tabs .nav-link.drop-enabled:focus, .nav-tabs .nav-link.active:hover, .nav-tabs .file-upload input:focus ~ .nav-link.file-upload-choose:hover, .file-upload .nav-tabs input:focus ~ .nav-link.file-upload-choose:hover, .nav-tabs
.file-upload input:focus ~ .nav-link.file-upload-browse:hover,
.file-upload .nav-tabs input:focus ~ .nav-link.file-upload-browse:hover, .nav-tabs
.file-upload input:hover ~ .nav-link.file-upload-choose:hover,
.file-upload .nav-tabs input:hover ~ .nav-link.file-upload-choose:hover, .nav-tabs
.file-upload input:hover ~ .nav-link.file-upload-browse:hover,
.file-upload .nav-tabs input:hover ~ .nav-link.file-upload-browse:hover, .nav-tabs select.nav-link:hover:focus, .nav-tabs .nav-link.drop-enabled:hover,
.nav-tabs .nav-item.open .nav-link,
.nav-tabs .nav-item.open .nav-link:focus,
.nav-tabs .nav-item.open .nav-link:hover {
  color: #949aa2;
  background-color: #f6f9fb;
  border-color: #ddd #ddd transparent;
}

.nav-pills::after {
  content: "";
  display: table;
  clear: both;
}

.nav-pills .nav-item {
  float: left;
}

.nav-pills .nav-item + .nav-item {
  margin-left: .2rem;
}

.nav-pills .nav-link {
  display: block;
  padding: 5px 1em;
  line-height: 20px;
  border-radius: 0.125rem;
}

.nav-pills .nav-link.active, .nav-pills .file-upload input:focus ~ .nav-link.file-upload-choose, .file-upload .nav-pills input:focus ~ .nav-link.file-upload-choose, .nav-pills
.file-upload input:focus ~ .nav-link.file-upload-browse,
.file-upload .nav-pills input:focus ~ .nav-link.file-upload-browse, .nav-pills
.file-upload input:hover ~ .nav-link.file-upload-choose,
.file-upload .nav-pills input:hover ~ .nav-link.file-upload-choose, .nav-pills
.file-upload input:hover ~ .nav-link.file-upload-browse,
.file-upload .nav-pills input:hover ~ .nav-link.file-upload-browse, .nav-pills select.nav-link:focus, .nav-pills .nav-link.drop-enabled, .nav-pills .nav-link.active:focus, .nav-pills .file-upload input:focus ~ .nav-link.file-upload-choose:focus, .file-upload .nav-pills input:focus ~ .nav-link.file-upload-choose:focus, .nav-pills
.file-upload input:focus ~ .nav-link.file-upload-browse:focus,
.file-upload .nav-pills input:focus ~ .nav-link.file-upload-browse:focus, .nav-pills
.file-upload input:hover ~ .nav-link.file-upload-choose:focus,
.file-upload .nav-pills input:hover ~ .nav-link.file-upload-choose:focus, .nav-pills
.file-upload input:hover ~ .nav-link.file-upload-browse:focus,
.file-upload .nav-pills input:hover ~ .nav-link.file-upload-browse:focus, .nav-pills select.nav-link:focus, .nav-pills
select.nav-link.form-control:focus, .nav-pills select.nav-link.ac_input:focus, .nav-pills .nav-link.drop-enabled:focus, .nav-pills .nav-link.active:hover, .nav-pills .file-upload input:focus ~ .nav-link.file-upload-choose:hover, .file-upload .nav-pills input:focus ~ .nav-link.file-upload-choose:hover, .nav-pills
.file-upload input:focus ~ .nav-link.file-upload-browse:hover,
.file-upload .nav-pills input:focus ~ .nav-link.file-upload-browse:hover, .nav-pills
.file-upload input:hover ~ .nav-link.file-upload-choose:hover,
.file-upload .nav-pills input:hover ~ .nav-link.file-upload-choose:hover, .nav-pills
.file-upload input:hover ~ .nav-link.file-upload-browse:hover,
.file-upload .nav-pills input:hover ~ .nav-link.file-upload-browse:hover, .nav-pills select.nav-link:hover:focus, .nav-pills .nav-link.drop-enabled:hover,
.nav-pills .nav-item.open .nav-link,
.nav-pills .nav-item.open .nav-link:focus,
.nav-pills .nav-item.open .nav-link:hover {
  color: #fff;
  cursor: default;
  background-color: #0291db;
}

.nav-stacked .nav-item {
  display: block;
  float: none;
}

.nav-stacked .nav-item + .nav-item {
  margin-top: .2rem;
  margin-left: 0;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active, .file-upload .tab-content > input:focus ~ .file-upload-choose,
.file-upload .tab-content > input:focus ~ .file-upload-browse,
.file-upload .tab-content > input:hover ~ .file-upload-choose,
.file-upload .tab-content > input:hover ~ .file-upload-browse, .tab-content > select:focus, .tab-content > .drop-enabled {
  display: block;
}

.nav-tabs .dropdown-menu, .nav-tabs .ac_results {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 0.75rem;
  opacity: 0;
}

.tooltip.in {
  opacity: 0.9;
}

.tooltip.tooltip-top, .tooltip.bs-tether-element-attached-bottom {
  padding: 5px 0;
  margin-top: -3px;
}

.tooltip.tooltip-top .tooltip-arrow, .tooltip.bs-tether-element-attached-bottom .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}

.tooltip.tooltip-right, .tooltip.bs-tether-element-attached-left {
  padding: 0 5px;
  margin-left: 3px;
}

.tooltip.tooltip-right .tooltip-arrow, .tooltip.bs-tether-element-attached-left .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000;
}

.tooltip.tooltip-bottom, .tooltip.bs-tether-element-attached-top {
  padding: 5px 0;
  margin-top: 3px;
}

.tooltip.tooltip-bottom .tooltip-arrow, .tooltip.bs-tether-element-attached-top .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}

.tooltip.tooltip-left, .tooltip.bs-tether-element-attached-right {
  padding: 0 5px;
  margin-left: -3px;
}

.tooltip.tooltip-left .tooltip-arrow, .tooltip.bs-tether-element-attached-right .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000;
}

.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.125rem;
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.dropup,
.dropdown {
  position: relative;
}

.dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: .25rem;
  margin-left: .25rem;
  vertical-align: middle;
  content: "";
  border-top: 0.375em solid;
  border-right: 0.375em solid transparent;
  border-left: 0.375em solid transparent;
}

.dropdown-toggle:focus {
  outline: 0;
}

.dropup .dropdown-toggle::after {
  border-top: 0;
  border-bottom: 0.375em solid;
}

.dropdown-menu, .ac_results {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 0.875rem;
  color: #555a62;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 0.0625rem solid rgba(0, 0, 0, 0.15);
  border-radius: 0.125rem;
}

.dropdown-divider {
  height: 1px;
  margin: 0.375rem 0;
  overflow: hidden;
  background-color: #e7e8e9;
}

.dropdown-item, .ac_results li {
  display: block;
  width: 100%;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.5;
  color: #555a62;
  text-align: inherit;
  white-space: nowrap;
  background: none;
  border: 0;
}

.dropdown-item:focus, .ac_results li:focus, .dropdown-item:hover, .ac_results li:hover {
  color: #494d54;
  text-decoration: none;
  background-color: #f6f9fb;
}

.dropdown-item.active, .file-upload input:focus ~ .dropdown-item.file-upload-choose, .file-upload .ac_results input:focus ~ li.file-upload-choose, .ac_results .file-upload input:focus ~ li.file-upload-choose,
.file-upload input:focus ~ .dropdown-item.file-upload-browse,
.file-upload .ac_results input:focus ~ li.file-upload-browse, .ac_results
.file-upload input:focus ~ li.file-upload-browse,
.file-upload input:hover ~ .dropdown-item.file-upload-choose,
.file-upload .ac_results input:hover ~ li.file-upload-choose, .ac_results
.file-upload input:hover ~ li.file-upload-choose,
.file-upload input:hover ~ .dropdown-item.file-upload-browse,
.file-upload .ac_results input:hover ~ li.file-upload-browse, .ac_results
.file-upload input:hover ~ li.file-upload-browse, select.dropdown-item:focus, .dropdown-item.drop-enabled, .ac_results li.drop-enabled, .ac_results li.active, .dropdown-item.active:focus, .file-upload input:focus ~ .dropdown-item.file-upload-choose:focus, .file-upload .ac_results input:focus ~ li.file-upload-choose:focus, .ac_results .file-upload input:focus ~ li.file-upload-choose:focus,
.file-upload input:focus ~ .dropdown-item.file-upload-browse:focus,
.file-upload .ac_results input:focus ~ li.file-upload-browse:focus, .ac_results
.file-upload input:focus ~ li.file-upload-browse:focus,
.file-upload input:hover ~ .dropdown-item.file-upload-choose:focus,
.file-upload .ac_results input:hover ~ li.file-upload-choose:focus, .ac_results
.file-upload input:hover ~ li.file-upload-choose:focus,
.file-upload input:hover ~ .dropdown-item.file-upload-browse:focus,
.file-upload .ac_results input:hover ~ li.file-upload-browse:focus, .ac_results
.file-upload input:hover ~ li.file-upload-browse:focus, select.dropdown-item:focus, .dropdown-item.drop-enabled:focus, .ac_results li.drop-enabled:focus, .ac_results li.active:focus, .dropdown-item.active:hover, .file-upload input:focus ~ .dropdown-item.file-upload-choose:hover, .file-upload .ac_results input:focus ~ li.file-upload-choose:hover, .ac_results .file-upload input:focus ~ li.file-upload-choose:hover,
.file-upload input:focus ~ .dropdown-item.file-upload-browse:hover,
.file-upload .ac_results input:focus ~ li.file-upload-browse:hover, .ac_results
.file-upload input:focus ~ li.file-upload-browse:hover,
.file-upload input:hover ~ .dropdown-item.file-upload-choose:hover,
.file-upload .ac_results input:hover ~ li.file-upload-choose:hover, .ac_results
.file-upload input:hover ~ li.file-upload-choose:hover,
.file-upload input:hover ~ .dropdown-item.file-upload-browse:hover,
.file-upload .ac_results input:hover ~ li.file-upload-browse:hover, .ac_results
.file-upload input:hover ~ li.file-upload-browse:hover, select.dropdown-item:hover:focus, .dropdown-item.drop-enabled:hover, .ac_results li.drop-enabled:hover, .ac_results li.active:hover {
  color: #fff;
  text-decoration: none;
  background-color: #0291db;
  outline: 0;
}

.dropdown-item.disabled, .ac_results li.disabled, .dropdown-item.disabled:focus, .ac_results li.disabled:focus, .dropdown-item.disabled:hover, .ac_results li.disabled:hover {
  color: #f6f9fb;
}

.dropdown-item.disabled:focus, .ac_results li.disabled:focus, .dropdown-item.disabled:hover, .ac_results li.disabled:hover {
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
  background-image: none;
  filter: "progid:DXImageTransform.Microsoft.gradient(enabled = false)";
}

.open > .dropdown-menu, .open > .ac_results {
  display: block;
}

.open > a {
  outline: 0;
}

.dropdown-menu-right {
  right: 0;
  left: auto;
}

.dropdown-menu-left {
  right: auto;
  left: 0;
}

.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 0.75rem;
  line-height: 1.5;
  color: #a3a3a3;
  white-space: nowrap;
}

.dropdown-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 990;
}

.pull-right > .dropdown-menu, .pull-right > .ac_results {
  right: 0;
  left: auto;
}

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  content: "";
  border-top: 0;
  border-bottom: 0.375em solid;
}

.dropup .dropdown-menu, .dropup .ac_results,
.navbar-fixed-bottom .dropdown .dropdown-menu,
.navbar-fixed-bottom .dropdown .ac_results {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px;
}

.list-group {
  padding-left: 0;
  margin-bottom: 0;
}

.list-group-item {
  position: relative;
  display: block;
  padding: .75rem 1.25rem;
  margin-bottom: -0.0625rem;
  background-color: #fff;
  border: 0.0625rem solid #ddd;
}

.list-group-item:first-child {
  border-top-right-radius: 0.125rem;
  border-top-left-radius: 0.125rem;
}

.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0.125rem;
  border-bottom-left-radius: 0.125rem;
}

.list-group-flush .list-group-item {
  border-width: 0.0625rem 0;
  border-radius: 0;
}

.list-group-flush:first-child .list-group-item:first-child {
  border-top: 0;
}

.list-group-flush:last-child .list-group-item:last-child {
  border-bottom: 0;
}

a.list-group-item,
button.list-group-item {
  width: 100%;
  color: #555;
  text-align: inherit;
}

a.list-group-item .list-group-item-heading,
button.list-group-item .list-group-item-heading {
  color: #333;
}

a.list-group-item:focus, a.list-group-item:hover,
button.list-group-item:focus,
button.list-group-item:hover {
  color: #555;
  text-decoration: none;
  background-color: #f5f5f5;
}

.list-group-item.disabled, .list-group-item.disabled:focus, .list-group-item.disabled:hover {
  color: #f6f9fb;
  cursor: not-allowed;
  background-color: #f4f6fb;
}

.list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading {
  color: inherit;
}

.list-group-item.disabled .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text {
  color: #f6f9fb;
}

.list-group-item.active, .file-upload input:focus ~ .list-group-item.file-upload-choose,
.file-upload input:focus ~ .list-group-item.file-upload-browse,
.file-upload input:hover ~ .list-group-item.file-upload-choose,
.file-upload input:hover ~ .list-group-item.file-upload-browse, select.list-group-item:focus, .list-group-item.drop-enabled, .list-group-item.active:focus, .file-upload input:focus ~ .list-group-item.file-upload-choose:focus,
.file-upload input:focus ~ .list-group-item.file-upload-browse:focus,
.file-upload input:hover ~ .list-group-item.file-upload-choose:focus,
.file-upload input:hover ~ .list-group-item.file-upload-browse:focus, select.list-group-item:focus, .list-group-item.drop-enabled:focus, .list-group-item.active:hover, .file-upload input:focus ~ .list-group-item.file-upload-choose:hover,
.file-upload input:focus ~ .list-group-item.file-upload-browse:hover,
.file-upload input:hover ~ .list-group-item.file-upload-choose:hover,
.file-upload input:hover ~ .list-group-item.file-upload-browse:hover, select.list-group-item:hover:focus, .list-group-item.drop-enabled:hover {
  z-index: 2;
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
}

.list-group-item.active .list-group-item-heading, .file-upload input:focus ~ .list-group-item.file-upload-choose .list-group-item-heading,
.file-upload input:focus ~ .list-group-item.file-upload-browse .list-group-item-heading,
.file-upload input:hover ~ .list-group-item.file-upload-choose .list-group-item-heading,
.file-upload input:hover ~ .list-group-item.file-upload-browse .list-group-item-heading, select.list-group-item:focus .list-group-item-heading, .list-group-item.drop-enabled .list-group-item-heading,
.list-group-item.active .list-group-item-heading > small, .file-upload input:focus ~ .list-group-item.file-upload-choose .list-group-item-heading > small,
.file-upload input:focus ~ .list-group-item.file-upload-browse .list-group-item-heading > small,
.file-upload input:hover ~ .list-group-item.file-upload-choose .list-group-item-heading > small,
.file-upload input:hover ~ .list-group-item.file-upload-browse .list-group-item-heading > small, select.list-group-item:focus .list-group-item-heading > small, .list-group-item.drop-enabled .list-group-item-heading > small,
.list-group-item.active .list-group-item-heading > .small, .file-upload input:focus ~ .list-group-item.file-upload-choose .list-group-item-heading > .small,
.file-upload input:focus ~ .list-group-item.file-upload-browse .list-group-item-heading > .small,
.file-upload input:hover ~ .list-group-item.file-upload-choose .list-group-item-heading > .small,
.file-upload input:hover ~ .list-group-item.file-upload-browse .list-group-item-heading > .small, select.list-group-item:focus .list-group-item-heading > .small, .list-group-item.drop-enabled .list-group-item-heading > .small, .list-group-item.active:focus .list-group-item-heading, .file-upload input:focus ~ .list-group-item.file-upload-choose:focus .list-group-item-heading,
.file-upload input:focus ~ .list-group-item.file-upload-browse:focus .list-group-item-heading,
.file-upload input:hover ~ .list-group-item.file-upload-choose:focus .list-group-item-heading,
.file-upload input:hover ~ .list-group-item.file-upload-browse:focus .list-group-item-heading, select.list-group-item:focus .list-group-item-heading,
select.list-group-item.form-control:focus .list-group-item-heading, select.list-group-item.ac_input:focus .list-group-item-heading, .list-group-item.drop-enabled:focus .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading > small, .file-upload input:focus ~ .list-group-item.file-upload-choose:focus .list-group-item-heading > small,
.file-upload input:focus ~ .list-group-item.file-upload-browse:focus .list-group-item-heading > small,
.file-upload input:hover ~ .list-group-item.file-upload-choose:focus .list-group-item-heading > small,
.file-upload input:hover ~ .list-group-item.file-upload-browse:focus .list-group-item-heading > small, select.list-group-item:focus .list-group-item-heading > small,
select.list-group-item.form-control:focus .list-group-item-heading > small, select.list-group-item.ac_input:focus .list-group-item-heading > small, .list-group-item.drop-enabled:focus .list-group-item-heading > small,
.list-group-item.active:focus .list-group-item-heading > .small, .file-upload input:focus ~ .list-group-item.file-upload-choose:focus .list-group-item-heading > .small,
.file-upload input:focus ~ .list-group-item.file-upload-browse:focus .list-group-item-heading > .small,
.file-upload input:hover ~ .list-group-item.file-upload-choose:focus .list-group-item-heading > .small,
.file-upload input:hover ~ .list-group-item.file-upload-browse:focus .list-group-item-heading > .small, select.list-group-item:focus .list-group-item-heading > .small,
select.list-group-item.form-control:focus .list-group-item-heading > .small, select.list-group-item.ac_input:focus .list-group-item-heading > .small, .list-group-item.drop-enabled:focus .list-group-item-heading > .small, .list-group-item.active:hover .list-group-item-heading, .file-upload input:focus ~ .list-group-item.file-upload-choose:hover .list-group-item-heading,
.file-upload input:focus ~ .list-group-item.file-upload-browse:hover .list-group-item-heading,
.file-upload input:hover ~ .list-group-item.file-upload-choose:hover .list-group-item-heading,
.file-upload input:hover ~ .list-group-item.file-upload-browse:hover .list-group-item-heading, select.list-group-item:hover:focus .list-group-item-heading, .list-group-item.drop-enabled:hover .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading > small, .file-upload input:focus ~ .list-group-item.file-upload-choose:hover .list-group-item-heading > small,
.file-upload input:focus ~ .list-group-item.file-upload-browse:hover .list-group-item-heading > small,
.file-upload input:hover ~ .list-group-item.file-upload-choose:hover .list-group-item-heading > small,
.file-upload input:hover ~ .list-group-item.file-upload-browse:hover .list-group-item-heading > small, select.list-group-item:hover:focus .list-group-item-heading > small, .list-group-item.drop-enabled:hover .list-group-item-heading > small,
.list-group-item.active:hover .list-group-item-heading > .small, .file-upload input:focus ~ .list-group-item.file-upload-choose:hover .list-group-item-heading > .small,
.file-upload input:focus ~ .list-group-item.file-upload-browse:hover .list-group-item-heading > .small,
.file-upload input:hover ~ .list-group-item.file-upload-choose:hover .list-group-item-heading > .small,
.file-upload input:hover ~ .list-group-item.file-upload-browse:hover .list-group-item-heading > .small, select.list-group-item:hover:focus .list-group-item-heading > .small, .list-group-item.drop-enabled:hover .list-group-item-heading > .small {
  color: inherit;
}

.list-group-item.active .list-group-item-text, .file-upload input:focus ~ .list-group-item.file-upload-choose .list-group-item-text,
.file-upload input:focus ~ .list-group-item.file-upload-browse .list-group-item-text,
.file-upload input:hover ~ .list-group-item.file-upload-choose .list-group-item-text,
.file-upload input:hover ~ .list-group-item.file-upload-browse .list-group-item-text, select.list-group-item:focus .list-group-item-text, .list-group-item.drop-enabled .list-group-item-text, .list-group-item.active:focus .list-group-item-text, .file-upload input:focus ~ .list-group-item.file-upload-choose:focus .list-group-item-text,
.file-upload input:focus ~ .list-group-item.file-upload-browse:focus .list-group-item-text,
.file-upload input:hover ~ .list-group-item.file-upload-choose:focus .list-group-item-text,
.file-upload input:hover ~ .list-group-item.file-upload-browse:focus .list-group-item-text, select.list-group-item:focus .list-group-item-text,
select.list-group-item.form-control:focus .list-group-item-text, select.list-group-item.ac_input:focus .list-group-item-text, .list-group-item.drop-enabled:focus .list-group-item-text, .list-group-item.active:hover .list-group-item-text, .file-upload input:focus ~ .list-group-item.file-upload-choose:hover .list-group-item-text,
.file-upload input:focus ~ .list-group-item.file-upload-browse:hover .list-group-item-text,
.file-upload input:hover ~ .list-group-item.file-upload-choose:hover .list-group-item-text,
.file-upload input:hover ~ .list-group-item.file-upload-browse:hover .list-group-item-text, select.list-group-item:hover:focus .list-group-item-text, .list-group-item.drop-enabled:hover .list-group-item-text {
  color: #abe2fe;
}

.list-group-item-success {
  color: #fff;
  background-color: #6bc573;
}

a.list-group-item-success,
button.list-group-item-success {
  color: #fff;
}

a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
  color: inherit;
}

a.list-group-item-success:focus, a.list-group-item-success:hover,
button.list-group-item-success:focus,
button.list-group-item-success:hover {
  color: #fff;
  background-color: #59be62;
}

a.list-group-item-success.active, .file-upload input:focus ~ a.list-group-item-success.file-upload-choose,
.file-upload input:focus ~ a.list-group-item-success.file-upload-browse,
.file-upload input:hover ~ a.list-group-item-success.file-upload-choose,
.file-upload input:hover ~ a.list-group-item-success.file-upload-browse, a.list-group-item-success.drop-enabled, a.list-group-item-success.active:focus, .file-upload input:focus ~ a.list-group-item-success.file-upload-choose:focus,
.file-upload input:focus ~ a.list-group-item-success.file-upload-browse:focus,
.file-upload input:hover ~ a.list-group-item-success.file-upload-choose:focus,
.file-upload input:hover ~ a.list-group-item-success.file-upload-browse:focus, a.list-group-item-success.drop-enabled:focus, a.list-group-item-success.active:hover, .file-upload input:focus ~ a.list-group-item-success.file-upload-choose:hover,
.file-upload input:focus ~ a.list-group-item-success.file-upload-browse:hover,
.file-upload input:hover ~ a.list-group-item-success.file-upload-choose:hover,
.file-upload input:hover ~ a.list-group-item-success.file-upload-browse:hover, a.list-group-item-success.drop-enabled:hover,
button.list-group-item-success.active,
.file-upload input:focus ~ button.list-group-item-success.file-upload-choose,
.file-upload input:focus ~ button.list-group-item-success.file-upload-browse,
.file-upload input:hover ~ button.list-group-item-success.file-upload-choose,
.file-upload input:hover ~ button.list-group-item-success.file-upload-browse,
button.list-group-item-success.drop-enabled,
button.list-group-item-success.active:focus,
.file-upload input:focus ~ button.list-group-item-success.file-upload-choose:focus,
.file-upload input:focus ~ button.list-group-item-success.file-upload-browse:focus,
.file-upload input:hover ~ button.list-group-item-success.file-upload-choose:focus,
.file-upload input:hover ~ button.list-group-item-success.file-upload-browse:focus,
button.list-group-item-success.drop-enabled:focus,
button.list-group-item-success.active:hover,
.file-upload input:focus ~ button.list-group-item-success.file-upload-choose:hover,
.file-upload input:focus ~ button.list-group-item-success.file-upload-browse:hover,
.file-upload input:hover ~ button.list-group-item-success.file-upload-choose:hover,
.file-upload input:hover ~ button.list-group-item-success.file-upload-browse:hover,
button.list-group-item-success.drop-enabled:hover {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-item-info {
  color: #fff;
  background-color: #a1b8c6;
}

a.list-group-item-info,
button.list-group-item-info {
  color: #fff;
}

a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
  color: inherit;
}

a.list-group-item-info:focus, a.list-group-item-info:hover,
button.list-group-item-info:focus,
button.list-group-item-info:hover {
  color: #fff;
  background-color: #91acbc;
}

a.list-group-item-info.active, .file-upload input:focus ~ a.list-group-item-info.file-upload-choose,
.file-upload input:focus ~ a.list-group-item-info.file-upload-browse,
.file-upload input:hover ~ a.list-group-item-info.file-upload-choose,
.file-upload input:hover ~ a.list-group-item-info.file-upload-browse, a.list-group-item-info.drop-enabled, a.list-group-item-info.active:focus, .file-upload input:focus ~ a.list-group-item-info.file-upload-choose:focus,
.file-upload input:focus ~ a.list-group-item-info.file-upload-browse:focus,
.file-upload input:hover ~ a.list-group-item-info.file-upload-choose:focus,
.file-upload input:hover ~ a.list-group-item-info.file-upload-browse:focus, a.list-group-item-info.drop-enabled:focus, a.list-group-item-info.active:hover, .file-upload input:focus ~ a.list-group-item-info.file-upload-choose:hover,
.file-upload input:focus ~ a.list-group-item-info.file-upload-browse:hover,
.file-upload input:hover ~ a.list-group-item-info.file-upload-choose:hover,
.file-upload input:hover ~ a.list-group-item-info.file-upload-browse:hover, a.list-group-item-info.drop-enabled:hover,
button.list-group-item-info.active,
.file-upload input:focus ~ button.list-group-item-info.file-upload-choose,
.file-upload input:focus ~ button.list-group-item-info.file-upload-browse,
.file-upload input:hover ~ button.list-group-item-info.file-upload-choose,
.file-upload input:hover ~ button.list-group-item-info.file-upload-browse,
button.list-group-item-info.drop-enabled,
button.list-group-item-info.active:focus,
.file-upload input:focus ~ button.list-group-item-info.file-upload-choose:focus,
.file-upload input:focus ~ button.list-group-item-info.file-upload-browse:focus,
.file-upload input:hover ~ button.list-group-item-info.file-upload-choose:focus,
.file-upload input:hover ~ button.list-group-item-info.file-upload-browse:focus,
button.list-group-item-info.drop-enabled:focus,
button.list-group-item-info.active:hover,
.file-upload input:focus ~ button.list-group-item-info.file-upload-choose:hover,
.file-upload input:focus ~ button.list-group-item-info.file-upload-browse:hover,
.file-upload input:hover ~ button.list-group-item-info.file-upload-choose:hover,
.file-upload input:hover ~ button.list-group-item-info.file-upload-browse:hover,
button.list-group-item-info.drop-enabled:hover {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-item-warning {
  color: #fff;
  background-color: #faae42;
}

a.list-group-item-warning,
button.list-group-item-warning {
  color: #fff;
}

a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
  color: inherit;
}

a.list-group-item-warning:focus, a.list-group-item-warning:hover,
button.list-group-item-warning:focus,
button.list-group-item-warning:hover {
  color: #fff;
  background-color: #f9a329;
}

a.list-group-item-warning.active, .file-upload input:focus ~ a.list-group-item-warning.file-upload-choose,
.file-upload input:focus ~ a.list-group-item-warning.file-upload-browse,
.file-upload input:hover ~ a.list-group-item-warning.file-upload-choose,
.file-upload input:hover ~ a.list-group-item-warning.file-upload-browse, a.list-group-item-warning.drop-enabled, a.list-group-item-warning.active:focus, .file-upload input:focus ~ a.list-group-item-warning.file-upload-choose:focus,
.file-upload input:focus ~ a.list-group-item-warning.file-upload-browse:focus,
.file-upload input:hover ~ a.list-group-item-warning.file-upload-choose:focus,
.file-upload input:hover ~ a.list-group-item-warning.file-upload-browse:focus, a.list-group-item-warning.drop-enabled:focus, a.list-group-item-warning.active:hover, .file-upload input:focus ~ a.list-group-item-warning.file-upload-choose:hover,
.file-upload input:focus ~ a.list-group-item-warning.file-upload-browse:hover,
.file-upload input:hover ~ a.list-group-item-warning.file-upload-choose:hover,
.file-upload input:hover ~ a.list-group-item-warning.file-upload-browse:hover, a.list-group-item-warning.drop-enabled:hover,
button.list-group-item-warning.active,
.file-upload input:focus ~ button.list-group-item-warning.file-upload-choose,
.file-upload input:focus ~ button.list-group-item-warning.file-upload-browse,
.file-upload input:hover ~ button.list-group-item-warning.file-upload-choose,
.file-upload input:hover ~ button.list-group-item-warning.file-upload-browse,
button.list-group-item-warning.drop-enabled,
button.list-group-item-warning.active:focus,
.file-upload input:focus ~ button.list-group-item-warning.file-upload-choose:focus,
.file-upload input:focus ~ button.list-group-item-warning.file-upload-browse:focus,
.file-upload input:hover ~ button.list-group-item-warning.file-upload-choose:focus,
.file-upload input:hover ~ button.list-group-item-warning.file-upload-browse:focus,
button.list-group-item-warning.drop-enabled:focus,
button.list-group-item-warning.active:hover,
.file-upload input:focus ~ button.list-group-item-warning.file-upload-choose:hover,
.file-upload input:focus ~ button.list-group-item-warning.file-upload-browse:hover,
.file-upload input:hover ~ button.list-group-item-warning.file-upload-choose:hover,
.file-upload input:hover ~ button.list-group-item-warning.file-upload-browse:hover,
button.list-group-item-warning.drop-enabled:hover {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-item-danger {
  color: #fff;
  background-color: #fa2e1f;
}

a.list-group-item-danger,
button.list-group-item-danger {
  color: #fff;
}

a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
  color: inherit;
}

a.list-group-item-danger:focus, a.list-group-item-danger:hover,
button.list-group-item-danger:focus,
button.list-group-item-danger:hover {
  color: #fff;
  background-color: #f91706;
}

a.list-group-item-danger.active, .file-upload input:focus ~ a.list-group-item-danger.file-upload-choose,
.file-upload input:focus ~ a.list-group-item-danger.file-upload-browse,
.file-upload input:hover ~ a.list-group-item-danger.file-upload-choose,
.file-upload input:hover ~ a.list-group-item-danger.file-upload-browse, a.list-group-item-danger.drop-enabled, a.list-group-item-danger.active:focus, .file-upload input:focus ~ a.list-group-item-danger.file-upload-choose:focus,
.file-upload input:focus ~ a.list-group-item-danger.file-upload-browse:focus,
.file-upload input:hover ~ a.list-group-item-danger.file-upload-choose:focus,
.file-upload input:hover ~ a.list-group-item-danger.file-upload-browse:focus, a.list-group-item-danger.drop-enabled:focus, a.list-group-item-danger.active:hover, .file-upload input:focus ~ a.list-group-item-danger.file-upload-choose:hover,
.file-upload input:focus ~ a.list-group-item-danger.file-upload-browse:hover,
.file-upload input:hover ~ a.list-group-item-danger.file-upload-choose:hover,
.file-upload input:hover ~ a.list-group-item-danger.file-upload-browse:hover, a.list-group-item-danger.drop-enabled:hover,
button.list-group-item-danger.active,
.file-upload input:focus ~ button.list-group-item-danger.file-upload-choose,
.file-upload input:focus ~ button.list-group-item-danger.file-upload-browse,
.file-upload input:hover ~ button.list-group-item-danger.file-upload-choose,
.file-upload input:hover ~ button.list-group-item-danger.file-upload-browse,
button.list-group-item-danger.drop-enabled,
button.list-group-item-danger.active:focus,
.file-upload input:focus ~ button.list-group-item-danger.file-upload-choose:focus,
.file-upload input:focus ~ button.list-group-item-danger.file-upload-browse:focus,
.file-upload input:hover ~ button.list-group-item-danger.file-upload-choose:focus,
.file-upload input:hover ~ button.list-group-item-danger.file-upload-browse:focus,
button.list-group-item-danger.drop-enabled:focus,
button.list-group-item-danger.active:hover,
.file-upload input:focus ~ button.list-group-item-danger.file-upload-choose:hover,
.file-upload input:focus ~ button.list-group-item-danger.file-upload-browse:hover,
.file-upload input:hover ~ button.list-group-item-danger.file-upload-choose:hover,
.file-upload input:hover ~ button.list-group-item-danger.file-upload-browse:hover,
button.list-group-item-danger.drop-enabled:hover {
  color: #fff;
  background-color: #fff;
  border-color: #fff;
}

.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}

.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}

.form-control, .ac_input {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #555a62;
  background-color: #fff;
  background-image: none;
  border: 0.0625rem solid #ccc;
  border-radius: 0.125rem;
}

.form-control::-ms-expand, .ac_input::-ms-expand {
  background-color: transparent;
  border: 0;
}

.form-control:focus, .ac_input:focus {
  border-color: #66afe9;
  outline: none;
}

.form-control::-webkit-input-placeholder, .ac_input::-webkit-input-placeholder {
  color: #999;
  opacity: 1;
}

.form-control::-moz-placeholder, .ac_input::-moz-placeholder {
  color: #999;
  opacity: 1;
}

.form-control:-ms-input-placeholder, .ac_input:-ms-input-placeholder {
  color: #999;
  opacity: 1;
}

.form-control::placeholder, .ac_input::placeholder {
  color: #999;
  opacity: 1;
}

.form-control:disabled, .ac_input:disabled, .form-control[readonly], [readonly].ac_input {
  background-color: #f4f6fb;
  opacity: 1;
}

.form-control:disabled, .ac_input:disabled {
  cursor: not-allowed;
}

.form-control-file,
.form-control-range {
  display: block;
}

.form-control-label {
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"].form-control, input[type="date"].ac_input,
  input[type="time"].form-control,
  input[type="time"].ac_input,
  input[type="datetime-local"].form-control,
  input[type="datetime-local"].ac_input,
  input[type="month"].form-control,
  input[type="month"].ac_input {
    line-height: 2.0625rem;
  }
  input[type="date"].input-sm,
  .input-group-sm input[type="date"].form-control,
  .input-group-sm input[type="date"].ac_input,
  input[type="time"].input-sm,
  .input-group-sm
  input[type="time"].form-control,
  .input-group-sm input[type="time"].ac_input,
  input[type="datetime-local"].input-sm,
  .input-group-sm
  input[type="datetime-local"].form-control,
  .input-group-sm input[type="datetime-local"].ac_input,
  input[type="month"].input-sm,
  .input-group-sm
  input[type="month"].form-control,
  .input-group-sm input[type="month"].ac_input {
    line-height: 1.675rem;
  }
  input[type="date"].input-lg,
  .input-group-lg input[type="date"].form-control,
  .input-group-lg input[type="date"].ac_input,
  input[type="time"].input-lg,
  .input-group-lg
  input[type="time"].form-control,
  .input-group-lg input[type="time"].ac_input,
  input[type="datetime-local"].input-lg,
  .input-group-lg
  input[type="datetime-local"].form-control,
  .input-group-lg input[type="datetime-local"].ac_input,
  input[type="month"].input-lg,
  .input-group-lg
  input[type="month"].form-control,
  .input-group-lg input[type="month"].ac_input {
    line-height: 3rem;
  }
}

.form-control-static {
  min-height: 2.0625rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  margin-bottom: 0;
}

.form-control-static.form-control-sm, .form-control-static.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  padding: 0.275rem 0.75rem;
  font-size: 0.75rem;
  line-height: 1.5;
  border-radius: 0.0625rem;
}

.form-control-lg {
  padding: 0.75rem 1.25rem;
  font-size: 1.125rem;
  line-height: 1.3333333333;
  border-radius: 0.25rem;
}

.form-group {
  margin-bottom: 0.75rem;
}

.radio,
.checkbox {
  position: relative;
  display: block;
  margin-bottom: 0.84375rem;
}

.radio label,
.checkbox label {
  padding-left: 1.25rem;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}

.radio label input:only-child,
.checkbox label input:only-child {
  position: static;
}

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-top: .25rem;
  margin-left: -1.25rem;
}

.radio + .radio,
.checkbox + .checkbox {
  margin-top: -.25rem;
}

.radio-inline,
.checkbox-inline {
  position: relative;
  display: inline-block;
  padding-left: 1.25rem;
  margin-bottom: 0;
  font-weight: normal;
  vertical-align: middle;
  cursor: pointer;
}

.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: .75rem;
}

input[type="radio"]:disabled, input[type="radio"].disabled,
input[type="checkbox"]:disabled,
input[type="checkbox"].disabled {
  cursor: not-allowed;
}

.radio-inline.disabled,
.checkbox-inline.disabled {
  cursor: not-allowed;
}

.radio.disabled label,
.checkbox.disabled label {
  cursor: not-allowed;
}

.form-control-success,
.form-control-warning,
.form-control-danger {
  padding-right: 2.25rem;
  background-repeat: no-repeat;
  background-position: center right 0.515625rem;
  background-size: 1.340625rem 1.340625rem;
}

.has-success .text-help,
.has-success .form-control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label {
  color: #6bc573;
}

.has-success .form-control, .has-success .ac_input {
  border-color: #6bc573;
}

.has-success .input-group-addon {
  color: #6bc573;
  border-color: #6bc573;
  background-color: #fefefe;
}

.has-success .form-control-feedback {
  color: #6bc573;
}

.has-success .form-control-success {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2MTIgNzkyIj48cGF0aCBmaWxsPSIjNWNiODVjIiBkPSJNMjMzLjggNjEwYy0xMy4zIDAtMjYtNi0zNC0xNi44TDkwLjUgNDQ4LjhDNzYuMyA0MzAgODAgNDAzLjMgOTguOCAzODljMTguOC0xNC4yIDQ1LjUtMTAuNCA1OS44IDguNGw3MiA5NUw0NTEuMyAyNDJjMTIuNS0yMCAzOC44LTI2LjIgNTguOC0xMy43IDIwIDEyLjQgMjYgMzguNyAxMy43IDU4LjhMMjcwIDU5MGMtNy40IDEyLTIwLjIgMTkuNC0zNC4zIDIwaC0yeiIvPjwvc3ZnPg==");
}

.has-warning .text-help,
.has-warning .form-control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label {
  color: #faae42;
}

.has-warning .form-control, .has-warning .ac_input {
  border-color: #faae42;
}

.has-warning .input-group-addon {
  color: #faae42;
  border-color: #faae42;
  background-color: white;
}

.has-warning .form-control-feedback {
  color: #faae42;
}

.has-warning .form-control-warning {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2MTIgNzkyIj48cGF0aCBmaWxsPSIjZjBhZDRlIiBkPSJNNjAzIDY0MC4ybC0yNzguNS01MDljLTMuOC02LjYtMTAuOC0xMC42LTE4LjUtMTAuNnMtMTQuNyA0LTE4LjUgMTAuNkw5IDY0MC4yYy0zLjcgNi41LTMuNiAxNC40LjIgMjAuOCAzLjggNi41IDEwLjggMTAuNCAxOC4zIDEwLjRoNTU3YzcuNiAwIDE0LjYtNCAxOC40LTEwLjQgMy41LTYuNCAzLjYtMTQuNCAwLTIwLjh6bS0yNjYuNC0zMGgtNjEuMlY1NDloNjEuMnY2MS4yem0wLTEwN2gtNjEuMlYzMDRoNjEuMnYxOTl6Ii8+PC9zdmc+");
}

.has-danger .text-help,
.has-danger .form-control-label,
.has-danger .radio,
.has-danger .checkbox,
.has-danger .radio-inline,
.has-danger .checkbox-inline,
.has-danger.radio label,
.has-danger.checkbox label,
.has-danger.radio-inline label,
.has-danger.checkbox-inline label {
  color: #fa2e1f;
}

.has-danger .form-control, .has-danger .ac_input {
  border-color: #fa2e1f;
}

.has-danger .input-group-addon {
  color: #fa2e1f;
  border-color: #fa2e1f;
  background-color: #fee8e7;
}

.has-danger .form-control-feedback {
  color: #fa2e1f;
}

.has-danger .form-control-danger {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2MTIgNzkyIj48cGF0aCBmaWxsPSIjZDk1MzRmIiBkPSJNNDQ3IDU0NC40Yy0xNC40IDE0LjQtMzcuNiAxNC40LTUyIDBsLTg5LTkyLjctODkgOTIuN2MtMTQuNSAxNC40LTM3LjcgMTQuNC01MiAwLTE0LjQtMTQuNC0xNC40LTM3LjYgMC01Mmw5Mi40LTk2LjMtOTIuNC05Ni4zYy0xNC40LTE0LjQtMTQuNC0zNy42IDAtNTJzMzcuNi0xNC4zIDUyIDBsODkgOTIuOCA4OS4yLTkyLjdjMTQuNC0xNC40IDM3LjYtMTQuNCA1MiAwIDE0LjMgMTQuNCAxNC4zIDM3LjYgMCA1MkwzNTQuNiAzOTZsOTIuNCA5Ni40YzE0LjQgMTQuNCAxNC40IDM3LjYgMCA1MnoiLz48L3N2Zz4=");
}

@media (min-width: 544px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control, .form-inline .ac_input {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-static {
    display: inline-block;
  }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .form-inline .input-group .input-group-addon,
  .form-inline .input-group .input-group-btn,
  .form-inline .input-group .form-control,
  .form-inline .input-group .ac_input {
    width: auto;
  }
  .form-inline .input-group > .form-control, .form-inline .input-group > .ac_input {
    width: 100%;
  }
  .form-inline .form-control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio label,
  .form-inline .checkbox label {
    padding-left: 0;
  }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}

.container {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}

@media (min-width: 544px) {
  .container {
    max-width: 576px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 940px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

.container-fluid {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}

.row, .footer, .form-group, .label-selector, .media-addon,
.media-callout, .plank-container-grid {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
}

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12, .form-group > .label-wrap-wide, .Homepage .label-selector-item, .label-selector-item, .media-callout .media-left, .modal .media-callout .media-left, .plank-container-grid .plank-wrapper, .col-xs-13, .col-xs-14, .col-xs-15, .col-xs-16, .col-xs-17, .col-xs-18, .col-xs-19, .col-xs-20, .col-xs-21, .col-xs-22, .col-xs-23, .col-xs-24, .form-group > .input-wrap, .form-group > .color-picker, .form-group > .label-wrap, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-13, .col-sm-14, .col-sm-15, .col-sm-16, .col-sm-17, .col-sm-18, .col-sm-19, .col-sm-20, .col-sm-21, .col-sm-22, .col-sm-23, .col-sm-24, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md-13, .col-md-14, .col-md-15, .col-md-16, .col-md-17, .col-md-18, .col-md-19, .col-md-20, .col-md-21, .col-md-22, .col-md-23, .col-md-24, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-13, .col-lg-14, .col-lg-15, .col-lg-16, .col-lg-17, .col-lg-18, .col-lg-19, .col-lg-20, .col-lg-21, .col-lg-22, .col-lg-23, .col-lg-24, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-13, .col-xl-14, .col-xl-15, .col-xl-16, .col-xl-17, .col-xl-18, .col-xl-19, .col-xl-20, .col-xl-21, .col-xl-22, .col-xl-23, .col-xl-24 {
  position: relative;
  min-height: 1px;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}

.col-xs-1 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 4.1666666667%;
      -ms-flex: 0 0 4.1666666667%;
          flex: 0 0 4.1666666667%;
}

.col-xs-2 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 8.3333333333%;
      -ms-flex: 0 0 8.3333333333%;
          flex: 0 0 8.3333333333%;
}

.col-xs-3 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 12.5%;
      -ms-flex: 0 0 12.5%;
          flex: 0 0 12.5%;
}

.col-xs-4 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 16.6666666667%;
      -ms-flex: 0 0 16.6666666667%;
          flex: 0 0 16.6666666667%;
}

.col-xs-5 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 20.8333333333%;
      -ms-flex: 0 0 20.8333333333%;
          flex: 0 0 20.8333333333%;
}

.col-xs-6 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 25%;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
}

.col-xs-7 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 29.1666666667%;
      -ms-flex: 0 0 29.1666666667%;
          flex: 0 0 29.1666666667%;
}

.col-xs-8 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 33.3333333333%;
      -ms-flex: 0 0 33.3333333333%;
          flex: 0 0 33.3333333333%;
}

.col-xs-9 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 37.5%;
      -ms-flex: 0 0 37.5%;
          flex: 0 0 37.5%;
}

.col-xs-10 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 41.6666666667%;
      -ms-flex: 0 0 41.6666666667%;
          flex: 0 0 41.6666666667%;
}

.col-xs-11 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 45.8333333333%;
      -ms-flex: 0 0 45.8333333333%;
          flex: 0 0 45.8333333333%;
}

.col-xs-12, .form-group > .label-wrap-wide, .Homepage .label-selector-item, .label-selector-item, .media-callout .media-left, .modal .media-callout .media-left, .plank-container-grid .plank-wrapper {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 50%;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
}

.col-xs-13 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 54.1666666667%;
      -ms-flex: 0 0 54.1666666667%;
          flex: 0 0 54.1666666667%;
}

.col-xs-14 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 58.3333333333%;
      -ms-flex: 0 0 58.3333333333%;
          flex: 0 0 58.3333333333%;
}

.col-xs-15 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 62.5%;
      -ms-flex: 0 0 62.5%;
          flex: 0 0 62.5%;
}

.col-xs-16 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 66.6666666667%;
      -ms-flex: 0 0 66.6666666667%;
          flex: 0 0 66.6666666667%;
}

.col-xs-17 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 70.8333333333%;
      -ms-flex: 0 0 70.8333333333%;
          flex: 0 0 70.8333333333%;
}

.col-xs-18 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 75%;
      -ms-flex: 0 0 75%;
          flex: 0 0 75%;
}

.col-xs-19 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 79.1666666667%;
      -ms-flex: 0 0 79.1666666667%;
          flex: 0 0 79.1666666667%;
}

.col-xs-20 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 83.3333333333%;
      -ms-flex: 0 0 83.3333333333%;
          flex: 0 0 83.3333333333%;
}

.col-xs-21 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 87.5%;
      -ms-flex: 0 0 87.5%;
          flex: 0 0 87.5%;
}

.col-xs-22 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 91.6666666667%;
      -ms-flex: 0 0 91.6666666667%;
          flex: 0 0 91.6666666667%;
}

.col-xs-23 {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 95.8333333333%;
      -ms-flex: 0 0 95.8333333333%;
          flex: 0 0 95.8333333333%;
}

.col-xs-24, .form-group > .input-wrap, .form-group > .color-picker, .form-group > .label-wrap {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 100%;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

.col-xs-pull-0 {
  right: auto;
}

.col-xs-pull-1 {
  right: 4.1666666667%;
}

.col-xs-pull-2 {
  right: 8.3333333333%;
}

.col-xs-pull-3 {
  right: 12.5%;
}

.col-xs-pull-4 {
  right: 16.6666666667%;
}

.col-xs-pull-5 {
  right: 20.8333333333%;
}

.col-xs-pull-6 {
  right: 25%;
}

.col-xs-pull-7 {
  right: 29.1666666667%;
}

.col-xs-pull-8 {
  right: 33.3333333333%;
}

.col-xs-pull-9 {
  right: 37.5%;
}

.col-xs-pull-10 {
  right: 41.6666666667%;
}

.col-xs-pull-11 {
  right: 45.8333333333%;
}

.col-xs-pull-12 {
  right: 50%;
}

.col-xs-pull-13 {
  right: 54.1666666667%;
}

.col-xs-pull-14 {
  right: 58.3333333333%;
}

.col-xs-pull-15 {
  right: 62.5%;
}

.col-xs-pull-16 {
  right: 66.6666666667%;
}

.col-xs-pull-17 {
  right: 70.8333333333%;
}

.col-xs-pull-18 {
  right: 75%;
}

.col-xs-pull-19 {
  right: 79.1666666667%;
}

.col-xs-pull-20 {
  right: 83.3333333333%;
}

.col-xs-pull-21 {
  right: 87.5%;
}

.col-xs-pull-22 {
  right: 91.6666666667%;
}

.col-xs-pull-23 {
  right: 95.8333333333%;
}

.col-xs-pull-24 {
  right: 100%;
}

.col-xs-push-0 {
  left: auto;
}

.col-xs-push-1 {
  left: 4.1666666667%;
}

.col-xs-push-2 {
  left: 8.3333333333%;
}

.col-xs-push-3 {
  left: 12.5%;
}

.col-xs-push-4 {
  left: 16.6666666667%;
}

.col-xs-push-5 {
  left: 20.8333333333%;
}

.col-xs-push-6 {
  left: 25%;
}

.col-xs-push-7 {
  left: 29.1666666667%;
}

.col-xs-push-8 {
  left: 33.3333333333%;
}

.col-xs-push-9 {
  left: 37.5%;
}

.col-xs-push-10 {
  left: 41.6666666667%;
}

.col-xs-push-11 {
  left: 45.8333333333%;
}

.col-xs-push-12 {
  left: 50%;
}

.col-xs-push-13 {
  left: 54.1666666667%;
}

.col-xs-push-14 {
  left: 58.3333333333%;
}

.col-xs-push-15 {
  left: 62.5%;
}

.col-xs-push-16 {
  left: 66.6666666667%;
}

.col-xs-push-17 {
  left: 70.8333333333%;
}

.col-xs-push-18 {
  left: 75%;
}

.col-xs-push-19 {
  left: 79.1666666667%;
}

.col-xs-push-20 {
  left: 83.3333333333%;
}

.col-xs-push-21 {
  left: 87.5%;
}

.col-xs-push-22 {
  left: 91.6666666667%;
}

.col-xs-push-23 {
  left: 95.8333333333%;
}

.col-xs-push-24 {
  left: 100%;
}

.col-xs-offset-0, .form-group > .input-wrap.no-label, .form-group > .no-label.color-picker {
  margin-left: 0%;
}

.col-xs-offset-1 {
  margin-left: 4.1666666667%;
}

.col-xs-offset-2 {
  margin-left: 8.3333333333%;
}

.col-xs-offset-3 {
  margin-left: 12.5%;
}

.col-xs-offset-4 {
  margin-left: 16.6666666667%;
}

.col-xs-offset-5 {
  margin-left: 20.8333333333%;
}

.col-xs-offset-6 {
  margin-left: 25%;
}

.col-xs-offset-7 {
  margin-left: 29.1666666667%;
}

.col-xs-offset-8 {
  margin-left: 33.3333333333%;
}

.col-xs-offset-9 {
  margin-left: 37.5%;
}

.col-xs-offset-10 {
  margin-left: 41.6666666667%;
}

.col-xs-offset-11 {
  margin-left: 45.8333333333%;
}

.col-xs-offset-12 {
  margin-left: 50%;
}

.col-xs-offset-13 {
  margin-left: 54.1666666667%;
}

.col-xs-offset-14 {
  margin-left: 58.3333333333%;
}

.col-xs-offset-15 {
  margin-left: 62.5%;
}

.col-xs-offset-16 {
  margin-left: 66.6666666667%;
}

.col-xs-offset-17 {
  margin-left: 70.8333333333%;
}

.col-xs-offset-18 {
  margin-left: 75%;
}

.col-xs-offset-19 {
  margin-left: 79.1666666667%;
}

.col-xs-offset-20 {
  margin-left: 83.3333333333%;
}

.col-xs-offset-21 {
  margin-left: 87.5%;
}

.col-xs-offset-22 {
  margin-left: 91.6666666667%;
}

.col-xs-offset-23 {
  margin-left: 95.8333333333%;
}

.col-xs-offset-24 {
  margin-left: 100%;
}

@media (min-width: 544px) {
  .col-sm-1 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 4.1666666667%;
        -ms-flex: 0 0 4.1666666667%;
            flex: 0 0 4.1666666667%;
  }
  .col-sm-2 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 8.3333333333%;
        -ms-flex: 0 0 8.3333333333%;
            flex: 0 0 8.3333333333%;
  }
  .col-sm-3 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 12.5%;
        -ms-flex: 0 0 12.5%;
            flex: 0 0 12.5%;
  }
  .col-sm-4 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 16.6666666667%;
        -ms-flex: 0 0 16.6666666667%;
            flex: 0 0 16.6666666667%;
  }
  .col-sm-5 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 20.8333333333%;
        -ms-flex: 0 0 20.8333333333%;
            flex: 0 0 20.8333333333%;
  }
  .col-sm-6 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 25%;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
  }
  .col-sm-7 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 29.1666666667%;
        -ms-flex: 0 0 29.1666666667%;
            flex: 0 0 29.1666666667%;
  }
  .col-sm-8, .Homepage .label-selector-item, .label-selector-item, .media-callout .media-left, .plank-container-grid .plank-wrapper {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 33.3333333333%;
        -ms-flex: 0 0 33.3333333333%;
            flex: 0 0 33.3333333333%;
  }
  .col-sm-9 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 37.5%;
        -ms-flex: 0 0 37.5%;
            flex: 0 0 37.5%;
  }
  .col-sm-10, .form-group > .label-wrap, .form-group > .label-wrap-wide {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 41.6666666667%;
        -ms-flex: 0 0 41.6666666667%;
            flex: 0 0 41.6666666667%;
  }
  .col-sm-11 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 45.8333333333%;
        -ms-flex: 0 0 45.8333333333%;
            flex: 0 0 45.8333333333%;
  }
  .col-sm-12 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 50%;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
  .col-sm-13 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 54.1666666667%;
        -ms-flex: 0 0 54.1666666667%;
            flex: 0 0 54.1666666667%;
  }
  .col-sm-14, .form-group > .input-wrap, .form-group > .color-picker {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 58.3333333333%;
        -ms-flex: 0 0 58.3333333333%;
            flex: 0 0 58.3333333333%;
  }
  .col-sm-15 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 62.5%;
        -ms-flex: 0 0 62.5%;
            flex: 0 0 62.5%;
  }
  .col-sm-16 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 66.6666666667%;
        -ms-flex: 0 0 66.6666666667%;
            flex: 0 0 66.6666666667%;
  }
  .col-sm-17 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 70.8333333333%;
        -ms-flex: 0 0 70.8333333333%;
            flex: 0 0 70.8333333333%;
  }
  .col-sm-18 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 75%;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
  }
  .col-sm-19 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 79.1666666667%;
        -ms-flex: 0 0 79.1666666667%;
            flex: 0 0 79.1666666667%;
  }
  .col-sm-20 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 83.3333333333%;
        -ms-flex: 0 0 83.3333333333%;
            flex: 0 0 83.3333333333%;
  }
  .col-sm-21 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 87.5%;
        -ms-flex: 0 0 87.5%;
            flex: 0 0 87.5%;
  }
  .col-sm-22 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 91.6666666667%;
        -ms-flex: 0 0 91.6666666667%;
            flex: 0 0 91.6666666667%;
  }
  .col-sm-23 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 95.8333333333%;
        -ms-flex: 0 0 95.8333333333%;
            flex: 0 0 95.8333333333%;
  }
  .col-sm-24 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 100%;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
  }
  .col-sm-pull-0 {
    right: auto;
  }
  .col-sm-pull-1 {
    right: 4.1666666667%;
  }
  .col-sm-pull-2 {
    right: 8.3333333333%;
  }
  .col-sm-pull-3 {
    right: 12.5%;
  }
  .col-sm-pull-4 {
    right: 16.6666666667%;
  }
  .col-sm-pull-5 {
    right: 20.8333333333%;
  }
  .col-sm-pull-6 {
    right: 25%;
  }
  .col-sm-pull-7 {
    right: 29.1666666667%;
  }
  .col-sm-pull-8 {
    right: 33.3333333333%;
  }
  .col-sm-pull-9 {
    right: 37.5%;
  }
  .col-sm-pull-10 {
    right: 41.6666666667%;
  }
  .col-sm-pull-11 {
    right: 45.8333333333%;
  }
  .col-sm-pull-12 {
    right: 50%;
  }
  .col-sm-pull-13 {
    right: 54.1666666667%;
  }
  .col-sm-pull-14 {
    right: 58.3333333333%;
  }
  .col-sm-pull-15 {
    right: 62.5%;
  }
  .col-sm-pull-16 {
    right: 66.6666666667%;
  }
  .col-sm-pull-17 {
    right: 70.8333333333%;
  }
  .col-sm-pull-18 {
    right: 75%;
  }
  .col-sm-pull-19 {
    right: 79.1666666667%;
  }
  .col-sm-pull-20 {
    right: 83.3333333333%;
  }
  .col-sm-pull-21 {
    right: 87.5%;
  }
  .col-sm-pull-22 {
    right: 91.6666666667%;
  }
  .col-sm-pull-23 {
    right: 95.8333333333%;
  }
  .col-sm-pull-24 {
    right: 100%;
  }
  .col-sm-push-0 {
    left: auto;
  }
  .col-sm-push-1 {
    left: 4.1666666667%;
  }
  .col-sm-push-2 {
    left: 8.3333333333%;
  }
  .col-sm-push-3 {
    left: 12.5%;
  }
  .col-sm-push-4 {
    left: 16.6666666667%;
  }
  .col-sm-push-5 {
    left: 20.8333333333%;
  }
  .col-sm-push-6 {
    left: 25%;
  }
  .col-sm-push-7 {
    left: 29.1666666667%;
  }
  .col-sm-push-8 {
    left: 33.3333333333%;
  }
  .col-sm-push-9 {
    left: 37.5%;
  }
  .col-sm-push-10 {
    left: 41.6666666667%;
  }
  .col-sm-push-11 {
    left: 45.8333333333%;
  }
  .col-sm-push-12 {
    left: 50%;
  }
  .col-sm-push-13 {
    left: 54.1666666667%;
  }
  .col-sm-push-14 {
    left: 58.3333333333%;
  }
  .col-sm-push-15 {
    left: 62.5%;
  }
  .col-sm-push-16 {
    left: 66.6666666667%;
  }
  .col-sm-push-17 {
    left: 70.8333333333%;
  }
  .col-sm-push-18 {
    left: 75%;
  }
  .col-sm-push-19 {
    left: 79.1666666667%;
  }
  .col-sm-push-20 {
    left: 83.3333333333%;
  }
  .col-sm-push-21 {
    left: 87.5%;
  }
  .col-sm-push-22 {
    left: 91.6666666667%;
  }
  .col-sm-push-23 {
    left: 95.8333333333%;
  }
  .col-sm-push-24 {
    left: 100%;
  }
  .col-sm-offset-0 {
    margin-left: 0%;
  }
  .col-sm-offset-1 {
    margin-left: 4.1666666667%;
  }
  .col-sm-offset-2 {
    margin-left: 8.3333333333%;
  }
  .col-sm-offset-3 {
    margin-left: 12.5%;
  }
  .col-sm-offset-4 {
    margin-left: 16.6666666667%;
  }
  .col-sm-offset-5 {
    margin-left: 20.8333333333%;
  }
  .col-sm-offset-6 {
    margin-left: 25%;
  }
  .col-sm-offset-7 {
    margin-left: 29.1666666667%;
  }
  .col-sm-offset-8 {
    margin-left: 33.3333333333%;
  }
  .col-sm-offset-9 {
    margin-left: 37.5%;
  }
  .col-sm-offset-10, .form-group > .input-wrap.no-label, .form-group > .no-label.color-picker {
    margin-left: 41.6666666667%;
  }
  .col-sm-offset-11 {
    margin-left: 45.8333333333%;
  }
  .col-sm-offset-12 {
    margin-left: 50%;
  }
  .col-sm-offset-13 {
    margin-left: 54.1666666667%;
  }
  .col-sm-offset-14 {
    margin-left: 58.3333333333%;
  }
  .col-sm-offset-15 {
    margin-left: 62.5%;
  }
  .col-sm-offset-16 {
    margin-left: 66.6666666667%;
  }
  .col-sm-offset-17 {
    margin-left: 70.8333333333%;
  }
  .col-sm-offset-18 {
    margin-left: 75%;
  }
  .col-sm-offset-19 {
    margin-left: 79.1666666667%;
  }
  .col-sm-offset-20 {
    margin-left: 83.3333333333%;
  }
  .col-sm-offset-21 {
    margin-left: 87.5%;
  }
  .col-sm-offset-22 {
    margin-left: 91.6666666667%;
  }
  .col-sm-offset-23 {
    margin-left: 95.8333333333%;
  }
  .col-sm-offset-24 {
    margin-left: 100%;
  }
}

@media (min-width: 768px) {
  .col-md-1 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 4.1666666667%;
        -ms-flex: 0 0 4.1666666667%;
            flex: 0 0 4.1666666667%;
  }
  .col-md-2 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 8.3333333333%;
        -ms-flex: 0 0 8.3333333333%;
            flex: 0 0 8.3333333333%;
  }
  .col-md-3 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 12.5%;
        -ms-flex: 0 0 12.5%;
            flex: 0 0 12.5%;
  }
  .col-md-4 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 16.6666666667%;
        -ms-flex: 0 0 16.6666666667%;
            flex: 0 0 16.6666666667%;
  }
  .col-md-5 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 20.8333333333%;
        -ms-flex: 0 0 20.8333333333%;
            flex: 0 0 20.8333333333%;
  }
  .col-md-6 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 25%;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
  }
  .col-md-7 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 29.1666666667%;
        -ms-flex: 0 0 29.1666666667%;
            flex: 0 0 29.1666666667%;
  }
  .col-md-8 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 33.3333333333%;
        -ms-flex: 0 0 33.3333333333%;
            flex: 0 0 33.3333333333%;
  }
  .col-md-9 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 37.5%;
        -ms-flex: 0 0 37.5%;
            flex: 0 0 37.5%;
  }
  .col-md-10 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 41.6666666667%;
        -ms-flex: 0 0 41.6666666667%;
            flex: 0 0 41.6666666667%;
  }
  .col-md-11 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 45.8333333333%;
        -ms-flex: 0 0 45.8333333333%;
            flex: 0 0 45.8333333333%;
  }
  .col-md-12 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 50%;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
  .col-md-13 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 54.1666666667%;
        -ms-flex: 0 0 54.1666666667%;
            flex: 0 0 54.1666666667%;
  }
  .col-md-14 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 58.3333333333%;
        -ms-flex: 0 0 58.3333333333%;
            flex: 0 0 58.3333333333%;
  }
  .col-md-15 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 62.5%;
        -ms-flex: 0 0 62.5%;
            flex: 0 0 62.5%;
  }
  .col-md-16 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 66.6666666667%;
        -ms-flex: 0 0 66.6666666667%;
            flex: 0 0 66.6666666667%;
  }
  .col-md-17 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 70.8333333333%;
        -ms-flex: 0 0 70.8333333333%;
            flex: 0 0 70.8333333333%;
  }
  .col-md-18 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 75%;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
  }
  .col-md-19 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 79.1666666667%;
        -ms-flex: 0 0 79.1666666667%;
            flex: 0 0 79.1666666667%;
  }
  .col-md-20 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 83.3333333333%;
        -ms-flex: 0 0 83.3333333333%;
            flex: 0 0 83.3333333333%;
  }
  .col-md-21 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 87.5%;
        -ms-flex: 0 0 87.5%;
            flex: 0 0 87.5%;
  }
  .col-md-22 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 91.6666666667%;
        -ms-flex: 0 0 91.6666666667%;
            flex: 0 0 91.6666666667%;
  }
  .col-md-23 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 95.8333333333%;
        -ms-flex: 0 0 95.8333333333%;
            flex: 0 0 95.8333333333%;
  }
  .col-md-24 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 100%;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
  }
  .col-md-pull-0 {
    right: auto;
  }
  .col-md-pull-1 {
    right: 4.1666666667%;
  }
  .col-md-pull-2 {
    right: 8.3333333333%;
  }
  .col-md-pull-3 {
    right: 12.5%;
  }
  .col-md-pull-4 {
    right: 16.6666666667%;
  }
  .col-md-pull-5 {
    right: 20.8333333333%;
  }
  .col-md-pull-6 {
    right: 25%;
  }
  .col-md-pull-7 {
    right: 29.1666666667%;
  }
  .col-md-pull-8 {
    right: 33.3333333333%;
  }
  .col-md-pull-9 {
    right: 37.5%;
  }
  .col-md-pull-10 {
    right: 41.6666666667%;
  }
  .col-md-pull-11 {
    right: 45.8333333333%;
  }
  .col-md-pull-12 {
    right: 50%;
  }
  .col-md-pull-13 {
    right: 54.1666666667%;
  }
  .col-md-pull-14 {
    right: 58.3333333333%;
  }
  .col-md-pull-15 {
    right: 62.5%;
  }
  .col-md-pull-16 {
    right: 66.6666666667%;
  }
  .col-md-pull-17 {
    right: 70.8333333333%;
  }
  .col-md-pull-18 {
    right: 75%;
  }
  .col-md-pull-19 {
    right: 79.1666666667%;
  }
  .col-md-pull-20 {
    right: 83.3333333333%;
  }
  .col-md-pull-21 {
    right: 87.5%;
  }
  .col-md-pull-22 {
    right: 91.6666666667%;
  }
  .col-md-pull-23 {
    right: 95.8333333333%;
  }
  .col-md-pull-24 {
    right: 100%;
  }
  .col-md-push-0 {
    left: auto;
  }
  .col-md-push-1 {
    left: 4.1666666667%;
  }
  .col-md-push-2 {
    left: 8.3333333333%;
  }
  .col-md-push-3 {
    left: 12.5%;
  }
  .col-md-push-4 {
    left: 16.6666666667%;
  }
  .col-md-push-5 {
    left: 20.8333333333%;
  }
  .col-md-push-6 {
    left: 25%;
  }
  .col-md-push-7 {
    left: 29.1666666667%;
  }
  .col-md-push-8 {
    left: 33.3333333333%;
  }
  .col-md-push-9 {
    left: 37.5%;
  }
  .col-md-push-10 {
    left: 41.6666666667%;
  }
  .col-md-push-11 {
    left: 45.8333333333%;
  }
  .col-md-push-12 {
    left: 50%;
  }
  .col-md-push-13 {
    left: 54.1666666667%;
  }
  .col-md-push-14 {
    left: 58.3333333333%;
  }
  .col-md-push-15 {
    left: 62.5%;
  }
  .col-md-push-16 {
    left: 66.6666666667%;
  }
  .col-md-push-17 {
    left: 70.8333333333%;
  }
  .col-md-push-18 {
    left: 75%;
  }
  .col-md-push-19 {
    left: 79.1666666667%;
  }
  .col-md-push-20 {
    left: 83.3333333333%;
  }
  .col-md-push-21 {
    left: 87.5%;
  }
  .col-md-push-22 {
    left: 91.6666666667%;
  }
  .col-md-push-23 {
    left: 95.8333333333%;
  }
  .col-md-push-24 {
    left: 100%;
  }
  .col-md-offset-0 {
    margin-left: 0%;
  }
  .col-md-offset-1 {
    margin-left: 4.1666666667%;
  }
  .col-md-offset-2 {
    margin-left: 8.3333333333%;
  }
  .col-md-offset-3 {
    margin-left: 12.5%;
  }
  .col-md-offset-4 {
    margin-left: 16.6666666667%;
  }
  .col-md-offset-5 {
    margin-left: 20.8333333333%;
  }
  .col-md-offset-6 {
    margin-left: 25%;
  }
  .col-md-offset-7 {
    margin-left: 29.1666666667%;
  }
  .col-md-offset-8 {
    margin-left: 33.3333333333%;
  }
  .col-md-offset-9 {
    margin-left: 37.5%;
  }
  .col-md-offset-10 {
    margin-left: 41.6666666667%;
  }
  .col-md-offset-11 {
    margin-left: 45.8333333333%;
  }
  .col-md-offset-12 {
    margin-left: 50%;
  }
  .col-md-offset-13 {
    margin-left: 54.1666666667%;
  }
  .col-md-offset-14 {
    margin-left: 58.3333333333%;
  }
  .col-md-offset-15 {
    margin-left: 62.5%;
  }
  .col-md-offset-16 {
    margin-left: 66.6666666667%;
  }
  .col-md-offset-17 {
    margin-left: 70.8333333333%;
  }
  .col-md-offset-18 {
    margin-left: 75%;
  }
  .col-md-offset-19 {
    margin-left: 79.1666666667%;
  }
  .col-md-offset-20 {
    margin-left: 83.3333333333%;
  }
  .col-md-offset-21 {
    margin-left: 87.5%;
  }
  .col-md-offset-22 {
    margin-left: 91.6666666667%;
  }
  .col-md-offset-23 {
    margin-left: 95.8333333333%;
  }
  .col-md-offset-24 {
    margin-left: 100%;
  }
}

@media (min-width: 992px) {
  .col-lg-1 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 4.1666666667%;
        -ms-flex: 0 0 4.1666666667%;
            flex: 0 0 4.1666666667%;
  }
  .col-lg-2 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 8.3333333333%;
        -ms-flex: 0 0 8.3333333333%;
            flex: 0 0 8.3333333333%;
  }
  .col-lg-3 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 12.5%;
        -ms-flex: 0 0 12.5%;
            flex: 0 0 12.5%;
  }
  .col-lg-4 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 16.6666666667%;
        -ms-flex: 0 0 16.6666666667%;
            flex: 0 0 16.6666666667%;
  }
  .col-lg-5 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 20.8333333333%;
        -ms-flex: 0 0 20.8333333333%;
            flex: 0 0 20.8333333333%;
  }
  .col-lg-6 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 25%;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
  }
  .col-lg-7 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 29.1666666667%;
        -ms-flex: 0 0 29.1666666667%;
            flex: 0 0 29.1666666667%;
  }
  .col-lg-8, .label-selector-item, .media-callout .media-left {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 33.3333333333%;
        -ms-flex: 0 0 33.3333333333%;
            flex: 0 0 33.3333333333%;
  }
  .col-lg-9 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 37.5%;
        -ms-flex: 0 0 37.5%;
            flex: 0 0 37.5%;
  }
  .col-lg-10 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 41.6666666667%;
        -ms-flex: 0 0 41.6666666667%;
            flex: 0 0 41.6666666667%;
  }
  .col-lg-11 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 45.8333333333%;
        -ms-flex: 0 0 45.8333333333%;
            flex: 0 0 45.8333333333%;
  }
  .col-lg-12, .Homepage .label-selector-item, .plank-container-grid .plank-wrapper {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 50%;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
  .col-lg-13 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 54.1666666667%;
        -ms-flex: 0 0 54.1666666667%;
            flex: 0 0 54.1666666667%;
  }
  .col-lg-14 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 58.3333333333%;
        -ms-flex: 0 0 58.3333333333%;
            flex: 0 0 58.3333333333%;
  }
  .col-lg-15 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 62.5%;
        -ms-flex: 0 0 62.5%;
            flex: 0 0 62.5%;
  }
  .col-lg-16 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 66.6666666667%;
        -ms-flex: 0 0 66.6666666667%;
            flex: 0 0 66.6666666667%;
  }
  .col-lg-17 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 70.8333333333%;
        -ms-flex: 0 0 70.8333333333%;
            flex: 0 0 70.8333333333%;
  }
  .col-lg-18 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 75%;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
  }
  .col-lg-19 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 79.1666666667%;
        -ms-flex: 0 0 79.1666666667%;
            flex: 0 0 79.1666666667%;
  }
  .col-lg-20 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 83.3333333333%;
        -ms-flex: 0 0 83.3333333333%;
            flex: 0 0 83.3333333333%;
  }
  .col-lg-21 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 87.5%;
        -ms-flex: 0 0 87.5%;
            flex: 0 0 87.5%;
  }
  .col-lg-22 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 91.6666666667%;
        -ms-flex: 0 0 91.6666666667%;
            flex: 0 0 91.6666666667%;
  }
  .col-lg-23 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 95.8333333333%;
        -ms-flex: 0 0 95.8333333333%;
            flex: 0 0 95.8333333333%;
  }
  .col-lg-24 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 100%;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
  }
  .col-lg-pull-0 {
    right: auto;
  }
  .col-lg-pull-1 {
    right: 4.1666666667%;
  }
  .col-lg-pull-2 {
    right: 8.3333333333%;
  }
  .col-lg-pull-3 {
    right: 12.5%;
  }
  .col-lg-pull-4 {
    right: 16.6666666667%;
  }
  .col-lg-pull-5 {
    right: 20.8333333333%;
  }
  .col-lg-pull-6 {
    right: 25%;
  }
  .col-lg-pull-7 {
    right: 29.1666666667%;
  }
  .col-lg-pull-8 {
    right: 33.3333333333%;
  }
  .col-lg-pull-9 {
    right: 37.5%;
  }
  .col-lg-pull-10 {
    right: 41.6666666667%;
  }
  .col-lg-pull-11 {
    right: 45.8333333333%;
  }
  .col-lg-pull-12 {
    right: 50%;
  }
  .col-lg-pull-13 {
    right: 54.1666666667%;
  }
  .col-lg-pull-14 {
    right: 58.3333333333%;
  }
  .col-lg-pull-15 {
    right: 62.5%;
  }
  .col-lg-pull-16 {
    right: 66.6666666667%;
  }
  .col-lg-pull-17 {
    right: 70.8333333333%;
  }
  .col-lg-pull-18 {
    right: 75%;
  }
  .col-lg-pull-19 {
    right: 79.1666666667%;
  }
  .col-lg-pull-20 {
    right: 83.3333333333%;
  }
  .col-lg-pull-21 {
    right: 87.5%;
  }
  .col-lg-pull-22 {
    right: 91.6666666667%;
  }
  .col-lg-pull-23 {
    right: 95.8333333333%;
  }
  .col-lg-pull-24 {
    right: 100%;
  }
  .col-lg-push-0 {
    left: auto;
  }
  .col-lg-push-1 {
    left: 4.1666666667%;
  }
  .col-lg-push-2 {
    left: 8.3333333333%;
  }
  .col-lg-push-3 {
    left: 12.5%;
  }
  .col-lg-push-4 {
    left: 16.6666666667%;
  }
  .col-lg-push-5 {
    left: 20.8333333333%;
  }
  .col-lg-push-6 {
    left: 25%;
  }
  .col-lg-push-7 {
    left: 29.1666666667%;
  }
  .col-lg-push-8 {
    left: 33.3333333333%;
  }
  .col-lg-push-9 {
    left: 37.5%;
  }
  .col-lg-push-10 {
    left: 41.6666666667%;
  }
  .col-lg-push-11 {
    left: 45.8333333333%;
  }
  .col-lg-push-12 {
    left: 50%;
  }
  .col-lg-push-13 {
    left: 54.1666666667%;
  }
  .col-lg-push-14 {
    left: 58.3333333333%;
  }
  .col-lg-push-15 {
    left: 62.5%;
  }
  .col-lg-push-16 {
    left: 66.6666666667%;
  }
  .col-lg-push-17 {
    left: 70.8333333333%;
  }
  .col-lg-push-18 {
    left: 75%;
  }
  .col-lg-push-19 {
    left: 79.1666666667%;
  }
  .col-lg-push-20 {
    left: 83.3333333333%;
  }
  .col-lg-push-21 {
    left: 87.5%;
  }
  .col-lg-push-22 {
    left: 91.6666666667%;
  }
  .col-lg-push-23 {
    left: 95.8333333333%;
  }
  .col-lg-push-24 {
    left: 100%;
  }
  .col-lg-offset-0 {
    margin-left: 0%;
  }
  .col-lg-offset-1 {
    margin-left: 4.1666666667%;
  }
  .col-lg-offset-2 {
    margin-left: 8.3333333333%;
  }
  .col-lg-offset-3 {
    margin-left: 12.5%;
  }
  .col-lg-offset-4 {
    margin-left: 16.6666666667%;
  }
  .col-lg-offset-5 {
    margin-left: 20.8333333333%;
  }
  .col-lg-offset-6 {
    margin-left: 25%;
  }
  .col-lg-offset-7 {
    margin-left: 29.1666666667%;
  }
  .col-lg-offset-8 {
    margin-left: 33.3333333333%;
  }
  .col-lg-offset-9 {
    margin-left: 37.5%;
  }
  .col-lg-offset-10 {
    margin-left: 41.6666666667%;
  }
  .col-lg-offset-11 {
    margin-left: 45.8333333333%;
  }
  .col-lg-offset-12 {
    margin-left: 50%;
  }
  .col-lg-offset-13 {
    margin-left: 54.1666666667%;
  }
  .col-lg-offset-14 {
    margin-left: 58.3333333333%;
  }
  .col-lg-offset-15 {
    margin-left: 62.5%;
  }
  .col-lg-offset-16 {
    margin-left: 66.6666666667%;
  }
  .col-lg-offset-17 {
    margin-left: 70.8333333333%;
  }
  .col-lg-offset-18 {
    margin-left: 75%;
  }
  .col-lg-offset-19 {
    margin-left: 79.1666666667%;
  }
  .col-lg-offset-20 {
    margin-left: 83.3333333333%;
  }
  .col-lg-offset-21 {
    margin-left: 87.5%;
  }
  .col-lg-offset-22 {
    margin-left: 91.6666666667%;
  }
  .col-lg-offset-23 {
    margin-left: 95.8333333333%;
  }
  .col-lg-offset-24 {
    margin-left: 100%;
  }
}

@media (min-width: 1200px) {
  .col-xl-1 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 4.1666666667%;
        -ms-flex: 0 0 4.1666666667%;
            flex: 0 0 4.1666666667%;
  }
  .col-xl-2 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 8.3333333333%;
        -ms-flex: 0 0 8.3333333333%;
            flex: 0 0 8.3333333333%;
  }
  .col-xl-3 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 12.5%;
        -ms-flex: 0 0 12.5%;
            flex: 0 0 12.5%;
  }
  .col-xl-4 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 16.6666666667%;
        -ms-flex: 0 0 16.6666666667%;
            flex: 0 0 16.6666666667%;
  }
  .col-xl-5 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 20.8333333333%;
        -ms-flex: 0 0 20.8333333333%;
            flex: 0 0 20.8333333333%;
  }
  .col-xl-6, .label-selector-item, .media-callout .media-left {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 25%;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
  }
  .col-xl-7 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 29.1666666667%;
        -ms-flex: 0 0 29.1666666667%;
            flex: 0 0 29.1666666667%;
  }
  .col-xl-8, .Homepage .label-selector-item, .plank-container-grid .plank-wrapper {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 33.3333333333%;
        -ms-flex: 0 0 33.3333333333%;
            flex: 0 0 33.3333333333%;
  }
  .col-xl-9 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 37.5%;
        -ms-flex: 0 0 37.5%;
            flex: 0 0 37.5%;
  }
  .col-xl-10 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 41.6666666667%;
        -ms-flex: 0 0 41.6666666667%;
            flex: 0 0 41.6666666667%;
  }
  .col-xl-11 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 45.8333333333%;
        -ms-flex: 0 0 45.8333333333%;
            flex: 0 0 45.8333333333%;
  }
  .col-xl-12 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 50%;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
  .col-xl-13 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 54.1666666667%;
        -ms-flex: 0 0 54.1666666667%;
            flex: 0 0 54.1666666667%;
  }
  .col-xl-14 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 58.3333333333%;
        -ms-flex: 0 0 58.3333333333%;
            flex: 0 0 58.3333333333%;
  }
  .col-xl-15 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 62.5%;
        -ms-flex: 0 0 62.5%;
            flex: 0 0 62.5%;
  }
  .col-xl-16 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 66.6666666667%;
        -ms-flex: 0 0 66.6666666667%;
            flex: 0 0 66.6666666667%;
  }
  .col-xl-17 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 70.8333333333%;
        -ms-flex: 0 0 70.8333333333%;
            flex: 0 0 70.8333333333%;
  }
  .col-xl-18 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 75%;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
  }
  .col-xl-19 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 79.1666666667%;
        -ms-flex: 0 0 79.1666666667%;
            flex: 0 0 79.1666666667%;
  }
  .col-xl-20 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 83.3333333333%;
        -ms-flex: 0 0 83.3333333333%;
            flex: 0 0 83.3333333333%;
  }
  .col-xl-21 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 87.5%;
        -ms-flex: 0 0 87.5%;
            flex: 0 0 87.5%;
  }
  .col-xl-22 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 91.6666666667%;
        -ms-flex: 0 0 91.6666666667%;
            flex: 0 0 91.6666666667%;
  }
  .col-xl-23 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 95.8333333333%;
        -ms-flex: 0 0 95.8333333333%;
            flex: 0 0 95.8333333333%;
  }
  .col-xl-24 {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 100%;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
  }
  .col-xl-pull-0 {
    right: auto;
  }
  .col-xl-pull-1 {
    right: 4.1666666667%;
  }
  .col-xl-pull-2 {
    right: 8.3333333333%;
  }
  .col-xl-pull-3 {
    right: 12.5%;
  }
  .col-xl-pull-4 {
    right: 16.6666666667%;
  }
  .col-xl-pull-5 {
    right: 20.8333333333%;
  }
  .col-xl-pull-6 {
    right: 25%;
  }
  .col-xl-pull-7 {
    right: 29.1666666667%;
  }
  .col-xl-pull-8 {
    right: 33.3333333333%;
  }
  .col-xl-pull-9 {
    right: 37.5%;
  }
  .col-xl-pull-10 {
    right: 41.6666666667%;
  }
  .col-xl-pull-11 {
    right: 45.8333333333%;
  }
  .col-xl-pull-12 {
    right: 50%;
  }
  .col-xl-pull-13 {
    right: 54.1666666667%;
  }
  .col-xl-pull-14 {
    right: 58.3333333333%;
  }
  .col-xl-pull-15 {
    right: 62.5%;
  }
  .col-xl-pull-16 {
    right: 66.6666666667%;
  }
  .col-xl-pull-17 {
    right: 70.8333333333%;
  }
  .col-xl-pull-18 {
    right: 75%;
  }
  .col-xl-pull-19 {
    right: 79.1666666667%;
  }
  .col-xl-pull-20 {
    right: 83.3333333333%;
  }
  .col-xl-pull-21 {
    right: 87.5%;
  }
  .col-xl-pull-22 {
    right: 91.6666666667%;
  }
  .col-xl-pull-23 {
    right: 95.8333333333%;
  }
  .col-xl-pull-24 {
    right: 100%;
  }
  .col-xl-push-0 {
    left: auto;
  }
  .col-xl-push-1 {
    left: 4.1666666667%;
  }
  .col-xl-push-2 {
    left: 8.3333333333%;
  }
  .col-xl-push-3 {
    left: 12.5%;
  }
  .col-xl-push-4 {
    left: 16.6666666667%;
  }
  .col-xl-push-5 {
    left: 20.8333333333%;
  }
  .col-xl-push-6 {
    left: 25%;
  }
  .col-xl-push-7 {
    left: 29.1666666667%;
  }
  .col-xl-push-8 {
    left: 33.3333333333%;
  }
  .col-xl-push-9 {
    left: 37.5%;
  }
  .col-xl-push-10 {
    left: 41.6666666667%;
  }
  .col-xl-push-11 {
    left: 45.8333333333%;
  }
  .col-xl-push-12 {
    left: 50%;
  }
  .col-xl-push-13 {
    left: 54.1666666667%;
  }
  .col-xl-push-14 {
    left: 58.3333333333%;
  }
  .col-xl-push-15 {
    left: 62.5%;
  }
  .col-xl-push-16 {
    left: 66.6666666667%;
  }
  .col-xl-push-17 {
    left: 70.8333333333%;
  }
  .col-xl-push-18 {
    left: 75%;
  }
  .col-xl-push-19 {
    left: 79.1666666667%;
  }
  .col-xl-push-20 {
    left: 83.3333333333%;
  }
  .col-xl-push-21 {
    left: 87.5%;
  }
  .col-xl-push-22 {
    left: 91.6666666667%;
  }
  .col-xl-push-23 {
    left: 95.8333333333%;
  }
  .col-xl-push-24 {
    left: 100%;
  }
  .col-xl-offset-0 {
    margin-left: 0%;
  }
  .col-xl-offset-1 {
    margin-left: 4.1666666667%;
  }
  .col-xl-offset-2 {
    margin-left: 8.3333333333%;
  }
  .col-xl-offset-3 {
    margin-left: 12.5%;
  }
  .col-xl-offset-4 {
    margin-left: 16.6666666667%;
  }
  .col-xl-offset-5 {
    margin-left: 20.8333333333%;
  }
  .col-xl-offset-6 {
    margin-left: 25%;
  }
  .col-xl-offset-7 {
    margin-left: 29.1666666667%;
  }
  .col-xl-offset-8 {
    margin-left: 33.3333333333%;
  }
  .col-xl-offset-9 {
    margin-left: 37.5%;
  }
  .col-xl-offset-10 {
    margin-left: 41.6666666667%;
  }
  .col-xl-offset-11 {
    margin-left: 45.8333333333%;
  }
  .col-xl-offset-12 {
    margin-left: 50%;
  }
  .col-xl-offset-13 {
    margin-left: 54.1666666667%;
  }
  .col-xl-offset-14 {
    margin-left: 58.3333333333%;
  }
  .col-xl-offset-15 {
    margin-left: 62.5%;
  }
  .col-xl-offset-16 {
    margin-left: 66.6666666667%;
  }
  .col-xl-offset-17 {
    margin-left: 70.8333333333%;
  }
  .col-xl-offset-18 {
    margin-left: 75%;
  }
  .col-xl-offset-19 {
    margin-left: 79.1666666667%;
  }
  .col-xl-offset-20 {
    margin-left: 83.3333333333%;
  }
  .col-xl-offset-21 {
    margin-left: 87.5%;
  }
  .col-xl-offset-22 {
    margin-left: 91.6666666667%;
  }
  .col-xl-offset-23 {
    margin-left: 95.8333333333%;
  }
  .col-xl-offset-24 {
    margin-left: 100%;
  }
}

.col-xs-first {
  -webkit-box-ordinal-group: 0;
  -webkit-order: -1;
      -ms-flex-order: -1;
          order: -1;
}

.col-xs-last {
  -webkit-box-ordinal-group: 2;
  -webkit-order: 1;
      -ms-flex-order: 1;
          order: 1;
}

@media (min-width: 544px) {
  .col-sm-first {
    -webkit-box-ordinal-group: 0;
    -webkit-order: -1;
        -ms-flex-order: -1;
            order: -1;
  }
  .col-sm-last {
    -webkit-box-ordinal-group: 2;
    -webkit-order: 1;
        -ms-flex-order: 1;
            order: 1;
  }
}

@media (min-width: 768px) {
  .col-md-first {
    -webkit-box-ordinal-group: 0;
    -webkit-order: -1;
        -ms-flex-order: -1;
            order: -1;
  }
  .col-md-last {
    -webkit-box-ordinal-group: 2;
    -webkit-order: 1;
        -ms-flex-order: 1;
            order: 1;
  }
}

@media (min-width: 992px) {
  .col-lg-first {
    -webkit-box-ordinal-group: 0;
    -webkit-order: -1;
        -ms-flex-order: -1;
            order: -1;
  }
  .col-lg-last {
    -webkit-box-ordinal-group: 2;
    -webkit-order: 1;
        -ms-flex-order: 1;
            order: 1;
  }
}

@media (min-width: 1200px) {
  .col-xl-first {
    -webkit-box-ordinal-group: 0;
    -webkit-order: -1;
        -ms-flex-order: -1;
            order: -1;
  }
  .col-xl-last {
    -webkit-box-ordinal-group: 2;
    -webkit-order: 1;
        -ms-flex-order: 1;
            order: 1;
  }
}

.row-xs-top {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.row-xs-center {
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.row-xs-bottom {
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
      -ms-flex-align: end;
          align-items: flex-end;
}

@media (min-width: 544px) {
  .row-sm-top {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .row-sm-center {
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .row-sm-bottom {
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
        -ms-flex-align: end;
            align-items: flex-end;
  }
}

@media (min-width: 768px) {
  .row-md-top {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .row-md-center {
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .row-md-bottom {
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
        -ms-flex-align: end;
            align-items: flex-end;
  }
}

@media (min-width: 992px) {
  .row-lg-top {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .row-lg-center {
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .row-lg-bottom {
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
        -ms-flex-align: end;
            align-items: flex-end;
  }
}

@media (min-width: 1200px) {
  .row-xl-top {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .row-xl-center {
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .row-xl-bottom {
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
        -ms-flex-align: end;
            align-items: flex-end;
  }
}

.col-xs-top {
  -webkit-align-self: flex-start;
      -ms-flex-item-align: start;
          align-self: flex-start;
}

.col-xs-center {
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
}

.col-xs-bottom {
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}

@media (min-width: 544px) {
  .col-sm-top {
    -webkit-align-self: flex-start;
        -ms-flex-item-align: start;
            align-self: flex-start;
  }
  .col-sm-center {
    -webkit-align-self: center;
        -ms-flex-item-align: center;
            align-self: center;
  }
  .col-sm-bottom {
    -webkit-align-self: flex-end;
        -ms-flex-item-align: end;
            align-self: flex-end;
  }
}

@media (min-width: 768px) {
  .col-md-top {
    -webkit-align-self: flex-start;
        -ms-flex-item-align: start;
            align-self: flex-start;
  }
  .col-md-center {
    -webkit-align-self: center;
        -ms-flex-item-align: center;
            align-self: center;
  }
  .col-md-bottom {
    -webkit-align-self: flex-end;
        -ms-flex-item-align: end;
            align-self: flex-end;
  }
}

@media (min-width: 992px) {
  .col-lg-top {
    -webkit-align-self: flex-start;
        -ms-flex-item-align: start;
            align-self: flex-start;
  }
  .col-lg-center {
    -webkit-align-self: center;
        -ms-flex-item-align: center;
            align-self: center;
  }
  .col-lg-bottom {
    -webkit-align-self: flex-end;
        -ms-flex-item-align: end;
            align-self: flex-end;
  }
}

@media (min-width: 1200px) {
  .col-xl-top {
    -webkit-align-self: flex-start;
        -ms-flex-item-align: start;
            align-self: flex-start;
  }
  .col-xl-center {
    -webkit-align-self: center;
        -ms-flex-item-align: center;
            align-self: center;
  }
  .col-xl-bottom {
    -webkit-align-self: flex-end;
        -ms-flex-item-align: end;
            align-self: flex-end;
  }
}

html {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

@-moz-viewport {
  width: device-width;
}

@-ms-viewport {
  width: device-width;
}

@-webkit-viewport {
  width: device-width;
}

@viewport {
  width: device-width;
}

html {
  font-size: 16px;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #555a62;
  background-color: #f6f9fb;
}

[tabindex="-1"]:focus {
  outline: none !important;
}

h1, h2, .analytics-widget-header .title, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: .5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #f6f9fb;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: bold;
}

dd {
  margin-bottom: .5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

a {
  color: #0291db;
  text-decoration: none;
}

a:focus, a:hover {
  color: #015f8f;
  text-decoration: underline;
}

a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
}

[role="button"] {
  cursor: pointer;
}

a,
area,
button,
[role="button"],
input,
label,
select,
summary,
textarea {
  -ms-touch-action: manipulation;
      touch-action: manipulation;
}

table {
  background-color: transparent;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #f6f9fb;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: left;
}

label {
  display: inline-block;
  margin-bottom: .5rem;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
textarea {
  margin: 0;
  line-height: inherit;
  border-radius: 0;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  line-height: inherit;
}

input[type="search"] {
  box-sizing: inherit;
  -webkit-appearance: none;
}

output {
  display: inline-block;
}

[hidden] {
  display: none !important;
}

.alert, .CasualMessage, .InfoMessage, .WarningMessage, .AlertMessage, div.Messages.Errors {
  padding: 15px;
  border: 0.0625rem solid transparent;
}

.alert > p, .CasualMessage > p, .InfoMessage > p, .WarningMessage > p, .AlertMessage > p, div.Messages.Errors > p,
.alert > ul, .CasualMessage > ul, .InfoMessage > ul, .WarningMessage > ul, .AlertMessage > ul, div.Messages.Errors > ul {
  margin-bottom: 0;
}

.alert > p + p, .CasualMessage > p + p, .InfoMessage > p + p, .WarningMessage > p + p, .AlertMessage > p + p, div.Messages.Errors > p + p {
  margin-top: 5px;
}

.alert-success, .CasualMessage {
  background-color: #6bc573;
  border-color: #6bc573;
  color: #fff;
}

.alert-success hr, .CasualMessage hr {
  border-top-color: #59be62;
}

.alert-success .alert-link, .CasualMessage .alert-link {
  color: #e6e6e6;
}

.alert-info, .InfoMessage {
  background-color: #a1b8c6;
  border-color: #a1b8c6;
  color: #fff;
}

.alert-info hr, .InfoMessage hr {
  border-top-color: #91acbc;
}

.alert-info .alert-link, .InfoMessage .alert-link {
  color: #e6e6e6;
}

.alert-warning, .WarningMessage, .AlertMessage {
  background-color: #faae42;
  border-color: #faae42;
  color: #fff;
}

.alert-warning hr, .WarningMessage hr, .AlertMessage hr {
  border-top-color: #f9a329;
}

.alert-warning .alert-link, .WarningMessage .alert-link, .AlertMessage .alert-link {
  color: #e6e6e6;
}

.alert-danger, div.Messages.Errors {
  background-color: #fa2e1f;
  border-color: #fa2e1f;
  color: #fff;
}

.alert-danger hr, div.Messages.Errors hr {
  border-top-color: #f91706;
}

.alert-danger .alert-link, div.Messages.Errors .alert-link {
  color: #e6e6e6;
}

.toolbar-stats {
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.toolbar-stats > * {
  margin-left: auto;
  margin-right: auto;
}

.toolbar-stats .toolbar-stats-slot {
  margin-top: 2.25rem;
}

@media (max-width: 991px) {
  .toolbar-stats .toolbar-stats-slot {
    margin-top: 1.125rem;
  }
}

.toolbar-stats .toolbar-stats-date-picker {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  margin-top: 1.125rem;
}

@media (max-width: 991px) {
  .toolbar-stats .toolbar-stats-date-picker {
    margin-top: 0;
  }
}

.toolbar-stats .toolbar-stats-date-picker > * {
  margin-top: 1.125rem;
  margin-left: auto;
  margin-right: auto;
}

.toolbar-stats .toolbar-stats-date-picker .filter-date {
  padding-left: 0.5625rem;
  padding-right: 0.5625rem;
  min-width: 14.25rem;
}

.toolbar-stats .toolbar-stats-date-picker .filter-date:after {
  right: 1.125rem;
}

.analytics-panel,
.analytics-panel ul,
.analytics-panel ol {
  padding-left: 0;
}

.analytics-panel li {
  list-style: none;
}

.analytics-widget {
  position: relative;
}

.analytics-widget-options {
  position: absolute;
  right: 0;
  top: 1.125rem;
}

.analytics-widget-metric .analytics-widget-options {
  right: 0.5625rem;
  top: 0;
}

.bookmarked svg {
  -webkit-transform: rotate(30deg);
      -ms-transform: rotate(30deg);
          transform: rotate(30deg);
}

.analytics-widget-move {
  margin-left: 10px;
  cursor: move;
}

.analytics-widget-move::before {
  color: #949aa2;
  display: inline-block;
  min-width: 1em;
  font-family: "vanillicon";
  font-variant: normal;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  text-align: center;
  text-decoration: inherit;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  content: "\";
}

.analytics-panel-metrics {
  display: block;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
  overflow: hidden;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  padding: 0.5625rem 0 1.125rem;
}

.analytics-panel-metrics .metric-value {
  text-align: center;
  color: #949aa2;
}

.analytics-panel-metrics .metric-title {
  text-align: center;
  color: #949aa2;
}

.analytics-panel-metrics .analytics-widget-metric {
  padding: 1.125rem 2.25rem !important;
  margin-top: 0.5625rem;
  min-width: 11.25rem;
}

.analytics-panel-metrics .analytics-widget-metric:last-child {
  border-right: 0;
}

.analytics-panel-metrics:empty {
  display: none;
}

.keen-spinner > div > div {
  width: 5px !important;
  height: 1px !important;
}

.toolbar .filter-category {
  display: none;
}

.toolbar .filter-category svg {
  -webkit-transform: scale(0.75);
      -ms-transform: scale(0.75);
          transform: scale(0.75);
}

@media (max-width: 991px) {
  .toolbar .filter-category {
    display: block;
  }
}

.table-summary-cell-position-change svg {
  margin-top: -4px;
}

.rise .table-summary-cell-position-change svg {
  color: #6bc573;
}

.fall .table-summary-cell-position-change svg {
  color: #fa2e1f;
}

.bigcrumbs {
  display: block;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
}

.bigcrumbs .crumb {
  position: relative;
  display: inline-block;
  padding: 6px 24px 6px 12px;
  line-height: 24px;
  color: #555a62;
}

.bigcrumbs .crumb:hover {
  color: #0291db;
}

.bigcrumbs .crumb:before, .bigcrumbs .crumb:after {
  position: absolute;
  display: block;
  top: -1px;
  bottom: -1px;
  right: -12px;
  width: 0;
  border: solid 12px transparent;
  border-top-width: 19px;
  border-bottom-width: 19px;
  border-left-color: #e7e8e9;
  content: '';
}

.bigcrumbs .crumb:after {
  right: -11px;
  border-left-color: #fff;
  content: '';
}

.bigcrumbs .crumb:last-child:before, .bigcrumbs .crumb:last-child:after {
  display: none;
}

input[type="submit"],
input[type="reset"],
input[type="button"],
button {
  background: none;
  border: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  padding: 0;
  -webkit-appearance: button;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

input::-moz-focus-inner,
button::-moz-focus-inner {
  border: 0;
  padding: 0;
}

.btn, .file-upload-browse, .Button {
  display: inline-block;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  vertical-align: top;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border: 0.0625rem solid transparent;
  padding: 5px 1rem;
  font-size: 0.875rem;
  line-height: 24px;
  border-radius: 0.125rem;
}

.btn, .file-upload-browse, .Button, .btn:hover, .file-upload-browse:hover, .Button:hover {
  text-decoration: none;
}

.btn:focus, .file-upload-browse:focus, .Button:focus, .btn:hover, .file-upload-browse:hover, .Button:hover {
  text-decoration: none;
}

.btn.focus, .focus.file-upload-browse, .focus.Button {
  text-decoration: none;
}

.btn:active, .file-upload-browse:active, .Button:active, .btn.active, .active.file-upload-browse,
.file-upload input:focus ~ .file-upload-browse,
.file-upload input:hover ~ .file-upload-browse, select.file-upload-browse:focus, .file-upload-browse.drop-enabled, .file-upload input:focus ~ .btn.file-upload-choose, .file-upload input:focus ~ .file-upload-choose.Button,
.file-upload input:hover ~ .btn.file-upload-choose,
.file-upload input:hover ~ .file-upload-choose.Button, select.btn:focus, select.Button:focus, .btn.drop-enabled, .drop-enabled.Button, .active.Button {
  background-image: none;
  outline: 0;
}

.btn.disabled, .disabled.file-upload-browse, .disabled.Button, .btn:disabled, .file-upload-browse:disabled, .Button:disabled {
  cursor: not-allowed;
}

.btn.disabled, .disabled.file-upload-browse, .disabled.Button, .btn.disabled:hover, .disabled.file-upload-browse:hover, .disabled.Button:hover, .btn:disabled, .file-upload-browse:disabled, .Button:disabled, .btn:disabled:hover, .file-upload-browse:disabled:hover, .Button:disabled:hover {
  color: #ededed;
  border-color: #ededed;
}

.header-block .btn, .header-block .file-upload-browse, .header-block .Button {
  padding: 2px 1em;
}

.btn + .btn, .file-upload-browse + .btn, .Button + .btn, .btn + .file-upload-browse, .file-upload-browse + .file-upload-browse, .Button + .file-upload-browse, .btn + .Button, .file-upload-browse + .Button, .Button + .Button {
  margin-left: 0.5625rem;
}

.btn-icon + .btn-icon, .bookmark + .btn-icon, .search-wrap .search-icon-wrap + .btn-icon, .btn-icon + .bookmark, .bookmark + .bookmark, .search-wrap .search-icon-wrap + .bookmark, .search-wrap .btn-icon + .search-icon-wrap, .search-wrap .bookmark + .search-icon-wrap, .search-wrap .search-icon-wrap + .search-icon-wrap {
  margin-left: 0;
}

a.btn.disabled, a.disabled.file-upload-browse, a.disabled.Button,
fieldset[disabled] a.btn,
fieldset[disabled] a.file-upload-browse,
fieldset[disabled] a.Button {
  pointer-events: none;
  cursor: not-allowed;
}

.btn-primary, .Button {
  min-width: 6rem;
  color: #0291db;
  background-color: #fff;
  border-color: #0291db;
}

.btn-primary:hover, .Button:hover {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
}

.btn-primary:focus, .Button:focus, .btn-primary.focus, .focus.Button {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
  outline: 0;
}

.btn-primary:active, .Button:active, .btn-primary.active, .file-upload input:focus ~ .btn-primary.file-upload-choose, .file-upload input:focus ~ .file-upload-choose.Button,
.file-upload input:focus ~ .btn-primary.file-upload-browse,
.file-upload input:focus ~ .file-upload-browse.Button,
.file-upload input:hover ~ .btn-primary.file-upload-choose,
.file-upload input:hover ~ .file-upload-choose.Button,
.file-upload input:hover ~ .btn-primary.file-upload-browse,
.file-upload input:hover ~ .file-upload-browse.Button, select.btn-primary:focus, select.Button:focus, .btn-primary.drop-enabled, .drop-enabled.Button, .active.Button,
.open > .btn-primary.dropdown-toggle,
.open > .dropdown-toggle.Button {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
  background-image: none;
}

.btn-primary.disabled:focus, .disabled.Button:focus, .btn-primary.disabled.focus, .disabled.focus.Button, .btn-primary:disabled:focus, .Button:disabled:focus, .btn-primary:disabled.focus, .Button:disabled.focus {
  background-color: #fff;
}

.btn-primary.disabled:hover, .disabled.Button:hover, .btn-primary:disabled:hover, .Button:disabled:hover {
  background-color: #fff;
}

.btn-secondary, .file-upload-browse {
  min-width: 6rem;
  color: #555a62;
  background-color: #fff;
  border-color: #ccc;
}

.btn-secondary:hover, .file-upload-browse:hover {
  color: #555a62;
  background-color: #ddf3ff;
  border-color: #ccc;
}

.btn-secondary:focus, .file-upload-browse:focus, .btn-secondary.focus, .focus.file-upload-browse {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
  outline: 0;
}

.btn-secondary:active, .file-upload-browse:active, .btn-secondary.active, .active.file-upload-browse,
.file-upload input:focus ~ .file-upload-browse,
.file-upload input:hover ~ .file-upload-browse, select.file-upload-browse:focus, .file-upload-browse.drop-enabled, .file-upload input:focus ~ .btn-secondary.file-upload-choose,
.file-upload input:hover ~ .btn-secondary.file-upload-choose, select.btn-secondary:focus, .btn-secondary.drop-enabled,
.open > .btn-secondary.dropdown-toggle,
.open > .dropdown-toggle.file-upload-browse {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
  background-image: none;
}

.btn-secondary.disabled:focus, .disabled.file-upload-browse:focus, .btn-secondary.disabled.focus, .disabled.focus.file-upload-browse, .btn-secondary:disabled:focus, .file-upload-browse:disabled:focus, .btn-secondary:disabled.focus, .file-upload-browse:disabled.focus {
  background-color: #fff;
}

.btn-secondary.disabled:hover, .disabled.file-upload-browse:hover, .btn-secondary:disabled:hover, .file-upload-browse:disabled:hover {
  background-color: #fff;
}

.btn-link, .btn-icon, .bookmark, .search-wrap .search-icon-wrap {
  font-weight: normal;
  color: #0291db;
  border-radius: 0;
  border-width: 0;
}

.btn-link, .btn-icon, .bookmark, .search-wrap .search-icon-wrap, .btn-link:active, .btn-icon:active, .bookmark:active, .search-wrap .search-icon-wrap:active, .btn-link.active, .active.btn-icon, .active.bookmark, .file-upload input:focus ~ .bookmark.file-upload-choose,
.file-upload input:focus ~ .bookmark.file-upload-browse,
.file-upload input:hover ~ .bookmark.file-upload-choose,
.file-upload input:hover ~ .bookmark.file-upload-browse, select.bookmark:focus, .bookmark.drop-enabled, .file-upload input:focus ~ .btn-icon.file-upload-choose, .file-upload .search-wrap input:focus ~ .file-upload-choose.search-icon-wrap, .search-wrap .file-upload input:focus ~ .file-upload-choose.search-icon-wrap,
.file-upload input:focus ~ .btn-icon.file-upload-browse,
.file-upload .search-wrap input:focus ~ .file-upload-browse.search-icon-wrap, .search-wrap
.file-upload input:focus ~ .file-upload-browse.search-icon-wrap,
.file-upload input:hover ~ .btn-icon.file-upload-choose,
.file-upload .search-wrap input:hover ~ .file-upload-choose.search-icon-wrap, .search-wrap
.file-upload input:hover ~ .file-upload-choose.search-icon-wrap,
.file-upload input:hover ~ .btn-icon.file-upload-browse,
.file-upload .search-wrap input:hover ~ .file-upload-browse.search-icon-wrap, .search-wrap
.file-upload input:hover ~ .file-upload-browse.search-icon-wrap, select.btn-icon:focus, .search-wrap select.search-icon-wrap:focus, .search-wrap .active.search-icon-wrap, .search-wrap .search-icon-wrap.drop-enabled, .btn-icon.drop-enabled, .file-upload input:focus ~ .btn-link.file-upload-choose,
.file-upload input:focus ~ .btn-link.file-upload-browse,
.file-upload input:hover ~ .btn-link.file-upload-choose,
.file-upload input:hover ~ .btn-link.file-upload-browse, select.btn-link:focus, .btn-link.drop-enabled, .btn-link:disabled, .btn-icon:disabled, .bookmark:disabled, .search-wrap .search-icon-wrap:disabled {
  background-color: transparent;
}

.btn-link, .btn-icon, .bookmark, .search-wrap .search-icon-wrap, .btn-link:focus, .btn-icon:focus, .bookmark:focus, .search-wrap .search-icon-wrap:focus, .btn-link:active, .btn-icon:active, .bookmark:active, .search-wrap .search-icon-wrap:active {
  border-color: transparent;
}

.btn-link:hover, .btn-icon:hover, .bookmark:hover, .search-wrap .search-icon-wrap:hover {
  border-color: transparent;
}

.btn-link:focus, .btn-icon:focus, .bookmark:focus, .search-wrap .search-icon-wrap:focus, .btn-link:hover, .btn-icon:hover, .bookmark:hover, .search-wrap .search-icon-wrap:hover {
  color: #015f8f;
  text-decoration: underline;
  background-color: transparent;
}

.btn-link:disabled:focus, .btn-icon:disabled:focus, .bookmark:disabled:focus, .search-wrap .search-icon-wrap:disabled:focus, .btn-link:disabled:hover, .btn-icon:disabled:hover, .bookmark:disabled:hover, .search-wrap .search-icon-wrap:disabled:hover {
  color: #ededed;
  text-decoration: none;
}

.btn-icon, .bookmark, .search-wrap .search-icon-wrap {
  padding: 0.1875rem;
  margin: 0;
  margin-left: 1rem;
  font-size: 0.75rem;
  line-height: 24px;
  border-width: 0;
  color: #555a62;
}

.btn-icon:first-child, .bookmark:first-child, .search-wrap .search-icon-wrap:first-child {
  margin-left: 0;
}

.btn-icon svg, .bookmark svg, .search-wrap .search-icon-wrap svg {
  height: 1.5rem;
  width: 1.5rem;
  vertical-align: top;
}

.btn-icon:hover, .bookmark:hover, .search-wrap .search-icon-wrap:hover {
  color: #555a62;
  opacity: 1;
}

.btn-icon.disabled, .disabled.bookmark, .search-wrap .disabled.search-icon-wrap {
  border: 0;
}

.btn-icon.btn-icon-sm svg, .bookmark svg, .search-wrap .btn-icon-sm.search-icon-wrap svg, .search-wrap .search-icon-wrap.bookmark svg {
  height: 1.125rem;
  width: 1.125rem;
}

.btn-icon-border {
  color: #0291db;
  background-color: #fff;
  border-color: #ccc;
  padding: 5px 10px;
  margin: 0 0 0 .5rem;
}

.btn-icon-border:hover {
  color: #555a62;
  background-color: #ddf3ff;
  border-color: #ccc;
}

.btn-icon-border:focus, .btn-icon-border.focus {
  color: #555a62;
  background-color: #ddf3ff;
  border-color: #ccc;
  outline: 0;
}

.btn-icon-border:active, .btn-icon-border.active, .file-upload input:focus ~ .btn-icon-border.file-upload-choose,
.file-upload input:focus ~ .btn-icon-border.file-upload-browse,
.file-upload input:hover ~ .btn-icon-border.file-upload-choose,
.file-upload input:hover ~ .btn-icon-border.file-upload-browse, select.btn-icon-border:focus, .btn-icon-border.drop-enabled,
.open > .btn-icon-border.dropdown-toggle {
  color: #555a62;
  background-color: #ddf3ff;
  border-color: #ccc;
  background-image: none;
}

.btn-icon-border.disabled:focus, .btn-icon-border.disabled.focus, .btn-icon-border:disabled:focus, .btn-icon-border:disabled.focus {
  background-color: #fff;
}

.btn-icon-border.disabled:hover, .btn-icon-border:disabled:hover {
  background-color: #fff;
}

.btn-icon-border svg {
  height: 24px;
  width: 24px;
  vertical-align: top;
}

.btn-icon-border:first-child {
  margin-left: 0;
}

.btn-group {
  font-size: 0;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
          flex-wrap: nowrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.btn-group > .btn:focus, .btn-group > .file-upload-browse:focus, .btn-group > .Button:focus, .btn-group > .btn:active, .btn-group > .file-upload-browse:active, .btn-group > .Button:active, .btn-group > .btn.active, .btn-group > .active.file-upload-browse,
.file-upload .btn-group > input:focus ~ .file-upload-browse,
.file-upload .btn-group > input:hover ~ .file-upload-browse, .btn-group > select.file-upload-browse:focus, .btn-group > .file-upload-browse.drop-enabled, .file-upload .btn-group > input:focus ~ .btn.file-upload-choose, .file-upload .btn-group > input:focus ~ .file-upload-choose.Button,
.file-upload .btn-group > input:hover ~ .btn.file-upload-choose,
.file-upload .btn-group > input:hover ~ .file-upload-choose.Button, .btn-group > select.btn:focus, .btn-group > select.Button:focus, .btn-group > .btn.drop-enabled, .btn-group > .drop-enabled.Button, .btn-group > .active.Button {
  z-index: 2;
}

.btn-group .btn, .btn-group .file-upload-browse, .btn-group .Button {
  border-radius: 0;
  margin-right: -0.0625rem;
}

.btn-group .btn:first-child, .btn-group .file-upload-browse:first-child, .btn-group .Button:first-child {
  border-top-left-radius: 0.125rem;
  border-bottom-left-radius: 0.125rem;
}

.btn-group .btn:last-child, .btn-group .file-upload-browse:last-child, .btn-group .Button:last-child {
  border-top-right-radius: 0.125rem;
  border-bottom-right-radius: 0.125rem;
}

.btn-group .btn + .btn, .btn-group .file-upload-browse + .btn, .btn-group .Button + .btn, .btn-group .btn + .file-upload-browse, .btn-group .file-upload-browse + .file-upload-browse, .btn-group .Button + .file-upload-browse, .btn-group .btn + .Button, .btn-group .file-upload-browse + .Button, .btn-group .Button + .Button,
.btn-group .btn + .btn-group,
.btn-group .file-upload-browse + .btn-group,
.btn-group .Button + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .file-upload-browse,
.btn-group .btn-group + .Button,
.btn-group .btn-group + .btn-group {
  margin-left: -0.0625rem;
}

.btn-group .btn-icon + .btn-icon, .btn-group .bookmark + .btn-icon, .btn-group .search-wrap .search-icon-wrap + .btn-icon, .search-wrap .btn-group .search-icon-wrap + .btn-icon, .btn-group .btn-icon + .bookmark, .btn-group .bookmark + .bookmark, .btn-group .search-wrap .search-icon-wrap + .bookmark, .search-wrap .btn-group .search-icon-wrap + .bookmark, .btn-group .search-wrap .btn-icon + .search-icon-wrap, .search-wrap .btn-group .btn-icon + .search-icon-wrap, .btn-group .search-wrap .bookmark + .search-icon-wrap, .search-wrap .btn-group .bookmark + .search-icon-wrap, .btn-group .search-wrap .search-icon-wrap + .search-icon-wrap, .search-wrap .btn-group .search-icon-wrap + .search-icon-wrap {
  margin-left: 0.375rem;
}

.btn-sm-rounded {
  display: inline-block;
  vertical-align: middle;
  text-transform: uppercase;
  padding: 0.125rem 0.75rem;
  font-size: 0.625rem;
  line-height: 0.875rem;
  border-radius: 0.625rem;
  color: #555a62;
  background-color: #fff;
  border-color: #ccc;
}

.btn-sm-rounded svg {
  margin-top: -0.125rem;
}

.btn-sm-rounded:hover {
  color: #555a62;
  background-color: #ddf3ff;
  border-color: #ccc;
}

.btn-sm-rounded:focus, .btn-sm-rounded.focus {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
  outline: 0;
}

.btn-sm-rounded:active, .btn-sm-rounded.active, .file-upload input:focus ~ .btn-sm-rounded.file-upload-choose,
.file-upload input:focus ~ .btn-sm-rounded.file-upload-browse,
.file-upload input:hover ~ .btn-sm-rounded.file-upload-choose,
.file-upload input:hover ~ .btn-sm-rounded.file-upload-browse, select.btn-sm-rounded:focus, .btn-sm-rounded.drop-enabled,
.open > .btn-sm-rounded.dropdown-toggle {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
  background-image: none;
}

.btn-sm-rounded.disabled:focus, .btn-sm-rounded.disabled.focus, .btn-sm-rounded:disabled:focus, .btn-sm-rounded:disabled.focus {
  background-color: #fff;
}

.btn-sm-rounded.disabled:hover, .btn-sm-rounded:disabled:hover {
  background-color: #fff;
}

.btn-navbar {
  display: inline-block;
  vertical-align: middle;
  text-transform: uppercase;
  padding: 0.125rem 0.75rem;
  font-size: 0.625rem;
  line-height: 0.875rem;
  border-radius: 0.625rem;
  color: #ceeeff;
  background-color: #0291db;
  border-color: #ceeeff;
}

.btn-navbar svg {
  margin-top: -0.125rem;
}

.btn-navbar:hover {
  color: #ceeeff;
  background-color: #1db1fd;
  border-color: #ceeeff;
}

.btn-navbar:focus, .btn-navbar.focus {
  color: #ceeeff;
  background-color: #1db1fd;
  border-color: #ceeeff;
  outline: 0;
}

.btn-navbar:active, .btn-navbar.active, .file-upload input:focus ~ .btn-navbar.file-upload-choose,
.file-upload input:focus ~ .btn-navbar.file-upload-browse,
.file-upload input:hover ~ .btn-navbar.file-upload-choose,
.file-upload input:hover ~ .btn-navbar.file-upload-browse, select.btn-navbar:focus, .btn-navbar.drop-enabled,
.open > .btn-navbar.dropdown-toggle {
  color: #ceeeff;
  background-color: #1db1fd;
  border-color: #ceeeff;
  background-image: none;
}

.btn-navbar.disabled:focus, .btn-navbar.disabled.focus, .btn-navbar:disabled:focus, .btn-navbar:disabled.focus {
  background-color: #0291db;
}

.btn-navbar.disabled:hover, .btn-navbar:disabled:hover {
  background-color: #0291db;
}

.btn-container {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.card {
  position: relative;
  display: block;
  background-color: #fff;
  border: 1px solid #e7e8e9;
  border-radius: 0.125rem;
}

.card .list-group-item {
  font-weight: 600;
  background-color: #f4f6fb;
}

.card .list-group-item:hover {
  background-color: #f8f9fc;
}

.card .list-group-item .icon, .card .list-group-item .btn-icon, .card .list-group-item .bookmark, .card .list-group-item .search-wrap .search-icon-wrap, .search-wrap .card .list-group-item .search-icon-wrap {
  position: absolute;
  right: 1rem;
  top: 1rem;
}

.card svg {
  position: relative;
  width: 0.875rem;
}

.card .media-title {
  font-weight: 600;
}

.card .media-sm .media-image-wrap {
  position: relative;
  min-width: 5.25rem;
  height: 5.25rem;
  border-radius: 0.25rem;
  overflow: hidden;
  background-color: #f6f9fb;
}

.card .media-sm .media-image-wrap::after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0.0625rem solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
  content: '';
}

.card .media-sm .media-image-wrap img {
  width: 5.25rem;
}

.card .media-sm .media-left {
  height: 5.25rem;
  padding-right: 1.125rem;
}

.card .media {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.panel-nav .card {
  display: block;
  padding: 0;
  margin-top: 0.5625rem;
  background: 0;
  border: 0;
  border-top: 1px dotted rgba(0, 0, 0, 0.0875);
}

.panel-nav .card .card-block {
  padding-left: 0;
}

.panel-nav .card .btn.btn-sm-rounded, .panel-nav .card .btn-sm-rounded.file-upload-browse, .panel-nav .card .btn-sm-rounded.Button {
  min-width: 3.75rem;
  padding: .0625rem .5rem;
  font-size: 0.5625rem;
}

.panel-nav .card .btn.btn-sm-rounded svg, .panel-nav .card .btn-sm-rounded.file-upload-browse svg, .panel-nav .card .btn-sm-rounded.Button svg {
  position: relative;
  width: 0.5625rem;
}

.card-footer {
  margin-top: -0.0625rem;
  padding: 1.40625rem;
  text-align: center;
  border-top: 0.0625rem solid #e7e8e9;
}

.card-block {
  padding: 1.125rem;
}

.drop {
  z-index: 1005;
}

.Section-Analytics .c3-axis-y path,
.Section-DashboardHome .c3-axis-y path {
  display: none;
}

.Section-Analytics .c3-axis-y .c3-axis-y-label text,
.Section-Analytics .c3-axis-y .tick text,
.Section-DashboardHome .c3-axis-y .c3-axis-y-label text,
.Section-DashboardHome .c3-axis-y .tick text {
  text-anchor: middle !important;
}

.Section-Analytics .c3-axis-y .c3-axis-y-label line,
.Section-Analytics .c3-axis-y .tick line,
.Section-DashboardHome .c3-axis-y .c3-axis-y-label line,
.Section-DashboardHome .c3-axis-y .tick line {
  display: none;
}

.Section-Analytics .c3-axis-y text,
.Section-DashboardHome .c3-axis-y text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #555a62;
}

.Section-Analytics .c3-axis-x text,
.Section-DashboardHome .c3-axis-x text {
  font-size: 0.75rem;
  -webkit-transform: translate(0, 0.5625rem);
      -ms-transform: translate(0, 0.5625rem);
          transform: translate(0, 0.5625rem);
}

.Section-Analytics .c3 svg,
.Section-DashboardHome .c3 svg {
  overflow: visible !important;
}

.Section-Analytics .c3-legend-item-tile,
.Section-DashboardHome .c3-legend-item-tile {
  rx: 100%;
  ry: 100%;
}

.Section-Analytics .c3-line,
.Section-DashboardHome .c3-line {
  stroke: rgba(25, 25, 25, 0.2) !important;
  stroke-width: 2;
}

.Section-Analytics .c3-xgrid,
.Section-Analytics .c3-ygrid,
.Section-DashboardHome .c3-xgrid,
.Section-DashboardHome .c3-ygrid {
  stroke-dasharray: 0;
  stroke: rgba(85, 89, 97, 0.12);
  stroke-width: 1;
}

.Section-Analytics .c3-tooltip .name span,
.Section-DashboardHome .c3-tooltip .name span {
  border-radius: 100%;
}

.Section-Analytics .c3-grid,
.Section-DashboardHome .c3-grid {
  stroke-width: 2;
}

.Section-Analytics .c3-regions,
.Section-DashboardHome .c3-regions {
  stroke-width: 8;
}

.Section-Analytics text,
.Section-DashboardHome text {
  font: 11px "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: 400;
  fill: #949aa2;
}

.Section-Analytics .c3-legend-item text,
.Section-DashboardHome .c3-legend-item text {
  font-size: 0.875rem;
}

.Section-Analytics .c3-axis path,
.Section-DashboardHome .c3-axis path {
  stroke-width: 1.5;
  stroke: #e7e8e9;
}

.Section-DashboardHome .c3-circle {
  r: 8;
  stroke-width: 2;
  stroke: #fff;
}

.Section-DashboardHome .c3-circle._expanded_ {
  r: 8;
  stroke-width: 2;
}

.Section-Analytics .c3-circle {
  r: 6;
  stroke-width: 2;
  stroke: #fff;
}

.Section-Analytics .c3-circle._expanded_ {
  r: 6;
  stroke-width: 2;
}

.Section-Analytics .c3-legend-item {
  translate: 0 1.125rem;
}

.ChartStatsPageViews,
.nav-stats-pageviews.active,
.file-upload input:focus ~ .nav-stats-pageviews.file-upload-choose,
.file-upload input:focus ~ .nav-stats-pageviews.file-upload-browse,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-choose,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-browse,
select.nav-stats-pageviews:focus,
.nav-stats-pageviews.drop-enabled,
.nav-stats-pageviews:hover {
  color: #f15854;
}

.ChartStatsPageViews .c3-circle,
.nav-stats-pageviews.active .c3-circle, .file-upload input:focus ~ .nav-stats-pageviews.file-upload-choose .c3-circle,
.file-upload input:focus ~ .nav-stats-pageviews.file-upload-browse .c3-circle,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-choose .c3-circle,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-browse .c3-circle, select.nav-stats-pageviews:focus .c3-circle, .nav-stats-pageviews.drop-enabled .c3-circle,
.nav-stats-pageviews:hover .c3-circle {
  fill: #f15854 !important;
}

.ChartStatsPageViews .c3-line,
.nav-stats-pageviews.active .c3-line, .file-upload input:focus ~ .nav-stats-pageviews.file-upload-choose .c3-line,
.file-upload input:focus ~ .nav-stats-pageviews.file-upload-browse .c3-line,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-choose .c3-line,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-browse .c3-line, select.nav-stats-pageviews:focus .c3-line, .nav-stats-pageviews.drop-enabled .c3-line,
.nav-stats-pageviews:hover .c3-line {
  stroke: #f15854 !important;
}

.ChartStatsPageViews .c3-area,
.nav-stats-pageviews.active .c3-area, .file-upload input:focus ~ .nav-stats-pageviews.file-upload-choose .c3-area,
.file-upload input:focus ~ .nav-stats-pageviews.file-upload-browse .c3-area,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-choose .c3-area,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-browse .c3-area, select.nav-stats-pageviews:focus .c3-area, .nav-stats-pageviews.drop-enabled .c3-area,
.nav-stats-pageviews:hover .c3-area {
  fill: #f15854 !important;
  opacity: 0.15 !important;
}

.ChartStatsPageViews .c3-xgrid-focus,
.nav-stats-pageviews.active .c3-xgrid-focus, .file-upload input:focus ~ .nav-stats-pageviews.file-upload-choose .c3-xgrid-focus,
.file-upload input:focus ~ .nav-stats-pageviews.file-upload-browse .c3-xgrid-focus,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-choose .c3-xgrid-focus,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-browse .c3-xgrid-focus, select.nav-stats-pageviews:focus .c3-xgrid-focus, .nav-stats-pageviews.drop-enabled .c3-xgrid-focus,
.nav-stats-pageviews:hover .c3-xgrid-focus {
  stroke: #f15854 !important;
}

.ChartStatsPageViews .c3-event-rect:hover,
.nav-stats-pageviews.active .c3-event-rect:hover, .file-upload input:focus ~ .nav-stats-pageviews.file-upload-choose .c3-event-rect:hover,
.file-upload input:focus ~ .nav-stats-pageviews.file-upload-browse .c3-event-rect:hover,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-choose .c3-event-rect:hover,
.file-upload input:hover ~ .nav-stats-pageviews.file-upload-browse .c3-event-rect:hover, select.nav-stats-pageviews:focus .c3-event-rect:hover, .nav-stats-pageviews.drop-enabled .c3-event-rect:hover,
.nav-stats-pageviews:hover .c3-event-rect:hover {
  fill-opacity: .1 !important;
  fill: #f15854 !important;
}

.ChartStatsComments,
.nav-stats-comments.active,
.file-upload input:focus ~ .nav-stats-comments.file-upload-choose,
.file-upload input:focus ~ .nav-stats-comments.file-upload-browse,
.file-upload input:hover ~ .nav-stats-comments.file-upload-choose,
.file-upload input:hover ~ .nav-stats-comments.file-upload-browse,
select.nav-stats-comments:focus,
.nav-stats-comments.drop-enabled,
.nav-stats-comments:hover {
  color: #60bd68;
}

.ChartStatsComments .c3-circle,
.nav-stats-comments.active .c3-circle, .file-upload input:focus ~ .nav-stats-comments.file-upload-choose .c3-circle,
.file-upload input:focus ~ .nav-stats-comments.file-upload-browse .c3-circle,
.file-upload input:hover ~ .nav-stats-comments.file-upload-choose .c3-circle,
.file-upload input:hover ~ .nav-stats-comments.file-upload-browse .c3-circle, select.nav-stats-comments:focus .c3-circle, .nav-stats-comments.drop-enabled .c3-circle,
.nav-stats-comments:hover .c3-circle {
  fill: #60bd68 !important;
}

.ChartStatsComments .c3-line,
.nav-stats-comments.active .c3-line, .file-upload input:focus ~ .nav-stats-comments.file-upload-choose .c3-line,
.file-upload input:focus ~ .nav-stats-comments.file-upload-browse .c3-line,
.file-upload input:hover ~ .nav-stats-comments.file-upload-choose .c3-line,
.file-upload input:hover ~ .nav-stats-comments.file-upload-browse .c3-line, select.nav-stats-comments:focus .c3-line, .nav-stats-comments.drop-enabled .c3-line,
.nav-stats-comments:hover .c3-line {
  stroke: #60bd68 !important;
}

.ChartStatsComments .c3-area,
.nav-stats-comments.active .c3-area, .file-upload input:focus ~ .nav-stats-comments.file-upload-choose .c3-area,
.file-upload input:focus ~ .nav-stats-comments.file-upload-browse .c3-area,
.file-upload input:hover ~ .nav-stats-comments.file-upload-choose .c3-area,
.file-upload input:hover ~ .nav-stats-comments.file-upload-browse .c3-area, select.nav-stats-comments:focus .c3-area, .nav-stats-comments.drop-enabled .c3-area,
.nav-stats-comments:hover .c3-area {
  fill: #60bd68 !important;
  opacity: 0.15 !important;
}

.ChartStatsComments .c3-xgrid-focus,
.nav-stats-comments.active .c3-xgrid-focus, .file-upload input:focus ~ .nav-stats-comments.file-upload-choose .c3-xgrid-focus,
.file-upload input:focus ~ .nav-stats-comments.file-upload-browse .c3-xgrid-focus,
.file-upload input:hover ~ .nav-stats-comments.file-upload-choose .c3-xgrid-focus,
.file-upload input:hover ~ .nav-stats-comments.file-upload-browse .c3-xgrid-focus, select.nav-stats-comments:focus .c3-xgrid-focus, .nav-stats-comments.drop-enabled .c3-xgrid-focus,
.nav-stats-comments:hover .c3-xgrid-focus {
  stroke: #60bd68 !important;
}

.ChartStatsComments .c3-event-rect:hover,
.nav-stats-comments.active .c3-event-rect:hover, .file-upload input:focus ~ .nav-stats-comments.file-upload-choose .c3-event-rect:hover,
.file-upload input:focus ~ .nav-stats-comments.file-upload-browse .c3-event-rect:hover,
.file-upload input:hover ~ .nav-stats-comments.file-upload-choose .c3-event-rect:hover,
.file-upload input:hover ~ .nav-stats-comments.file-upload-browse .c3-event-rect:hover, select.nav-stats-comments:focus .c3-event-rect:hover, .nav-stats-comments.drop-enabled .c3-event-rect:hover,
.nav-stats-comments:hover .c3-event-rect:hover {
  fill-opacity: .1 !important;
  fill: #60bd68 !important;
}

.ChartStatsDiscussions,
.nav-stats-discussions.active,
.file-upload input:focus ~ .nav-stats-discussions.file-upload-choose,
.file-upload input:focus ~ .nav-stats-discussions.file-upload-browse,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-choose,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-browse,
select.nav-stats-discussions:focus,
.nav-stats-discussions.drop-enabled,
.nav-stats-discussions:hover {
  color: #faa43a;
}

.ChartStatsDiscussions .c3-circle,
.nav-stats-discussions.active .c3-circle, .file-upload input:focus ~ .nav-stats-discussions.file-upload-choose .c3-circle,
.file-upload input:focus ~ .nav-stats-discussions.file-upload-browse .c3-circle,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-choose .c3-circle,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-browse .c3-circle, select.nav-stats-discussions:focus .c3-circle, .nav-stats-discussions.drop-enabled .c3-circle,
.nav-stats-discussions:hover .c3-circle {
  fill: #faa43a !important;
}

.ChartStatsDiscussions .c3-line,
.nav-stats-discussions.active .c3-line, .file-upload input:focus ~ .nav-stats-discussions.file-upload-choose .c3-line,
.file-upload input:focus ~ .nav-stats-discussions.file-upload-browse .c3-line,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-choose .c3-line,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-browse .c3-line, select.nav-stats-discussions:focus .c3-line, .nav-stats-discussions.drop-enabled .c3-line,
.nav-stats-discussions:hover .c3-line {
  stroke: #faa43a !important;
}

.ChartStatsDiscussions .c3-area,
.nav-stats-discussions.active .c3-area, .file-upload input:focus ~ .nav-stats-discussions.file-upload-choose .c3-area,
.file-upload input:focus ~ .nav-stats-discussions.file-upload-browse .c3-area,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-choose .c3-area,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-browse .c3-area, select.nav-stats-discussions:focus .c3-area, .nav-stats-discussions.drop-enabled .c3-area,
.nav-stats-discussions:hover .c3-area {
  fill: #faa43a !important;
  opacity: 0.15 !important;
}

.ChartStatsDiscussions .c3-xgrid-focus,
.nav-stats-discussions.active .c3-xgrid-focus, .file-upload input:focus ~ .nav-stats-discussions.file-upload-choose .c3-xgrid-focus,
.file-upload input:focus ~ .nav-stats-discussions.file-upload-browse .c3-xgrid-focus,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-choose .c3-xgrid-focus,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-browse .c3-xgrid-focus, select.nav-stats-discussions:focus .c3-xgrid-focus, .nav-stats-discussions.drop-enabled .c3-xgrid-focus,
.nav-stats-discussions:hover .c3-xgrid-focus {
  stroke: #faa43a !important;
}

.ChartStatsDiscussions .c3-event-rect:hover,
.nav-stats-discussions.active .c3-event-rect:hover, .file-upload input:focus ~ .nav-stats-discussions.file-upload-choose .c3-event-rect:hover,
.file-upload input:focus ~ .nav-stats-discussions.file-upload-browse .c3-event-rect:hover,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-choose .c3-event-rect:hover,
.file-upload input:hover ~ .nav-stats-discussions.file-upload-browse .c3-event-rect:hover, select.nav-stats-discussions:focus .c3-event-rect:hover, .nav-stats-discussions.drop-enabled .c3-event-rect:hover,
.nav-stats-discussions:hover .c3-event-rect:hover {
  fill-opacity: .1 !important;
  fill: #faa43a !important;
}

.ChartStatsUsers,
.nav-stats-users.active,
.file-upload input:focus ~ .nav-stats-users.file-upload-choose,
.file-upload input:focus ~ .nav-stats-users.file-upload-browse,
.file-upload input:hover ~ .nav-stats-users.file-upload-choose,
.file-upload input:hover ~ .nav-stats-users.file-upload-browse,
select.nav-stats-users:focus,
.nav-stats-users.drop-enabled,
.nav-stats-users:hover {
  color: #57c1c2;
}

.ChartStatsUsers .c3-circle,
.nav-stats-users.active .c3-circle, .file-upload input:focus ~ .nav-stats-users.file-upload-choose .c3-circle,
.file-upload input:focus ~ .nav-stats-users.file-upload-browse .c3-circle,
.file-upload input:hover ~ .nav-stats-users.file-upload-choose .c3-circle,
.file-upload input:hover ~ .nav-stats-users.file-upload-browse .c3-circle, select.nav-stats-users:focus .c3-circle, .nav-stats-users.drop-enabled .c3-circle,
.nav-stats-users:hover .c3-circle {
  fill: #57c1c2 !important;
}

.ChartStatsUsers .c3-line,
.nav-stats-users.active .c3-line, .file-upload input:focus ~ .nav-stats-users.file-upload-choose .c3-line,
.file-upload input:focus ~ .nav-stats-users.file-upload-browse .c3-line,
.file-upload input:hover ~ .nav-stats-users.file-upload-choose .c3-line,
.file-upload input:hover ~ .nav-stats-users.file-upload-browse .c3-line, select.nav-stats-users:focus .c3-line, .nav-stats-users.drop-enabled .c3-line,
.nav-stats-users:hover .c3-line {
  stroke: #57c1c2 !important;
}

.ChartStatsUsers .c3-area,
.nav-stats-users.active .c3-area, .file-upload input:focus ~ .nav-stats-users.file-upload-choose .c3-area,
.file-upload input:focus ~ .nav-stats-users.file-upload-browse .c3-area,
.file-upload input:hover ~ .nav-stats-users.file-upload-choose .c3-area,
.file-upload input:hover ~ .nav-stats-users.file-upload-browse .c3-area, select.nav-stats-users:focus .c3-area, .nav-stats-users.drop-enabled .c3-area,
.nav-stats-users:hover .c3-area {
  fill: #57c1c2 !important;
  opacity: 0.15 !important;
}

.ChartStatsUsers .c3-xgrid-focus,
.nav-stats-users.active .c3-xgrid-focus, .file-upload input:focus ~ .nav-stats-users.file-upload-choose .c3-xgrid-focus,
.file-upload input:focus ~ .nav-stats-users.file-upload-browse .c3-xgrid-focus,
.file-upload input:hover ~ .nav-stats-users.file-upload-choose .c3-xgrid-focus,
.file-upload input:hover ~ .nav-stats-users.file-upload-browse .c3-xgrid-focus, select.nav-stats-users:focus .c3-xgrid-focus, .nav-stats-users.drop-enabled .c3-xgrid-focus,
.nav-stats-users:hover .c3-xgrid-focus {
  stroke: #57c1c2 !important;
}

.ChartStatsUsers .c3-event-rect:hover,
.nav-stats-users.active .c3-event-rect:hover, .file-upload input:focus ~ .nav-stats-users.file-upload-choose .c3-event-rect:hover,
.file-upload input:focus ~ .nav-stats-users.file-upload-browse .c3-event-rect:hover,
.file-upload input:hover ~ .nav-stats-users.file-upload-choose .c3-event-rect:hover,
.file-upload input:hover ~ .nav-stats-users.file-upload-browse .c3-event-rect:hover, select.nav-stats-users:focus .c3-event-rect:hover, .nav-stats-users.drop-enabled .c3-event-rect:hover,
.nav-stats-users:hover .c3-event-rect:hover {
  fill-opacity: .1 !important;
  fill: #57c1c2 !important;
}

#StatsChart {
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  left: -0.84375rem;
}

.analytics-widget-chart {
  margin-bottom: 3.375rem;
}

.analytics-widget-chart .c3 {
  margin-left: -0.5625rem;
  left: -0.703125rem;
}

.analytics-widget-chart .c3-legend-item {
  width: 50px;
}

.popover-analytics {
  background-color: #fff;
  padding: 0.5625rem;
  font-weight: 600;
  border-radius: 0.25rem;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
}

.popover-single {
  font-size: 1.125rem;
}

.daterangepicker {
  position: absolute;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  width: 17.375rem;
  color: inherit;
  background: #fff;
  border-radius: 0.125rem;
  margin-top: 0.0625rem;
  top: 100px;
  left: 20px;
}

@media (min-width: 564px) {
  .daterangepicker {
    width: auto;
  }
}

.daterangepicker.dropdown-menu, .daterangepicker.ac_results {
  padding: 0.5625rem;
  max-width: none;
  z-index: 3001;
}

.daterangepicker.show-calendar .calendar {
  display: block;
}

.daterangepicker .calendar {
  display: none;
  max-width: 16.25rem;
  margin: 0.5625rem;
}

.daterangepicker .calendar.single .calendar-table {
  border: 0;
}

.daterangepicker .calendar th,
.daterangepicker .calendar td {
  white-space: nowrap;
  text-align: center;
  min-width: 32px;
}

.daterangepicker .calendar th.prev.available,
.daterangepicker .calendar td.prev.available {
  text-align: left;
}

.daterangepicker .calendar th.prev.available::before,
.daterangepicker .calendar td.prev.available::before {
  display: inline-block;
  content: url(data:image/svg+xml,%3Csvg%20width%3D%2224px%22%20height%3D%2224px%22%20viewBox%3D%220%200%2024%2024%22%20version%3D%221.1%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20xmlns%3Axlink%3D%22http%3A//www.w3.org/1999/xlink%22%3E%0A%20%20%20%20%3Cg%20id%3D%22Styles%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%20opacity%3D%220.7%22%3E%0A%20%20%20%20%20%20%20%20%3Cg%20id%3D%22Pager_Chevron_Left%22%20fill%3D%22%23555A62%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cpolygon%20id%3D%22Combined-Shape%22%20transform%3D%22translate%2812.268293%2C%2012.000000%29%20scale%28-1%2C%201%29%20translate%28-12.268293%2C%20-12.000000%29%20%22%20points%3D%2214.902439%2011.7804878%207%2019.6829268%208.31707317%2021%2016.6585366%2012.6585366%2017.5365854%2011.7804878%208.75609756%203%207.43902439%204.31707317%22%3E%3C/polygon%3E%0A%20%20%20%20%20%20%20%20%3C/g%3E%0A%20%20%20%20%3C/g%3E%0A%3C/svg%3E);
}

.daterangepicker .calendar th.next.available,
.daterangepicker .calendar td.next.available {
  text-align: right;
}

.daterangepicker .calendar th.next.available::before,
.daterangepicker .calendar td.next.available::before {
  display: inline-block;
  content: url(data:image/svg+xml,%3Csvg%20width%3D%2224px%22%20height%3D%2224px%22%20viewBox%3D%220%200%2024%2024%22%20version%3D%221.1%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20xmlns%3Axlink%3D%22http%3A//www.w3.org/1999/xlink%22%3E%0A%20%20%20%20%3Cg%20id%3D%22Styles%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%20opacity%3D%220.7%22%3E%0A%20%20%20%20%20%20%20%20%3Cg%20id%3D%22Pager_Chevron_Right%22%20fill%3D%22%23555A62%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cpolygon%20id%3D%22Combined-Shape%22%20points%3D%2214.902439%2011.7804878%207%2019.6829268%208.31707317%2021%2016.6585366%2012.6585366%2017.5365854%2011.7804878%208.75609756%203%207.43902439%204.31707317%22%3E%3C/polygon%3E%0A%20%20%20%20%20%20%20%20%3C/g%3E%0A%20%20%20%20%3C/g%3E%0A%3C/svg%3E);
}

.daterangepicker .calendar-table {
  margin-top: 0.5625rem;
  border: 0.0625rem solid #fff;
  border-radius: 0.125rem;
  background: #fff;
}

.daterangepicker table {
  width: 100%;
  margin: 0;
}

.daterangepicker td,
.daterangepicker th {
  text-align: center;
  width: 1.125rem;
  height: 1.125rem;
  border-radius: 0.125rem;
  border: 0.0625rem solid transparent;
  white-space: nowrap;
  cursor: pointer;
}

.daterangepicker td.week,
.daterangepicker th.week {
  font-size: 80%;
  color: #ccc;
}

.daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {
  background-color: #fff;
  border-color: transparent;
  color: #999;
}

.daterangepicker td.in-range {
  background-color: #eee;
  border-color: transparent;
  color: #000;
  border-radius: 0;
}

.daterangepicker td.start-date {
  border-radius: 0.125rem 0 0 0.125rem;
}

.daterangepicker td.start-date.end-date {
  border-radius: 0.125rem;
}

.daterangepicker td.end-date {
  border-radius: 0 0.125rem 0.125rem 0;
}

.daterangepicker td.active, .daterangepicker .file-upload input:focus ~ td.file-upload-choose, .file-upload .daterangepicker input:focus ~ td.file-upload-choose, .daterangepicker
.file-upload input:focus ~ td.file-upload-browse,
.file-upload .daterangepicker input:focus ~ td.file-upload-browse, .daterangepicker
.file-upload input:hover ~ td.file-upload-choose,
.file-upload .daterangepicker input:hover ~ td.file-upload-choose, .daterangepicker
.file-upload input:hover ~ td.file-upload-browse,
.file-upload .daterangepicker input:hover ~ td.file-upload-browse, .daterangepicker td.drop-enabled, .daterangepicker td.active:hover, .daterangepicker .file-upload input:focus ~ td.file-upload-choose:hover, .file-upload .daterangepicker input:focus ~ td.file-upload-choose:hover, .daterangepicker
.file-upload input:focus ~ td.file-upload-browse:hover,
.file-upload .daterangepicker input:focus ~ td.file-upload-browse:hover, .daterangepicker
.file-upload input:hover ~ td.file-upload-choose:hover,
.file-upload .daterangepicker input:hover ~ td.file-upload-choose:hover, .daterangepicker
.file-upload input:hover ~ td.file-upload-browse:hover,
.file-upload .daterangepicker input:hover ~ td.file-upload-browse:hover, .daterangepicker td.drop-enabled:hover {
  background-color: #0291db;
  border-color: transparent;
  color: #fff;
}

.daterangepicker th.month {
  width: auto;
  padding-bottom: 0.28125rem;
}

.daterangepicker td.disabled,
.daterangepicker option.disabled {
  color: #999;
  cursor: not-allowed;
  text-decoration: line-through;
}

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
  font-size: 12px;
  padding: 1px;
  height: auto;
  margin: 0;
  cursor: default;
}

.daterangepicker select.monthselect {
  margin-right: 2%;
  width: 56%;
}

.daterangepicker select.yearselect {
  width: 40%;
}

.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.secondselect, .daterangepicker select.ampmselect {
  width: 50px;
  margin-bottom: 0;
}

.daterangepicker .input-mini.active, .daterangepicker .file-upload input:focus ~ .input-mini.file-upload-choose, .file-upload .daterangepicker input:focus ~ .input-mini.file-upload-choose, .daterangepicker
.file-upload input:focus ~ .input-mini.file-upload-browse,
.file-upload .daterangepicker input:focus ~ .input-mini.file-upload-browse, .daterangepicker
.file-upload input:hover ~ .input-mini.file-upload-choose,
.file-upload .daterangepicker input:hover ~ .input-mini.file-upload-choose, .daterangepicker
.file-upload input:hover ~ .input-mini.file-upload-browse,
.file-upload .daterangepicker input:hover ~ .input-mini.file-upload-browse, .daterangepicker select.input-mini:focus, .daterangepicker .input-mini.drop-enabled {
  border: 0.0625rem solid #08c;
  border-radius: 0.125rem;
}

.daterangepicker .daterangepicker_input {
  position: relative;
}

.daterangepicker .daterangepicker_input i {
  position: absolute;
  left: 8px;
  top: 8px;
}

.daterangepicker.rtl .daterangepicker_input i {
  left: auto;
  right: 8px;
}

.daterangepicker .calendar-time {
  text-align: center;
  margin: 5px auto;
  line-height: 2.25rem;
  position: relative;
  padding-left: 28px;
}

.daterangepicker .calendar-time select.disabled {
  color: #ccc;
  cursor: not-allowed;
}

.filter-date {
  position: relative;
  min-width: 13.125rem;
}

.filter-date::after {
  position: absolute;
  display: inline-block;
  width: 18px;
  height: 18px;
  right: 9px;
  top: 9px;
  -webkit-transform: scale(1.5);
      -ms-transform: scale(1.5);
          transform: scale(1.5);
  opacity: .7;
  content: url(data:image/svg+xml,%3Csvg%20width%3D%2217px%22%20height%3D%2217px%22%20viewBox%3D%220%200%2017%2017%22%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%0A%20%20%20%20%3Cg%20id%3D%22-Styles%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%0A%20%20%20%20%20%20%20%20%3Cg%20id%3D%22Calendar%22%20fill%3D%22%23555A62%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cpath%20d%3D%22M12.7528056%2C4.25811336%20L12.7528056%2C3.5504246%20C12.7528056%2C3.2519202%2012.5010049%2C3%2012.2026422%2C3%20L11.4166944%2C3%20C11.1183317%2C3%2010.866531%2C3.2519202%2010.866531%2C3.5504246%20L10.866531%2C4.25811336%20L6.19749183%2C4.25811336%20L6.19749183%2C3.5504246%20C6.19749183%2C3.2519202%205.94569118%2C3%205.64732843%2C3%20L4.86138072%2C3%20C4.56301797%2C3%204.31121732%2C3.2519202%204.31121732%2C3.5504246%20L4.31121732%2C4.25811336%20L2.97514869%2C4.25811336%20C2.44100163%2C4.25811336%202%2C4.69785798%202%2C5.23372502%20L2%2C14.0243883%20C2%2C14.558789%202.43953595%2C15%202.97514869%2C15%20L14.0248513%2C15%20C14.5589984%2C15%2015%2C14.5602554%2015%2C14.0243883%20L15%2C5.23372502%20C15%2C4.69785798%2014.5604641%2C4.25811336%2014.026317%2C4.25811336%20L12.7528056%2C4.25811336%20Z%20M11.4487271%2C3.59703004%20L12.1720752%2C3.59703004%20L12.1720752%2C4.55661776%20L12.1720752%2C5.32691466%20L11.4487271%2C5.32691466%20L11.4487271%2C4.55661776%20L11.4487271%2C3.59703004%20L11.4487271%2C3.59703004%20Z%20M4.8934134%2C3.59703004%20L5.61676144%2C3.59703004%20L5.61676144%2C4.55661776%20L5.61676144%2C5.32691466%20L4.8934134%2C5.32691466%20L4.8934134%2C4.55661776%20L4.8934134%2C3.59703004%20L4.8934134%2C3.59703004%20Z%20M4.3272549%2C4.84056459%20L4.3272549%2C5.3589413%20C4.3272549%2C5.6574457%204.57905556%2C5.9093659%204.8774183%2C5.9093659%20L5.66336601%2C5.9093659%20C5.96172876%2C5.9093659%206.21352941%2C5.6574457%206.21352941%2C5.3589413%20L6.21352941%2C4.84056459%20L10.8665523%2C4.84056459%20L10.8665523%2C5.3589413%20C10.8665523%2C5.6574457%2011.1183529%2C5.9093659%2011.4167157%2C5.9093659%20L12.2026634%2C5.9093659%20C12.5010261%2C5.9093659%2012.7528268%2C5.6574457%2012.7528268%2C5.3589413%20L12.7528268%2C4.84056459%20L14.0423546%2C4.84056459%20C14.2621225%2C4.84056459%2014.4353284%2C5.01385271%2014.4353284%2C5.23372502%20L14.4353284%2C6.43134547%20L2.59819118%2C6.43134547%20L2.59819118%2C5.23372502%20C2.59819118%2C5.01238632%202.77137582%2C4.84056459%202.99116503%2C4.84056459%20L4.3272549%2C4.84056459%20Z%20M2.99116503%2C14.4174796%20C2.77139706%2C14.4174796%202.59819118%2C14.265274%202.59819118%2C14.0721517%20L2.59819118%2C7.01235157%20L14.4192908%2C7.01235157%20L14.4192908%2C14.0721517%20C14.4192696%2C14.265274%2014.246085%2C14.4174796%2014.026317%2C14.4174796%20L2.99116503%2C14.4174796%20Z%20M5.15266728%2C8.83119751%20C5.15266728%2C9.11607082%205.02819831%2C9.34289157%204.87192037%2C9.34289157%20L3.934126%2C9.34289157%20C3.77782693%2C9.34289157%203.65337908%2C9.11603232%203.65337908%2C8.83119751%20C3.65337908%2C8.5463242%203.77784805%2C8.31950345%203.934126%2C8.31950345%20L4.87189925%2C8.31950345%20C5.02676205%2C8.29047717%205.15266728%2C8.54636269%205.15266728%2C8.83119751%20L5.15266728%2C8.83119751%20Z%20M7.87145159%2C8.83119751%20C7.87145159%2C9.11607082%207.74698087%2C9.34289157%207.59070073%2C9.34289157%20L6.65291426%2C9.34289157%20C6.496613%2C9.34289157%206.3721634%2C9.11603232%206.3721634%2C8.83119751%20C6.3721634%2C8.5463242%206.49663412%2C8.31950345%206.65291426%2C8.31950345%20L7.59070073%2C8.31950345%20C7.73107616%2C8.29047717%207.87145159%2C8.54636269%207.87145159%2C8.83119751%20L7.87145159%2C8.83119751%20Z%20M10.575664%2C8.83119751%20C10.575664%2C9.11607082%2010.4512144%2C9.34289157%2010.2949131%2C9.34289157%20L9.35712668%2C9.34289157%20C9.20082542%2C9.34289157%209.07637582%2C9.11603232%209.07637582%2C8.83119751%20C9.07637582%2C8.5463242%209.20084654%2C8.31950345%209.35712668%2C8.31950345%20L10.2949131%2C8.31950345%20C10.449757%2C8.29047717%2010.575664%2C8.54636269%2010.575664%2C8.83119751%20L10.575664%2C8.83119751%20Z%20M13.011576%2C9.34289157%20L12.0753847%2C9.34289157%20C11.9193528%2C9.34289157%2011.7951176%2C9.11603232%2011.7951176%2C8.83119751%20C11.7951176%2C8.5463242%2011.9193739%2C8.31950345%2012.0753847%2C8.31950345%20L13.011576%2C8.31950345%20C13.1676079%2C8.29047717%2013.291843%2C8.54636269%2013.291843%2C8.83119751%20C13.291843%2C9.11607082%2013.1675868%2C9.34289157%2013.011576%2C9.34289157%20Z%20M5.16120915%2C10.765069%20C5.16120915%2C11.0505694%205.03603105%2C11.2778895%204.87886275%2C11.2778895%20L3.93572549%2C11.2778895%20C3.77853595%2C11.2778895%203.65337908%2C11.0505309%203.65337908%2C10.765069%20C3.65337908%2C10.4795686%203.77855719%2C10.2522485%203.93572549%2C10.2522485%20L4.8788415%2C10.2522485%20C5.0345866%2C10.2522485%205.16120915%2C10.4795686%205.16120915%2C10.765069%20L5.16120915%2C10.765069%20Z%20M7.87997222%2C10.765069%20C7.87997222%2C11.0505694%207.75479412%2C11.2778895%207.59762582%2C11.2778895%20L6.6545098%2C11.2778895%20C6.49732026%2C11.2778895%206.3721634%2C11.0505309%206.3721634%2C10.765069%20C6.3721634%2C10.4795686%206.4973415%2C10.2522485%206.6545098%2C10.2522485%20L7.59762582%2C10.2522485%20C7.73879902%2C10.2522485%207.87997222%2C10.4795686%207.87997222%2C10.765069%20L7.87997222%2C10.765069%20Z%20M10.5841846%2C10.765069%20C10.5841846%2C11.0505694%2010.4590278%2C11.2778895%2010.3018382%2C11.2778895%20L9.35872222%2C11.2778895%20C9.20153268%2C11.2778895%209.07637582%2C11.0505309%209.07637582%2C10.765069%20C9.07637582%2C10.4795686%209.20155392%2C10.2522485%209.35872222%2C10.2522485%20L10.3018382%2C10.2522485%20C10.4575621%2C10.2522485%2010.5841846%2C10.4795686%2010.5841846%2C10.765069%20L10.5841846%2C10.765069%20Z%20M13.3029477%2C10.765069%20C13.3029477%2C11.0505694%2013.1777696%2C11.2778895%2013.0206013%2C11.2778895%20L12.0774641%2C11.2778895%20C11.9202745%2C11.2778895%2011.7951176%2C11.0505309%2011.7951176%2C10.765069%20C11.7951176%2C10.4795686%2011.9202958%2C10.2522485%2012.0774641%2C10.2522485%20L13.0206013%2C10.2522485%20C13.1777908%2C10.2522485%2013.3029477%2C10.4795686%2013.3029477%2C10.765069%20L13.3029477%2C10.765069%20Z%20M5.16120915%2C12.7161392%20C5.16120915%2C13.0008264%205.03603105%2C13.227499%204.87886275%2C13.227499%20L3.93572549%2C13.227499%20C3.77853595%2C13.227499%203.65337908%2C13.0008264%203.65337908%2C12.7161392%20C3.65337908%2C12.431452%203.77855719%2C12.2047794%203.93572549%2C12.2047794%20L4.8788415%2C12.2047794%20C5.0345866%2C12.1731176%205.16120915%2C12.4024447%205.16120915%2C12.7161392%20L5.16120915%2C12.7161392%20Z%20M7.87997222%2C12.7161392%20C7.87997222%2C13.0008264%207.75479412%2C13.227499%207.59762582%2C13.227499%20L6.6545098%2C13.227499%20C6.49732026%2C13.227499%206.3721634%2C13.0008264%206.3721634%2C12.7161392%20C6.3721634%2C12.431452%206.4973415%2C12.2047794%206.6545098%2C12.2047794%20L7.59762582%2C12.2047794%20C7.73879902%2C12.1731176%207.87997222%2C12.4024447%207.87997222%2C12.7161392%20L7.87997222%2C12.7161392%20Z%20M10.5841846%2C12.7161392%20C10.5841846%2C13.0008264%2010.4590278%2C13.227499%2010.3018382%2C13.227499%20L9.35872222%2C13.227499%20C9.20153268%2C13.227499%209.07637582%2C13.0008264%209.07637582%2C12.7161392%20C9.07637582%2C12.431452%209.20155392%2C12.2047794%209.35872222%2C12.2047794%20L10.3018382%2C12.2047794%20C10.4575621%2C12.1731176%2010.5841846%2C12.4024447%2010.5841846%2C12.7161392%20L10.5841846%2C12.7161392%20Z%20M13.3029477%2C12.7161392%20C13.3029477%2C13.0008264%2013.1777696%2C13.227499%2013.0206013%2C13.227499%20L12.0774641%2C13.227499%20C11.9202745%2C13.227499%2011.7951176%2C13.0008264%2011.7951176%2C12.7161392%20C11.7951176%2C12.431452%2011.9202958%2C12.2047794%2012.0774641%2C12.2047794%20L13.0206013%2C12.2047794%20C13.1777908%2C12.1731176%2013.3029477%2C12.4024447%2013.3029477%2C12.7161392%20L13.3029477%2C12.7161392%20Z%22%20id%3D%22Shape-Copy-14%22%3E%3C%2Fpath%3E%0A%20%20%20%20%20%20%20%20%3C%2Fg%3E%0A%20%20%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E);
}

.filter-date .form-control, .filter-date .ac_input {
  padding-right: 36px;
}

.ranges {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
  text-align: left;
  padding: 0.5625rem;
}

@media (min-width: 564px) {
  .ranges {
    width: auto;
  }
}

.ranges ul {
  list-style: none;
  margin: 0 auto 0.5625rem;
  padding: 0;
  width: 100%;
}

.ranges li {
  padding: 0.140625rem 0;
  cursor: pointer;
}

.ranges li:hover, .ranges li.active, .ranges .file-upload input:focus ~ li.file-upload-choose, .file-upload .ranges input:focus ~ li.file-upload-choose, .ranges
.file-upload input:focus ~ li.file-upload-browse,
.file-upload .ranges input:focus ~ li.file-upload-browse, .ranges
.file-upload input:hover ~ li.file-upload-choose,
.file-upload .ranges input:hover ~ li.file-upload-choose, .ranges
.file-upload input:hover ~ li.file-upload-browse,
.file-upload .ranges input:hover ~ li.file-upload-browse, .ranges li.drop-enabled {
  color: #0291db;
}

.ranges .range_inputs {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -webkit-flex-direction: row-reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}

.ranges .range_inputs > * {
  margin-left: 0.28125rem;
}

.dropdown-toggle {
  cursor: pointer;
}

.dropdown-menu, .ac_results {
  padding: 0.375rem 0;
}

.dropdown-menu .icon, .ac_results .icon, .dropdown-menu .btn-icon, .ac_results .btn-icon, .dropdown-menu .bookmark, .ac_results .bookmark, .dropdown-menu .search-wrap .search-icon-wrap, .search-wrap .dropdown-menu .search-icon-wrap, .ac_results .search-wrap .search-icon-wrap, .search-wrap .ac_results .search-icon-wrap {
  margin-right: 0.28125rem;
}

.dropdown-menu svg.icon, .ac_results svg.icon, .dropdown-menu svg.btn-icon, .ac_results svg.btn-icon, .dropdown-menu svg.bookmark, .ac_results svg.bookmark, .dropdown-menu .search-wrap svg.search-icon-wrap, .search-wrap .dropdown-menu svg.search-icon-wrap, .ac_results .search-wrap svg.search-icon-wrap, .search-wrap .ac_results svg.search-icon-wrap {
  margin-top: -0.125rem;
}

.dropdown-menu .dropdown-item.disabled, .ac_results .dropdown-item.disabled, .dropdown-menu .ac_results li.disabled, .ac_results .dropdown-menu li.disabled, .ac_results li.disabled {
  color: #cbcdd2;
}

.dropup .dropdown-toggle::after {
  border-top: .375em solid;
  border-bottom: 0;
}

.dropdown-filter .btn, .dropdown-filter .file-upload-browse, .dropdown-filter .Button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  min-width: 120px;
}

.footer {
  -webkit-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
          flex-wrap: nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-top: 1px solid #e7e8e9;
  color: #000;
  background-color: #f6f9fb;
}

.footer .vanilla-logo {
  width: 4rem;
  height: 2rem;
}

.vanilla-logo {
  display: inline-block;
  background: 0;
  background-image: url("images/vanilla-black.svg");
  background-repeat: no-repeat;
  background-size: contain;
}

.vanilla-logo, .vanilla-logo:hover {
  font: "0/0" a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.vanilla-logo-white {
  background-image: url("images/vanilla-white.svg");
}

.footer-logo-powered-text {
  background-color: #f6f9fb;
  font-size: .425rem;
  line-height: 1.3333333333;
  text-transform: uppercase;
  text-align: center;
  opacity: 1;
}

.footer-logo-powered {
  position: relative;
  top: -.5rem;
  display: block;
}

.footer-logo {
  padding-top: .5rem;
  opacity: .3;
}

.footer-nav {
  opacity: .3;
}

.footer-nav-item {
  font-size: 0.6875rem;
  line-height: 1.3333333333;
  display: inline-block;
  padding-left: 1.125rem;
  border-left: 1px solid #e7e8e9;
}

form ul,
form ul ul,
form ul ol,
form ol,
form ol ul,
form ol ol {
  padding-left: 0;
}

form ul li,
form ol li {
  list-style: none;
}

.form-group {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  padding-top: 1rem;
  padding-bottom: 1rem;
  margin-bottom: 0;
  border-bottom: 0.0625rem dotted #e7e8e9;
}

.form-group > .no-grid {
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}

.form-group > .label-wrap-wide {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.form-group .input-wrap-right {
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}

@media (max-width: 543px) {
  .form-group .input-wrap, .form-group .color-picker {
    margin-top: 0.28125rem;
  }
}

label,
.label {
  font-weight: 600;
  margin-bottom: 0;
}

.input-wrap:not(.inline) label, .color-picker:not(.inline) label {
  display: block;
}

.input-wrap.inline label, .inline.color-picker label {
  margin-right: 0.28125rem;
}

.form-control, .ac_input {
  line-height: 1.5;
  padding-top: 0.40625rem;
  padding-bottom: 0.40625rem;
  border: 0.0625rem solid #ccc;
}

.form-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  padding: 1.125rem 0;
}

.label-wrap {
  min-width: 0;
  overflow: hidden;
}

.label-wrap img {
  max-width: 50%;
  max-height: 11.25rem;
}

.input-wrap > .form-group, .color-picker > .form-group {
  border: 0;
  padding-top: 0;
  padding-bottom: 1.125rem;
  margin-left: 0;
  margin-right: 0;
}

.input-wrap > .form-group:last-child, .color-picker > .form-group:last-child {
  padding-bottom: 0;
}

.input-wrap > .form-group > *, .color-picker > .form-group > * {
  padding-left: 0;
}

.input-wrap-multiple {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.input-wrap-multiple > *:not(:first-child) {
  margin-left: 0.5625rem;
}

.input-wrap-multiple.input-wrap-1_3 > * {
  width: 66.6666666667%;
}

.input-wrap-multiple.input-wrap-1_3 > *:first-child {
  width: 33.3333333333%;
}

.input-wrap-multiple.input-wrap-2_3 > * {
  width: 33.3333333333%;
}

.input-wrap-multiple.input-wrap-2_3 > *:first-child {
  width: 66.6666666667%;
}

.toggle-wrap {
  display: block;
  position: relative;
  width: 4.5rem;
  height: 2.25rem;
}

.toggle-wrap .toggle-well, .toggle-wrap input[type="checkbox"] + label::before {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 4.5rem;
  height: 2.25rem;
  background-color: #fff;
  border-radius: 1.125rem;
  border: 0.0625rem solid #949aa2;
  -webkit-transition: background .4s;
          transition: background .4s;
  cursor: pointer;
}

.toggle-wrap .toggle-slider, .toggle-wrap input[type="checkbox"] + label::after {
  display: block;
  position: absolute;
  top: 0.1875rem;
  left: 0.1875rem;
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 1.0625rem;
  border: 0.0625rem solid #949aa2;
  background-color: #fff;
  outline: 0;
  -webkit-transition: left .4s;
          transition: left .4s;
  cursor: pointer;
}

.toggle-wrap .toggle-well-on, .toggle-wrap.toggle-wrap-on .toggle-well, .toggle-wrap.toggle-wrap-on input[type="checkbox"] + label::before, .toggle-wrap .toggle-wrap-sm.toggle-wrap-on .toggle-well, .toggle-wrap-sm.toggle-wrap-on .toggle-wrap .toggle-well, .toggle-wrap-sm.toggle-wrap-on .toggle-wrap input[type="checkbox"] + label::before, .toggle-wrap .toggle-wrap-sm.toggle-wrap-on input[type="checkbox"] + label::before, .toggle-wrap input[type="checkbox"]:checked + label::before {
  background-color: #0291db;
  border: 0;
}

.toggle-wrap .toggle-slider-on, .toggle-wrap.toggle-wrap-on .toggle-slider, .toggle-wrap.toggle-wrap-on input[type="checkbox"] + label::after, .toggle-wrap .toggle-wrap-sm.toggle-wrap-on .toggle-slider, .toggle-wrap-sm.toggle-wrap-on .toggle-wrap .toggle-slider, .toggle-wrap-sm.toggle-wrap-on .toggle-wrap input[type="checkbox"] + label::after, .toggle-wrap .toggle-wrap-sm.toggle-wrap-on input[type="checkbox"] + label::after, .toggle-wrap input[type="checkbox"]:checked + label::after {
  left: 2.4375rem;
  border: 0;
}

.toggle-wrap .toggle-slider-active, .toggle-wrap .InProgress .toggle-slider, .toggle-wrap .InProgress input[type="checkbox"] + label::after, .toggle-wrap .toggle-wrap-active .toggle-slider, .toggle-wrap .toggle-wrap-active input[type="checkbox"] + label::after, .toggle-wrap-sm .InProgress .toggle-wrap .toggle-slider, .toggle-wrap-sm .InProgress .toggle-wrap input[type="checkbox"] + label::after,
.toggle-wrap-sm .toggle-wrap-active .toggle-wrap .toggle-slider,
.toggle-wrap-sm .toggle-wrap-active .toggle-wrap input[type="checkbox"] + label::after {
  left: 1.3125rem;
  -webkit-transition: left .4s;
          transition: left .4s;
}

.toggle-box-label-wrap {
  padding-left: 1.125rem;
}

.toggle-wrap-sm, .table-data .toggle-wrap {
  display: block;
  position: relative;
  width: 3rem;
  height: 1.5rem;
}

.toggle-wrap-sm .toggle-well, .table-data .toggle-wrap .toggle-well, .toggle-wrap-sm .toggle-wrap input[type="checkbox"] + label::before, .toggle-wrap .toggle-wrap-sm input[type="checkbox"] + label::before, .table-data .toggle-wrap input[type="checkbox"] + label::before {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 3rem;
  height: 1.5rem;
  background-color: #fff;
  border-radius: 0.75rem;
  border: 0.0625rem solid #949aa2;
  -webkit-transition: background .4s;
          transition: background .4s;
  cursor: pointer;
}

.toggle-wrap-sm .toggle-slider, .table-data .toggle-wrap .toggle-slider, .toggle-wrap-sm .toggle-wrap input[type="checkbox"] + label::after, .toggle-wrap .toggle-wrap-sm input[type="checkbox"] + label::after, .table-data .toggle-wrap input[type="checkbox"] + label::after {
  display: block;
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 0.71875rem;
  border: 0.0625rem solid #949aa2;
  background-color: #fff;
  outline: 0;
  -webkit-transition: left .4s;
          transition: left .4s;
  cursor: pointer;
}

.toggle-wrap-sm .toggle-well-on, .table-data .toggle-wrap .toggle-well-on, .toggle-wrap-sm .toggle-wrap.toggle-wrap-on .toggle-well, .toggle-wrap.toggle-wrap-on .toggle-wrap-sm .toggle-well, .toggle-wrap-sm .toggle-wrap.toggle-wrap-on input[type="checkbox"] + label::before, .toggle-wrap.toggle-wrap-on .toggle-wrap-sm input[type="checkbox"] + label::before, .toggle-wrap-sm.toggle-wrap-on .toggle-well, .table-data .toggle-wrap-on.toggle-wrap .toggle-well, .toggle-wrap-sm.toggle-wrap-on .toggle-wrap input[type="checkbox"] + label::before, .toggle-wrap .toggle-wrap-sm.toggle-wrap-on input[type="checkbox"] + label::before, .table-data .toggle-wrap-on.toggle-wrap input[type="checkbox"] + label::before, .toggle-wrap-sm .toggle-wrap input[type="checkbox"]:checked + label::before, .toggle-wrap .toggle-wrap-sm input[type="checkbox"]:checked + label::before, .table-data .toggle-wrap input[type="checkbox"]:checked + label::before {
  background-color: #0291db;
  border: 0;
}

.toggle-wrap-sm .toggle-slider-on, .table-data .toggle-wrap .toggle-slider-on, .toggle-wrap-sm .toggle-wrap.toggle-wrap-on .toggle-slider, .toggle-wrap.toggle-wrap-on .toggle-wrap-sm .toggle-slider, .toggle-wrap-sm .toggle-wrap.toggle-wrap-on input[type="checkbox"] + label::after, .toggle-wrap.toggle-wrap-on .toggle-wrap-sm input[type="checkbox"] + label::after, .toggle-wrap-sm.toggle-wrap-on .toggle-slider, .table-data .toggle-wrap-on.toggle-wrap .toggle-slider, .toggle-wrap-sm.toggle-wrap-on .toggle-wrap input[type="checkbox"] + label::after, .toggle-wrap .toggle-wrap-sm.toggle-wrap-on input[type="checkbox"] + label::after, .table-data .toggle-wrap-on.toggle-wrap input[type="checkbox"] + label::after, .toggle-wrap-sm .toggle-wrap input[type="checkbox"]:checked + label::after, .toggle-wrap .toggle-wrap-sm input[type="checkbox"]:checked + label::after, .table-data .toggle-wrap input[type="checkbox"]:checked + label::after {
  left: 1.625rem;
  border: 0;
}

.toggle-wrap-sm .toggle-slider-active, .table-data .toggle-wrap .toggle-slider-active, .toggle-wrap .InProgress .toggle-wrap-sm .toggle-slider, .toggle-wrap-sm .toggle-wrap .InProgress input[type="checkbox"] + label::after, .toggle-wrap .InProgress .toggle-wrap-sm input[type="checkbox"] + label::after,
.toggle-wrap .toggle-wrap-active .toggle-wrap-sm .toggle-slider, .toggle-wrap-sm .toggle-wrap .toggle-wrap-active input[type="checkbox"] + label::after, .toggle-wrap .toggle-wrap-active .toggle-wrap-sm input[type="checkbox"] + label::after, .toggle-wrap-sm .InProgress .toggle-slider, .table-data .toggle-wrap .InProgress .toggle-slider, .toggle-wrap-sm .InProgress .toggle-wrap input[type="checkbox"] + label::after, .toggle-wrap .toggle-wrap-sm .InProgress input[type="checkbox"] + label::after, .table-data .toggle-wrap .InProgress input[type="checkbox"] + label::after, .toggle-wrap-sm .toggle-wrap-active .toggle-slider, .table-data .toggle-wrap .toggle-wrap-active .toggle-slider, .toggle-wrap-sm .toggle-wrap-active .toggle-wrap input[type="checkbox"] + label::after, .toggle-wrap .toggle-wrap-sm .toggle-wrap-active input[type="checkbox"] + label::after, .table-data .toggle-wrap .toggle-wrap-active input[type="checkbox"] + label::after {
  left: 0.875rem;
  -webkit-transition: left .4s;
          transition: left .4s;
}

.toggle-wrap label {
  display: inline;
}

.toggle-wrap input[type="checkbox"] {
  position: absolute;
  margin-left: -9999px;
  visibility: hidden;
}

.toggle-wrap input[type="checkbox"] + label::before, .toggle-wrap input[type="checkbox"] + label::after {
  content: '';
}

.color-picker {
  position: relative;
}

.color-picker-color {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  padding: 0;
}

.color-picker-color:disabled {
  opacity: 0;
}

.color-picker-preview {
  position: absolute;
  top: 0.28125rem;
  right: 0.34375rem;
  width: 2.5rem;
  height: 1.71875rem;
  background: #000;
  border: 0.0625rem solid #e7e8e9;
  cursor: pointer;
}

.file-upload {
  position: relative;
  display: inline-block;
  width: 100%;
  font-weight: normal;
  cursor: pointer;
}

.file-upload input {
  margin: 0;
  filter: alpha(opacity=0);
  opacity: 0;
}

.file-upload input:disabled, .file-upload input.disabled {
  opacity: 0;
}

.file-upload-choose {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  padding: 0.40625rem 0.84375rem;
  color: #555a62;
  border: 0.0625rem solid #ccc;
  border-radius: 0.125rem;
}

.file-upload-browse {
  position: absolute;
  top: 0;
  right: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.file-upload input:focus ~ .file-upload-choose,
.file-upload input:focus ~ .file-upload-browse,
.file-upload input:hover ~ .file-upload-choose,
.file-upload input:hover ~ .file-upload-browse {
  border-color: #66afe9;
  outline: none;
}

.file-upload input:disabled ~ .file-upload-choose,
.file-upload input.disabled ~ .file-upload-choose {
  background-color: #f4f6fb;
}

select,
select.form-control,
select.ac_input {
  display: inline-block;
  height: 2.25rem;
  max-width: 100%;
  padding-right: 2rem;
  padding-left: .75rem;
  color: #555a62;
  vertical-align: middle;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: right .75rem center;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAUCAMAAACzvE1FAAAADFBMVEUzMzMzMzMzMzMzMzMKAG/3AAAAA3RSTlMAf4C/aSLHAAAAPElEQVR42q3NMQ4AIAgEQTn//2cLdRKppSGzBYwzVXvznNWs8C58CiussPJj8h6NwgorrKRdTvuV9v16Afn0AYFOB7aYAAAAAElFTkSuQmCC);
  background-image: none \9;
  background-size: 8px 10px;
  border: 0.0625rem solid #ccc;
  border-radius: 0.125rem;
  -moz-appearance: none;
  -webkit-appearance: none;
}

select::-ms-expand,
select.form-control::-ms-expand,
select.ac_input::-ms-expand {
  opacity: 0;
}

select:focus,
select.form-control:focus,
select.ac_input:focus {
  border-color: #66afe9;
  outline: none;
}

select + select {
  margin-left: 0.5625rem;
}

.label-selector {
  margin-bottom: 0.5625rem;
}

.label-selector,
.label-selector ul,
.label-selector ol {
  padding-left: 0;
}

.label-selector li {
  list-style: none;
}

.label-selector label {
  padding-left: 0;
  cursor: pointer;
}

.label-selector input[type=radio],
.label-selector input[type=checkbox] {
  position: absolute;
  opacity: 0;
}

.label-selector .active, .label-selector .file-upload input:focus ~ .file-upload-choose, .file-upload .label-selector input:focus ~ .file-upload-choose, .label-selector
.file-upload input:focus ~ .file-upload-browse,
.file-upload .label-selector input:focus ~ .file-upload-browse, .label-selector
.file-upload input:hover ~ .file-upload-choose,
.file-upload .label-selector input:hover ~ .file-upload-choose, .label-selector
.file-upload input:hover ~ .file-upload-browse,
.file-upload .label-selector input:hover ~ .file-upload-browse, .label-selector select:focus, .label-selector .drop-enabled,
.label-selector input[type=radio]:checked ~ label,
.label-selector input[type=radio]:checked ~ label,
.label-selector input[type=checkbox]:checked ~ label,
.label-selector input[type=checkbox]:checked ~ label {
  cursor: default;
}

.label-selector .active .overlay, .label-selector .file-upload input:focus ~ .file-upload-choose .overlay, .file-upload .label-selector input:focus ~ .file-upload-choose .overlay, .label-selector
.file-upload input:focus ~ .file-upload-browse .overlay,
.file-upload .label-selector input:focus ~ .file-upload-browse .overlay, .label-selector
.file-upload input:hover ~ .file-upload-choose .overlay,
.file-upload .label-selector input:hover ~ .file-upload-choose .overlay, .label-selector
.file-upload input:hover ~ .file-upload-browse .overlay,
.file-upload .label-selector input:hover ~ .file-upload-browse .overlay, .label-selector select:focus .overlay, .label-selector .drop-enabled .overlay,
.label-selector input[type=radio]:checked ~ label .overlay,
.label-selector input[type=radio]:checked ~ label .overlay,
.label-selector input[type=checkbox]:checked ~ label .overlay,
.label-selector input[type=checkbox]:checked ~ label .overlay {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.7);
}

.label-selector .active .overlay > .buttons, .label-selector .file-upload input:focus ~ .file-upload-choose .overlay > .buttons, .file-upload .label-selector input:focus ~ .file-upload-choose .overlay > .buttons, .label-selector
.file-upload input:focus ~ .file-upload-browse .overlay > .buttons,
.file-upload .label-selector input:focus ~ .file-upload-browse .overlay > .buttons, .label-selector
.file-upload input:hover ~ .file-upload-choose .overlay > .buttons,
.file-upload .label-selector input:hover ~ .file-upload-choose .overlay > .buttons, .label-selector
.file-upload input:hover ~ .file-upload-browse .overlay > .buttons,
.file-upload .label-selector input:hover ~ .file-upload-browse .overlay > .buttons, .label-selector select:focus .overlay > .buttons, .label-selector .drop-enabled .overlay > .buttons,
.label-selector input[type=radio]:checked ~ label .overlay > .buttons,
.label-selector input[type=radio]:checked ~ label .overlay > .buttons,
.label-selector input[type=checkbox]:checked ~ label .overlay > .buttons,
.label-selector input[type=checkbox]:checked ~ label .overlay > .buttons {
  display: none;
}

.label-selector .active .overlay > .selected, .label-selector .file-upload input:focus ~ .file-upload-choose .overlay > .selected, .file-upload .label-selector input:focus ~ .file-upload-choose .overlay > .selected, .label-selector
.file-upload input:focus ~ .file-upload-browse .overlay > .selected,
.file-upload .label-selector input:focus ~ .file-upload-browse .overlay > .selected, .label-selector
.file-upload input:hover ~ .file-upload-choose .overlay > .selected,
.file-upload .label-selector input:hover ~ .file-upload-choose .overlay > .selected, .label-selector
.file-upload input:hover ~ .file-upload-browse .overlay > .selected,
.file-upload .label-selector input:hover ~ .file-upload-browse .overlay > .selected, .label-selector select:focus .overlay > .selected, .label-selector .drop-enabled .overlay > .selected,
.label-selector input[type=radio]:checked ~ label .overlay > .selected,
.label-selector input[type=radio]:checked ~ label .overlay > .selected,
.label-selector input[type=checkbox]:checked ~ label .overlay > .selected,
.label-selector input[type=checkbox]:checked ~ label .overlay > .selected {
  display: block;
}

.label-selector .active .label-selector-image, .label-selector .file-upload input:focus ~ .file-upload-choose .label-selector-image, .file-upload .label-selector input:focus ~ .file-upload-choose .label-selector-image, .label-selector
.file-upload input:focus ~ .file-upload-browse .label-selector-image,
.file-upload .label-selector input:focus ~ .file-upload-browse .label-selector-image, .label-selector
.file-upload input:hover ~ .file-upload-choose .label-selector-image,
.file-upload .label-selector input:hover ~ .file-upload-choose .label-selector-image, .label-selector
.file-upload input:hover ~ .file-upload-browse .label-selector-image,
.file-upload .label-selector input:hover ~ .file-upload-browse .label-selector-image, .label-selector select:focus .label-selector-image, .label-selector .drop-enabled .label-selector-image,
.label-selector input[type=radio]:checked ~ label .label-selector-image,
.label-selector input[type=radio]:checked ~ label .label-selector-image,
.label-selector input[type=checkbox]:checked ~ label .label-selector-image,
.label-selector input[type=checkbox]:checked ~ label .label-selector-image {
  opacity: .5;
}

.label-selector .label-selector-image {
  display: block;
  width: 100%;
}

.label-selector .title {
  text-align: center;
  font-weight: 600;
}

.label-selector .title a {
  color: #555a62;
}

.label-selector .title a:hover {
  color: #0291db;
}

.label-selector .title a:hover {
  text-decoration: none;
}

.label-selector .image-wrap {
  position: relative;
  display: block;
  margin-bottom: 0.5625rem;
}

.label-selector .image-wrap::after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0.0625rem solid rgba(0, 0, 0, 0.125);
  border-radius: 0;
  content: '';
}

.label-selector .image-wrap:hover .overlay {
  height: 100%;
  opacity: .9;
}

.label-selector .selected {
  display: none;
  color: #6bc573;
}

.label-selector .selected svg {
  display: block;
  width: 84px;
}

.label-selector .overlay {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 20;
  background-color: #0291db;
  opacity: 0;
  overflow: hidden;
  -webkit-transition: all .1s;
          transition: all .1s;
  text-align: center;
}

.label-selector .buttons {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  margin-left: 0;
  margin-right: 0;
  padding: 0.5625rem;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.label-selector .buttons > .btn, .label-selector .buttons > .file-upload-browse, .label-selector .buttons > .Button {
  display: block;
  margin: 0.1875rem 0.28125rem;
  max-height: 2.25rem;
  min-width: 6rem;
}

.label-selector .label-selector-item {
  position: relative;
}

.label-selector .label-selector-corner-link {
  position: absolute;
  display: block;
  width: 1rem;
  top: 0.5625rem;
  right: 0.5625rem;
}

.label-selector .label-selector-corner-link svg {
  display: block;
}

.label-selector .label-selector-corner-link a {
  color: #fff;
}

.label-selector .label-selector-corner-link a:hover {
  color: #04a8fd;
}

.theme-wrap {
  position: relative;
  margin-bottom: 0.5625rem;
}

.theme-wrap .image-wrap {
  position: absolute;
  top: 0;
  left: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.theme-wrap .image-wrap::after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0.0625rem solid rgba(0, 0, 0, 0.125);
  border-radius: 0;
  content: '';
}

.theme-wrap .image-wrap .label-selector-image {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
  min-width: 100%;
  min-height: 100%;
}

.mobile-theme-wrap {
  position: relative;
  overflow: hidden;
  margin-bottom: 0.5625rem;
  color: rgba(85, 90, 98, 0.27);
}

.mobile-theme-wrap .mobile-frame {
  position: relative;
  bottom: 0;
}

.mobile-theme-wrap svg {
  max-width: 100%;
  display: block;
}

.mobile-theme-wrap .image-wrap {
  position: absolute;
  bottom: .7%;
  left: 8%;
  width: 84%;
  height: 86%;
  margin-bottom: 0;
  overflow: hidden;
}

.mobile-theme-wrap .image-wrap::after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0.0625rem solid rgba(0, 0, 0, 0.125);
  border-radius: 0;
  content: '';
}

.mobile-theme-wrap .image-wrap::after {
  border-bottom: 0;
}

.label-selector-item {
  padding: 1.125rem 1.125rem 0.5625rem;
}

.btn-overlay {
  color: #fff;
  background-color: transparent;
  border-color: #fff;
}

.btn-overlay:hover {
  color: #fff;
  background-color: #04a8fd;
  border-color: #fff;
}

.btn-overlay:focus, .btn-overlay.focus {
  color: #fff;
  background-color: #04a8fd;
  border-color: #fff;
  outline: 0;
}

.btn-overlay:active, .btn-overlay.active, .file-upload input:focus ~ .btn-overlay.file-upload-choose,
.file-upload input:focus ~ .btn-overlay.file-upload-browse,
.file-upload input:hover ~ .btn-overlay.file-upload-choose,
.file-upload input:hover ~ .btn-overlay.file-upload-browse, select.btn-overlay:focus, .btn-overlay.drop-enabled,
.open > .btn-overlay.dropdown-toggle {
  color: #fff;
  background-color: #04a8fd;
  border-color: #fff;
  background-image: none;
}

.btn-overlay.disabled:focus, .btn-overlay.disabled.focus, .btn-overlay:disabled:focus, .btn-overlay:disabled.focus {
  background-color: transparent;
}

.btn-overlay.disabled:hover, .btn-overlay:disabled:hover {
  background-color: transparent;
}

.search-wrap {
  position: relative;
}

.search-wrap .form-control, .search-wrap .ac_input {
  border-radius: 1.125rem;
  padding-left: 2.8125rem;
  padding-right: 2.8125rem;
}

.search-wrap .form-control[placeholder], .search-wrap [placeholder].ac_input {
  text-overflow: ellipsis;
}

.search-wrap .search-icon-wrap {
  padding: 0.375rem 0.75rem;
  position: absolute;
  top: 0;
}

.search-wrap .search-icon-wrap:first-child {
  margin-left: 0;
}

.search-wrap .search-icon-clear-wrap {
  right: 0;
}

.search-wrap .search-icon-search-wrap {
  left: 0;
}

.search-wrap .search-info {
  margin-top: 0.28125rem;
  font-style: italic;
}

.search-submit {
  position: absolute;
  left: -9999px;
}

.textbox-suffix {
  position: relative;
}

.textbox-suffix input {
  padding-right: 1.6875rem;
  width: 6rem;
  text-align: right;
}

.textbox-suffix::after {
  position: absolute;
  display: block;
  top: 0;
  right: 0;
  height: 2.25rem;
  padding-top: 0.75rem;
  padding-right: 0.5625rem;
  font-size: 0.75rem;
  line-height: 1;
  color: #949aa2;
  content: attr(data-suffix);
}

.text-input-button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.text-input-button input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.text-input-button .btn, .text-input-button .file-upload-browse, .text-input-button .Button {
  margin-left: -0.0625rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.text-input-button .label-wrap {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
  display: block;
  margin-right: 0.5625rem;
}

.text-input-button .label-wrap label {
  line-height: 1;
  margin-bottom: 0;
}

.radio,
.checkbox {
  margin-bottom: 0;
}

.radio + .radio,
.checkbox + .checkbox {
  margin-top: 0;
}

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: relative;
  margin-top: initial;
  margin-left: initial;
}

.icheck-label {
  padding-left: 0;
}

.iradio,
.icheckbox {
  background-color: #fff;
  display: inline-block;
  width: 1.125rem;
  height: 1.125rem;
  margin-right: 0.28125rem;
  vertical-align: middle;
  border: 0.0625rem solid #e7e8e9;
  cursor: pointer;
}

.iradio label,
.icheckbox label {
  padding-left: 0;
  height: 1.125rem;
  vertical-align: middle;
}

.iradio:hover, .iradio.hover,
.icheck-label:hover .iradio,
.icheckbox:hover,
.icheckbox.hover,
.icheck-label:hover
.icheckbox {
  background-color: #0291db;
  border-color: #0291db;
}

.iradio:hover::before, .iradio.hover::before,
.icheck-label:hover .iradio::before,
.icheckbox:hover::before,
.icheckbox.hover::before,
.icheck-label:hover
.icheckbox::before {
  display: block;
  color: #fff;
}

.icheck-label:focus .iradio, .iradio:focus, .iradio.focus, .icheck-label:focus
.icheckbox,
.icheckbox:focus,
.icheckbox.focus {
  box-shadow: 0px 0px 4px 1px #0291db;
}

.iradio {
  border-radius: 0.5625rem;
}

.iradio.checked::before {
  position: absolute;
  display: block;
  top: -0.0625rem;
  left: -0.0625rem;
  width: 1.125rem;
  height: 1.125rem;
  border: 0.25rem solid #0291db;
  border-radius: 0.5625rem;
  background-color: #fff;
  content: '';
}

.icheckbox.checked {
  font-size: 0.75rem;
  text-align: center;
  background-color: #0291db;
  border-color: #0291db;
}

.icheckbox.checked::before {
  display: block;
  margin-top: 0.125rem;
  color: #fff;
  content: "\f171";
}

label[id^=iCheck] {
  padding-left: 0;
}

.checkbox-painted-wrapper,
.radio-painted-wrapper {
  position: relative;
}

.checkbox-painted-wrapper label,
.radio-painted-wrapper label {
  padding-left: 0;
  font: "0/0" a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
  background-color: #fff;
  display: inline-block;
  width: 1.125rem;
  height: 1.125rem;
  margin-right: 0.28125rem;
  vertical-align: middle;
  border: 0.0625rem solid #e7e8e9;
  margin-right: 0;
}

.checkbox-painted-wrapper label:hover,
.radio-painted-wrapper label:hover {
  background-color: #0291db;
  border-color: #0291db;
}

.checkbox-painted-wrapper label:hover::before,
.radio-painted-wrapper label:hover::before {
  display: block;
  color: #fff;
}

.checkbox-painted-wrapper input:focus + label,
.radio-painted-wrapper input:focus + label {
  box-shadow: 0px 0px 4px 1px #0291db;
}

.checkbox-painted-wrapper input[type="radio"],
.checkbox-painted-wrapper input[type="checkbox"],
.radio-painted-wrapper input[type="radio"],
.radio-painted-wrapper input[type="checkbox"] {
  margin-left: -9999px;
  position: absolute;
}

.checkbox-painted-wrapper input:checked + label {
  font-size: 0.75rem;
  text-align: center;
  background-color: #0291db;
  border-color: #0291db;
}

.checkbox-painted-wrapper input:checked + label::before {
  display: block;
  margin-top: 0.125rem;
  color: #fff;
  content: "\f171";
}

.radio-painted-wrapper label {
  border-radius: 0.5625rem;
}

.radio-painted-wrapper input:checked + label {
  border: 0.25rem solid #0291db;
  border-radius: 0.5625rem;
  background-color: #fff;
}

.spoiler-trigger {
  width: 100%;
}

.spoiler-content {
  display: none;
}

.spoiler {
  position: relative;
  min-height: 2.2rem;
}

.spoiler.spoiler-visible .spoiler-trigger {
  display: none !important;
}

.spoiler.spoiler-visible .spoiler-content {
  display: block;
}

.label-flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.label-flex .TinyProgress {
  padding-top: 0;
  padding-bottom: 0;
}

.flex-grow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.hero {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 2.25rem 1.125rem;
}

.hero > * {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.hero .hero-content {
  padding: 0 6.75rem 0 4.5rem;
  font-size: 0.8125rem;
  text-align: center;
  -webkit-flex-shrink: 1;
      -ms-flex-negative: 1;
          flex-shrink: 1;
}

.hero .hero-media-wrapper {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}

.hero .btn, .hero .file-upload-browse, .hero .Button {
  margin-top: 2.25rem;
}

.media {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.media .info, .media .FeedItem .FeedDescription, .FeedItem .media .FeedDescription, .media .FeedItem .Date, .FeedItem .media .Date {
  font-size: 0.8125rem;
}

.media .flag {
  margin-top: -.0625rem;
  margin-bottom: 0.5625rem;
}

.media .media-title {
  margin-bottom: 0.75rem;
}

.media .media-image {
  height: 10rem;
  border: 0.0625rem solid #e7e8e9;
}

.media .media-body {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.media-list {
  padding-left: 0;
  list-style: none;
}

.media-addon .media-image-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  min-width: 5.25rem;
  height: 5.25rem;
  border-radius: 0.25rem;
  overflow: hidden;
  background-color: #f6f9fb;
}

.media-addon .media-image-wrap::after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0.0625rem solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
  content: '';
}

.media-addon .media-image-wrap img {
  width: 5.25rem;
}

@media (max-width: 991px) {
  .media-addon .media-image-wrap {
    position: relative;
    min-width: 3.9375rem;
    height: 3.9375rem;
    border-radius: 0.25rem;
    overflow: hidden;
    background-color: #f6f9fb;
  }
  .media-addon .media-image-wrap::after {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0.0625rem solid rgba(0, 0, 0, 0.125);
    border-radius: 0.25rem;
    content: '';
  }
  .media-addon .media-image-wrap img {
    width: 3.9375rem;
  }
}

.media-addon .media-body {
  max-width: 540px;
}

.media-addon .media-right {
  margin-left: auto;
}

.media-addon,
.media-callout {
  border-bottom: 1px solid #e7e8e9;
  padding: 1.125rem 1.125rem;
}

.media-addon .media-title,
.media-callout .media-title {
  font-weight: 600;
  margin-bottom: 0.28125rem;
  margin-top: 0;
}

.media-options {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
}

.media-options > *:not(:first-child) {
  margin-left: 2.25rem;
}

@media (max-width: 991px) {
  .media-options {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-align-self: flex-start;
        -ms-flex-item-align: start;
            align-self: flex-start;
  }
  .media-options > *:not(:first-child) {
    margin-left: 0;
  }
  .media-options > *:not(:last-child) {
    margin-top: 0.5625rem;
  }
}

.documentationLink {
  display: inline-block;
  position: relative;
  vertical-align: 1px;
  margin-left: 1px;
}

.media-right {
  padding-left: 1.125rem;
}

.media-left {
  padding-right: 1.125rem;
}

.media-heading {
  margin-top: 0;
  margin-bottom: 0.5625rem;
}

.media-description {
  line-height: 1.3333333333;
}

.media-callout {
  padding-left: 0;
}

.media-callout .media-title {
  font-weight: 400;
  margin-bottom: 0.5625rem;
}

.media-callout .media-left img {
  display: block;
  width: 100%;
}

.media-callout.media-callout-grey-bg {
  background-color: #f4f6fb;
}

.media-callout .media-image-wrap {
  position: relative;
}

.media-callout .media-image-wrap::after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0.0625rem solid rgba(0, 0, 0, 0.125);
  border-radius: 0;
  content: '';
}

.modal .media-callout .media-left {
  max-height: 11.25rem;
  overflow: hidden;
}

.media-sm {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.media-sm .media-left {
  padding-right: 0.5625rem;
  height: 1.875rem;
}

.media-sm .media-image-wrap {
  display: inline-block;
  position: relative;
  min-width: 1.875rem;
  height: 1.875rem;
  border-radius: 0.0625rem;
  overflow: hidden;
  background-color: #f6f9fb;
}

.media-sm .media-image-wrap::after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0.0625rem solid rgba(0, 0, 0, 0.125);
  border-radius: 0.0625rem;
  content: '';
}

.media-sm .media-image-wrap img {
  width: 1.875rem;
}

.media-sm .media-image-wrap-no-border {
  display: inline-block;
  position: relative;
  min-width: 1.875rem;
  height: 1.875rem;
  border-radius: 0.0625rem;
  overflow: hidden;
  background-color: transparent;
  margin-right: 0.5625rem;
}

.media-sm .media-image-wrap-no-border img {
  width: 1.875rem;
}

.media-sm .media-title {
  margin-bottom: 0.3125rem;
  font-size: 0.8125rem;
  line-height: 1;
}

.media-sm .media-title a {
  color: #555a62;
  -webkit-transition: color 200ms;
          transition: color 200ms;
}

.media-sm .media-title a:hover {
  text-decoration: none;
  color: #0291db;
}

.media-sm .info, .media-sm .FeedItem .FeedDescription, .FeedItem .media-sm .FeedDescription, .media-sm .FeedItem .Date, .FeedItem .media-sm .Date {
  font-size: 0.75rem;
  line-height: 1;
  color: #949aa2;
}

.modal-dialog {
  position: relative;
  margin: 3vh auto;
  max-width: 1024px;
}

.modal-dialog.modal-sm {
  max-width: 400px;
}

.modal-dialog.modal-md {
  max-width: 600px;
}

.modal-dialog.modal-center {
  margin: 25vh auto;
}

.modal-dialog.modal-center .modal-body {
  max-height: 50vh;
}

.modal-body {
  position: relative;
  max-height: -webkit-calc(94vh - (2 * 3rem));
  max-height: calc(94vh - (2 * 3rem));
  overflow-y: auto;
  padding: 0 1.125rem;
}

.modal-confirm .modal-body {
  padding: 1.125rem;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;
}

.modal-backdrop.fade {
  opacity: 0;
}

.modal-backdrop.in {
  opacity: 0.5;
}

.modal-content {
  position: relative;
  background-color: #fff;
  background-clip: padding-box;
  border: 0.0625rem solid rgba(0, 0, 0, 0.2);
  border-radius: 0.125rem;
  outline: 0;
}

.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 0 0.5625rem;
  z-index: 1050;
  overflow: hidden;
  outline: 0;
  -webkit-overflow-scrolling: touch;
}

.modal.fade .modal-dialog {
  -webkit-transition: opacity .2s ease-out;
          transition: opacity .2s ease-out;
  -webkit-transform: none;
      -ms-transform: none;
          transform: none;
  opacity: 0;
}

.modal.in .modal-dialog {
  -webkit-transform: none;
      -ms-transform: none;
          transform: none;
  -webkit-filter: none;
  opacity: 1;
}

.modal-open {
  overflow: hidden;
}

.modal-open .main-row,
.modal-open .navbar {
  -webkit-filter: blur(5px);
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal-footer,
.modal-header {
  z-index: 1005;
  padding: 0.75rem 1.125rem;
}

.modal-footer {
  text-align: right;
  border-top: 0.0625rem solid #e5e5e5;
  margin-top: 0.0625rem;
}

.modal-footer:empty {
  display: none;
}

.modal-footer::before {
  position: absolute;
  left: 0;
  bottom: 3.8125rem;
  display: block;
  width: 100%;
  height: 1rem;
  box-shadow: inset 0 -0.75rem 0.75rem 0 white;
  content: '';
}

.modal-header {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 0.0625rem solid #e5e5e5;
}

.modal-title {
  line-height: 1.5;
  margin: 0;
}

.modal-close {
  padding: 0;
}

.modal-close svg {
  height: 1.75rem;
}

button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
}

.modal-no-header .modal-close {
  position: absolute;
  top: 0.5625rem;
  right: 0.5625rem;
}

.modal .list-un-reset ul,
.modal .list-un-reset ol {
  margin-left: 15px;
}

.modal .list-un-reset ul li {
  list-style: disc inside;
}

.modal .list-un-reset ol li {
  list-style: decimal inside;
}

.modal .list-un-reset ul ul,
.modal .list-un-reset ol ul {
  margin-left: 30px;
}

.modal .list-un-reset ul ul li,
.modal .list-un-reset ol ul li {
  list-style: circle inside;
}

.modal .list-un-reset ol ol,
.modal .list-un-reset ul ol {
  margin-left: 15px;
}

.modal .list-un-reset ol ol li,
.modal .list-un-reset ul ol li {
  list-style: lower-latin inside;
}

.nav-adventure {
  margin-top: -0.0625rem;
}

.nav-adventure .nav-item {
  display: block;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
}

.nav-adventure .nav-item:first-child {
  border-top: 0.0625rem solid #e7e8e9;
}

.nav-adventure .nav-item-title {
  line-height: 1;
  font-weight: 600;
  margin-bottom: 0.375rem;
}

.nav-adventure .nav-item-description {
  margin-bottom: -0.3125rem;
}

.nav-adventure .nav-link {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  justify-items: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 3px 1.125rem;
  line-height: 20px;
  color: #555a62;
  -webkit-transition: background-color .1s;
          transition: background-color .1s;
}

.nav-adventure .nav-link > * {
  z-index: 1;
}

.nav-adventure .nav-link svg {
  display: block;
  height: 1.5rem;
}

.nav-adventure .nav-link:hover {
  background-color: #f9fdfe;
}

.nav-adventure .nav-link:hover::before {
  width: 100%;
}

.nav-adventure .nav-item-icon {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  width: 2.25rem;
  height: 2.25rem;
  border: 0.0625rem solid #e7e8e9;
  border-radius: 0.125rem;
  margin-right: 1.125rem;
}

.nav-adventure .nav-item-arrow {
  margin-left: auto;
}

.nav-stats, .analytics-panel-metrics {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.nav-stats-item, .analytics-panel-metrics .analytics-widget-metric {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: 1;
  padding: 0.5625rem 1.125rem 0;
  color: rgba(25, 25, 25, 0.2);
}

.nav-stats-link > * {
  cursor: pointer;
}

.nav-stats-title, .analytics-panel-metrics .metric-title {
  font-weight: 400;
  padding: 0.5625rem 0 0.28125rem;
  text-transform: uppercase;
  font-size: 0.75rem;
}

.nav-stats-value, .analytics-panel-metrics .metric-value {
  font-weight: 300;
  font-size: 2.5rem;
}

.nav-heading {
  margin-top: 0.5625rem;
  margin-bottom: 0.5625rem;
}

.nav-heading a {
  display: block;
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: .75rem;
  border-bottom: 1px dotted rgba(0, 0, 0, 0.0875);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  text-decoration: none;
}

.nav-heading a::before {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: .25rem;
  margin-left: .25rem;
  vertical-align: middle;
  content: '';
  border-top: 0.375em solid;
  border-right: 0.375em solid transparent;
  border-left: 0.375em solid transparent;
  position: relative;
  top: -1px;
  -webkit-transform: rotate(0deg);
  tranform: rotate(0deg);
  margin-left: -1rem;
  margin-right: .5rem;
  -webkit-transition: all .1s ease-out;
          transition: all .1s ease-out;
}

.nav-heading a.collapsed::before {
  -webkit-transform: rotate(-90deg);
  tranform: rotate(-90deg);
}

.nav-inline {
  font-size: 0;
}

.nav-inline .nav-item {
  display: inline-block;
}

.nav-inline .nav-item + .nav-item,
.nav-inline .nav-link + .nav-link {
  margin-left: 1rem;
}

.panel-nav a {
  color: #949aa2;
}

.panel-nav .title {
  display: block;
  margin-bottom: 1.125rem;
}

.panel-nav .badge {
  font-size: 0.6875rem;
}

.panel-nav .globals-sign-out {
  display: none;
}

@media (max-width: 991px) {
  .panel-nav .globals-sign-out {
    display: block;
    margin-top: 2.25rem;
  }
}

.panel-nav .nav-pill {
  color: #f6f9fb;
  background: rgba(148, 154, 162, 0.6);
  display: inline-block;
  font-size: 8px;
  line-height: 1;
  padding: 2px 4px;
  border-radius: 3px;
  vertical-align: middle;
}

.panel-nav .nav-link.active, .panel-nav .file-upload input:focus ~ .nav-link.file-upload-choose, .file-upload .panel-nav input:focus ~ .nav-link.file-upload-choose, .panel-nav
.file-upload input:focus ~ .nav-link.file-upload-browse,
.file-upload .panel-nav input:focus ~ .nav-link.file-upload-browse, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-choose,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-choose, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-browse,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-browse, .panel-nav select.nav-link:focus, .panel-nav .nav-link.drop-enabled {
  border-radius: 0.125rem 0 0 0.125rem;
}

.panel-nav .nav-link.active, .panel-nav .file-upload input:focus ~ .nav-link.file-upload-choose, .file-upload .panel-nav input:focus ~ .nav-link.file-upload-choose, .panel-nav
.file-upload input:focus ~ .nav-link.file-upload-browse,
.file-upload .panel-nav input:focus ~ .nav-link.file-upload-browse, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-choose,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-choose, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-browse,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-browse, .panel-nav select.nav-link:focus, .panel-nav .nav-link.drop-enabled, .panel-nav .nav-link.active:hover, .panel-nav .file-upload input:focus ~ .nav-link.file-upload-choose:hover, .file-upload .panel-nav input:focus ~ .nav-link.file-upload-choose:hover, .panel-nav
.file-upload input:focus ~ .nav-link.file-upload-browse:hover,
.file-upload .panel-nav input:focus ~ .nav-link.file-upload-browse:hover, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-choose:hover,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-choose:hover, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-browse:hover,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-browse:hover, .panel-nav select.nav-link:hover:focus, .panel-nav .nav-link.drop-enabled:hover {
  color: #fff;
  background-color: #0291db;
}

.panel-nav .nav-link.active .nav-pill, .panel-nav .file-upload input:focus ~ .nav-link.file-upload-choose .nav-pill, .file-upload .panel-nav input:focus ~ .nav-link.file-upload-choose .nav-pill, .panel-nav
.file-upload input:focus ~ .nav-link.file-upload-browse .nav-pill,
.file-upload .panel-nav input:focus ~ .nav-link.file-upload-browse .nav-pill, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-choose .nav-pill,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-choose .nav-pill, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-browse .nav-pill,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-browse .nav-pill, .panel-nav select.nav-link:focus .nav-pill, .panel-nav .nav-link.drop-enabled .nav-pill, .panel-nav .nav-link.active:hover .nav-pill, .panel-nav .file-upload input:focus ~ .nav-link.file-upload-choose:hover .nav-pill, .file-upload .panel-nav input:focus ~ .nav-link.file-upload-choose:hover .nav-pill, .panel-nav
.file-upload input:focus ~ .nav-link.file-upload-browse:hover .nav-pill,
.file-upload .panel-nav input:focus ~ .nav-link.file-upload-browse:hover .nav-pill, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-choose:hover .nav-pill,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-choose:hover .nav-pill, .panel-nav
.file-upload input:hover ~ .nav-link.file-upload-browse:hover .nav-pill,
.file-upload .panel-nav input:hover ~ .nav-link.file-upload-browse:hover .nav-pill, .panel-nav select.nav-link:hover:focus .nav-pill, .panel-nav .nav-link.drop-enabled:hover .nav-pill {
  color: #0291db;
  background-color: rgba(255, 255, 255, 0.7);
}

.panel-nav .nav-link:hover, .panel-nav .nav-link.hover {
  color: #949aa2;
  background-color: #e8ecf2;
}

.panel-nav .nav-stacked .nav-item + .nav-item {
  margin-top: 0;
}

.nav-link, .nav-link.active, .file-upload input:focus ~ .nav-link.file-upload-choose,
.file-upload input:focus ~ .nav-link.file-upload-browse,
.file-upload input:hover ~ .nav-link.file-upload-choose,
.file-upload input:hover ~ .nav-link.file-upload-browse, select.nav-link:focus, .nav-link.drop-enabled, .nav-link:active, .nav-link.hover, .nav-link:hover {
  cursor: pointer;
}

.navbar {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-justify-content: space-around;
      -ms-flex-pack: distribute;
          justify-content: space-around;
  padding: 0;
  color: #fff;
  background-color: #0291db;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.navbar .vanilla-logo {
  width: 65px;
}

.navbar .title {
  color: #fff;
}

.navbar .logo + .title {
  margin-left: 1.125rem;
}

.navbar img {
  height: 2.25rem;
}

.navbar .nav-item {
  text-align: center;
  border-right: 1px solid rgba(0, 0, 0, 0.125);
}

.navbar .nav-item + .nav-item {
  margin: 0;
}

.navbar .nav-item:first-child {
  border-left: 1px solid rgba(0, 0, 0, 0.125);
}

.navbar .nav {
  height: 100%;
}

.navbar .nav-link {
  position: relative;
  min-width: 180px;
  color: #fff;
  border-radius: 0;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.navbar .nav-link.active, .navbar .file-upload input:focus ~ .nav-link.file-upload-choose, .file-upload .navbar input:focus ~ .nav-link.file-upload-choose, .navbar
.file-upload input:focus ~ .nav-link.file-upload-browse,
.file-upload .navbar input:focus ~ .nav-link.file-upload-browse, .navbar
.file-upload input:hover ~ .nav-link.file-upload-choose,
.file-upload .navbar input:hover ~ .nav-link.file-upload-choose, .navbar
.file-upload input:hover ~ .nav-link.file-upload-browse,
.file-upload .navbar input:hover ~ .nav-link.file-upload-browse, .navbar select.nav-link:focus, .navbar .nav-link.drop-enabled, .navbar .nav-link.active:hover, .navbar .file-upload input:focus ~ .nav-link.file-upload-choose:hover, .file-upload .navbar input:focus ~ .nav-link.file-upload-choose:hover, .navbar
.file-upload input:focus ~ .nav-link.file-upload-browse:hover,
.file-upload .navbar input:focus ~ .nav-link.file-upload-browse:hover, .navbar
.file-upload input:hover ~ .nav-link.file-upload-choose:hover,
.file-upload .navbar input:hover ~ .nav-link.file-upload-choose:hover, .navbar
.file-upload input:hover ~ .nav-link.file-upload-browse:hover,
.file-upload .navbar input:hover ~ .nav-link.file-upload-browse:hover, .navbar select.nav-link:hover:focus, .navbar .nav-link.drop-enabled:hover {
  background-color: #04a8fd;
}

.navbar .nav-link.active > *, .navbar .file-upload input:focus ~ .nav-link.file-upload-choose > *, .file-upload .navbar input:focus ~ .nav-link.file-upload-choose > *, .navbar
.file-upload input:focus ~ .nav-link.file-upload-browse > *,
.file-upload .navbar input:focus ~ .nav-link.file-upload-browse > *, .navbar
.file-upload input:hover ~ .nav-link.file-upload-choose > *,
.file-upload .navbar input:hover ~ .nav-link.file-upload-choose > *, .navbar
.file-upload input:hover ~ .nav-link.file-upload-browse > *,
.file-upload .navbar input:hover ~ .nav-link.file-upload-browse > *, .navbar select.nav-link:focus > *, .navbar .nav-link.drop-enabled > *, .navbar .nav-link.active:hover > *, .navbar .file-upload input:focus ~ .nav-link.file-upload-choose:hover > *, .file-upload .navbar input:focus ~ .nav-link.file-upload-choose:hover > *, .navbar
.file-upload input:focus ~ .nav-link.file-upload-browse:hover > *,
.file-upload .navbar input:focus ~ .nav-link.file-upload-browse:hover > *, .navbar
.file-upload input:hover ~ .nav-link.file-upload-choose:hover > *,
.file-upload .navbar input:hover ~ .nav-link.file-upload-choose:hover > *, .navbar
.file-upload input:hover ~ .nav-link.file-upload-browse:hover > *,
.file-upload .navbar input:hover ~ .nav-link.file-upload-browse:hover > *, .navbar select.nav-link:hover:focus > *, .navbar .nav-link.drop-enabled:hover > * {
  color: #fff;
}

.navbar .nav-link:hover {
  background-color: #04a8fd;
}

@media (max-width: 1199px) {
  .navbar .navbar-brand,
  .navbar .navbar-memenu {
    min-width: inherit;
  }
}

@media (max-width: 991px) {
  .navbar {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
  .navbar .nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  .navbar .nav-item {
    width: 100%;
  }
  .navbar .nav-link {
    min-width: inherit;
    width: 100%;
    font-size: 0.75rem;
  }
  .navbar .nav-link-description {
    display: none;
  }
  .navbar .navbar-memenu,
  .navbar .navbar-brand {
    display: none;
  }
}

.drawer-toggle {
  color: #fff;
  font-size: 1.125rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.drawer-toggle:hover, .drawer-toggle:active, .drawer-toggle:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
}

.drawer-toggle:hover, .drawer-toggle:active {
  background-color: #04a8fd;
}

.navbar-memenu,
.navbar-brand {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-width: 15rem;
}

.navbar-profile {
  cursor: pointer;
}

.navbar-profile .icon-caret-down {
  border-top-color: #ceeeff;
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: .25rem;
  margin-left: .25rem;
  vertical-align: middle;
  content: '';
  border-top: 0.375em solid;
  border-right: 0.375em solid transparent;
  border-left: 0.375em solid transparent;
}

.nav-icons {
  margin-right: 1.125rem;
  color: #fff;
}

.nav-icons svg {
  display: block;
  height: 1.9583333333rem;
}

.nav-icons .nav-link {
  min-width: inherit;
}

.nestable-list,
.nestable-list ul,
.nestable-list ol,
.nestable-dragel,
.nestable-dragel ul,
.nestable-dragel ol {
  padding-left: 0;
}

.nestable-list li,
.nestable-dragel li {
  list-style: none;
}

.nestable-item .nestable-item,
.nestable-item .nestable-placeholder {
  margin-left: 1.875rem;
}

.nestable-item,
.nestable-empty,
.nestable-placeholder {
  display: block;
  position: relative;
  margin: 0 0 0.375rem;
  padding: 0;
}

.nestable-empty,
.nestable-placeholder {
  background: #f1faff;
  border: 0.0625rem dashed #0291db;
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

.nestable-collapse {
  position: absolute;
  display: block;
  width: 1.25rem;
  height: 2.25rem;
  left: 2.25rem;
}

.nestable-handle {
  position: absolute;
  display: block;
  width: 2.25rem;
  height: 2.25rem;
  cursor: move;
  cursor: -webkit-grab;
  cursor: grab;
}

.nestable-dragel {
  position: absolute;
  pointer-events: none;
  z-index: 9999;
  opacity: .8;
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

.nestable-content {
  margin-bottom: 0.375rem;
}

.help {
  margin-bottom: 2.25rem;
  color: #555a62;
}

.help .help-title {
  padding-bottom: 0.5625rem;
  margin-top: 0;
  margin-bottom: 0.5625rem;
  border-bottom: 0.0625rem dotted #ededed;
  letter-spacing: 0.0625rem;
  font-size: 0.875rem;
  text-transform: uppercase;
}

.help a {
  color: #0291db;
  font-size: 0.8125rem;
  font-weight: 500;
  border-bottom: 0.0625rem dotted #0291db;
}

.help a, .help a:hover {
  text-decoration: none;
}

.help code {
  word-wrap: break-word;
}

.help li {
  margin-bottom: 0.28125rem;
  font-size: 0.75rem;
}

.help ol,
.help ul {
  padding-left: 1.125rem;
}

.control-panel {
  font-size: 0.75rem;
  color: #727d83;
}

.panel .control-panel {
  margin-bottom: 2.25rem;
}

.control-panel .control-panel-list-item {
  line-height: 1.125rem;
  margin-bottom: 0;
}

.control-panel .control-panel-list-item > * {
  display: inline-block;
  vertical-align: middle;
}

.control-panel ul,
.control-panel ul ul,
.control-panel ul ol,
.control-panel ol,
.control-panel ol ul,
.control-panel ol ol {
  padding-left: 0;
}

.control-panel ul li,
.control-panel ol li {
  list-style: none;
}

.control-panel .control-panel-heading {
  padding-bottom: 0.5625rem;
  margin-top: 0;
  margin-bottom: 0.5625rem;
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 600;
  color: #555a62;
  border-bottom: 1px dotted #ededed;
}

.control-panel .control-panel-subheading {
  margin-top: 0.5625rem;
  margin-bottom: 0.28125rem;
  font-weight: 600;
}

.control-panel .control-panel-footer {
  margin-top: 1.125rem;
}

.control-panel .control-panel-list-item-label {
  max-width: 6.375rem;
  font-style: italic;
}

.control-panel select {
  height: 1.875rem;
  width: 100%;
}

.btn-control-panel {
  padding: 0.125rem 0.75rem;
  font-size: 0.6875rem;
  line-height: 1.5;
  border-radius: 0.125rem;
  color: #949aa2;
  background-color: #fff;
  border-color: #949aa2;
}

.btn-control-panel:hover {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
}

.btn-control-panel:focus, .btn-control-panel.focus {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
  outline: 0;
}

.btn-control-panel:active, .btn-control-panel.active, .file-upload input:focus ~ .btn-control-panel.file-upload-choose,
.file-upload input:focus ~ .btn-control-panel.file-upload-browse,
.file-upload input:hover ~ .btn-control-panel.file-upload-choose,
.file-upload input:hover ~ .btn-control-panel.file-upload-browse, select.btn-control-panel:focus, .btn-control-panel.drop-enabled,
.open > .btn-control-panel.dropdown-toggle {
  color: #fff;
  background-color: #0291db;
  border-color: #0291db;
  background-image: none;
}

.btn-control-panel.disabled:focus, .btn-control-panel.disabled.focus, .btn-control-panel:disabled:focus, .btn-control-panel:disabled.focus {
  background-color: #fff;
}

.btn-control-panel.disabled:hover, .btn-control-panel:disabled:hover {
  background-color: #fff;
}

.plank {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 0.375rem;
  min-height: 2.25rem;
  background-color: #fbfcfc;
  border: 0.0625rem solid #e7e8e9;
  border-radius: 2px;
}

.plank svg {
  height: 1rem;
  vertical-align: middle;
}

.plank-title {
  padding: 0 0.5625rem;
}

.plank-options {
  color: #949aa2;
  margin-left: auto;
  margin-top: -0.0625rem;
  margin-bottom: -0.0625rem;
}

.plank-options .dropdown-toggle {
  padding: 0.375rem 0.625rem;
  line-height: 1.375rem;
}

.plank-icon {
  color: #949aa2;
}

.plank-icon:hover {
  fill: rgba(85, 90, 98, 0.6);
}

.plank-container-grid {
  padding: 1.125rem 0.9375rem;
}

.plank-container-grid .plank-wrapper {
  padding-left: 0.1875rem;
  padding-right: 0.1875rem;
}

.main-row pre.prettyprint {
  padding: 0.5625rem;
  font-size: 0.75rem;
  border: 0.0625rem solid #e7e8e9 !important;
  border-radius: 0.125rem;
  background-color: transparent;
}

.input-wrap pre.prettyprint, .color-picker pre.prettyprint {
  min-height: 2.25rem;
  padding-top: .40625rem;
  padding-bottom: .40625rem;
  line-height: 24px;
  background-color: #f4f6fb;
}

.main-container {
  width: 100%;
  overflow-x: hidden;
}

.navbar {
  position: fixed;
  width: 100%;
  height: 3rem;
  z-index: 1010;
}

.main-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 3rem;
}

@media (max-width: 991px) {
  .main-row {
    display: block;
    width: 100%;
  }
}

.main {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  -webkit-box-ordinal-group: 3;
  -webkit-order: 2;
      -ms-flex-order: 2;
          order: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  min-height: 100vh;
  margin-top: -3rem;
  padding-top: 3rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  max-width: 1024px;
  overflow-y: hidden;
}

.main .content {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding-bottom: 7.5rem;
}

.main .footer {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
  height: 3.75rem;
  margin-top: -3.8125rem;
}

.panel {
  padding-top: 4.5rem;
  padding-bottom: 4.5rem;
}

.panel-content,
.panel-content-wrapper {
  width: 204px;
}

.panel-left {
  padding-left: 1.125rem;
}

.panel-right {
  padding-right: 1.125rem;
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
      -ms-flex-order: 3;
          order: 3;
}

.panel-right .panel-content {
  padding-left: 1.125rem;
}

.main-row-wide .main {
  max-width: 1296px;
}

.main-row-wide .panel-right {
  display: none;
}

.main-row-wide .panel-left {
  display: none;
}

.drawer-show .main {
  -webkit-transform: translate3d(222px, 0, 0);
          transform: translate3d(222px, 0, 0);
}

.drawer-show .main::after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.25);
  z-index: 3;
  content: '';
}

.drawer-toggle {
  display: none;
}

.drawer-only {
  display: none;
}

.drawer-show .drawer-only {
  display: block;
}

@media (max-width: 991px) {
  .panel-right {
    display: none;
  }
  .panel-left {
    display: none;
  }
  .drawer-toggle {
    display: block;
  }
  .drawer-show .panel-left {
    position: absolute;
    display: block;
    width: 222px;
    height: 100%;
    padding-top: 7.5rem;
  }
}

.table-wrap {
  display: block;
  margin-top: 0;
  margin-right: -1.125rem;
  margin-bottom: 0;
  margin-left: -1.125rem;
}

.table-data {
  margin-bottom: 0;
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
  margin-top: -0.0625rem;
}

.table-data td.options .btn-group {
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.table-data .toggle-wrap {
  margin-left: 0.75rem;
}

.table-data tbody + tbody {
  border-top: 0.0625rem solid #f4f6fb;
}

.table-data-cell, .table-data th,
.table-data td, .table-summary th,
.table-summary td {
  overflow: hidden;
  padding: 0.75rem 1.125rem;
  line-height: 1.5;
  vertical-align: middle;
  border-bottom: 0.0625rem solid #f4f6fb;
}

.table-data-body, .table-data tbody, .table-summary tbody {
  font-size: 0.8125rem;
}

.table-data-body tr:hover, .table-data tbody tr:hover, .table-summary tbody tr:hover {
  background-color: #f9fdfe;
}

.table-data-head tr, .table-data thead tr {
  background-color: #f6f9fb;
}

.table-data-head th, .table-data thead th,
.table-data-head td, .table-data thead td {
  width: 6.25rem;
  padding: 0.5625rem 0.84375rem;
  border-right: 0.0625rem solid #eee;
  border-top: 0.0625rem solid #ddd;
  border-bottom: 0.0625rem solid #f4f6fb;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.75rem;
  line-height: 1.3333333333;
}

.table-data-head th:last-child, .table-data thead th:last-child,
.table-data-head td:last-child, .table-data thead td:last-child {
  border-right: 0;
}

.table-data-head th.column-xl, .table-data thead th.column-xl,
.table-data-head td.column-xl, .table-data thead td.column-xl {
  width: 18.75rem;
}

.table-data-head th.column-lg, .table-data thead th.column-lg,
.table-data-head td.column-lg, .table-data thead td.column-lg {
  width: 13.125rem;
}

.table-data-head th.column-md, .table-data thead th.column-md,
.table-data-head td.column-md, .table-data thead td.column-md {
  width: 8.25rem;
}

.table-data-head th.column-sm, .table-data thead th.column-sm,
.table-data-head td.column-sm, .table-data thead td.column-sm {
  width: 5.625rem;
}

.table-data-head th.column-xs, .table-data thead th.column-xs,
.table-data-head td.column-xs, .table-data thead td.column-xs {
  width: 3.375rem;
}

.table-data-head th.column-checkbox, .table-data thead th.column-checkbox,
.table-data-head td.column-checkbox, .table-data thead td.column-checkbox {
  width: 3.375rem;
  max-width: 3.375rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}

.tj-meta,
.table-meta {
  margin-top: 0.5625rem;
}

.tj-meta + .tj-meta,
.table-meta + .tj-meta, .tj-meta +
.table-meta,
.table-meta +
.table-meta {
  margin-top: 0;
}

.tj-meta .table-meta-item,
.table-meta .table-meta-item {
  font-size: 0.75rem;
}

.tj-meta .table-meta-item-label,
.table-meta .table-meta-item-label {
  font-weight: 600;
}

.tj-meta .table-meta-item-label a,
.table-meta .table-meta-item-label a {
  color: #555a62;
  pointer-events: none;
  cursor: default;
}

.tj-meta .media-sm,
.table-meta .media-sm {
  display: inline-block;
}

.tj-meta .media-sm .media-left,
.tj-meta .media-sm .info,
.tj-meta .media-sm .FeedItem .FeedDescription, .FeedItem
.tj-meta .media-sm .FeedDescription,
.tj-meta .media-sm .FeedItem .Date, .FeedItem
.tj-meta .media-sm .Date,
.table-meta .media-sm .media-left,
.table-meta .media-sm .info,
.table-meta .media-sm .FeedItem .FeedDescription, .FeedItem
.table-meta .media-sm .FeedDescription,
.table-meta .media-sm .FeedItem .Date, .FeedItem
.table-meta .media-sm .Date {
  display: none;
}

.table-checkbox-grid {
  table-layout: auto;
}

.table-checkbox-grid .checkbox {
  margin-bottom: 0;
  margin-left: auto;
  margin-right: auto;
}

.table-checkbox-grid th {
  font-weight: 600;
}

.table-checkbox-grid a {
  color: #555a62;
}

.table-checkbox-grid a:hover {
  color: #0291db;
}

.table-checkbox-grid thead td {
  text-align: center;
  padding: 0.5625rem;
  border: 1px solid #eee;
  border-top: 1px solid #ddd;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.75rem;
  line-height: 1.3333333333;
  vertical-align: middle;
  width: 10%;
}

.table-checkbox-grid thead td:last-child {
  border-right: 0;
}

.table-checkbox-grid thead th {
  font-weight: 700;
  max-width: 7.5rem;
  min-width: 7.5rem;
}

.table-checkbox-grid tbody td {
  padding: 0.5625rem 0.84375rem;
  text-align: center;
}

@media screen and (max-width: 575px) {
  .table-checkbox-grid {
    table-layout: fixed;
  }
  .table-checkbox-grid thead td,
  .table-checkbox-grid thead th,
  .table-checkbox-grid tbody td,
  .table-checkbox-grid tbody th {
    font-size: 0.75rem;
    padding: 0.375rem;
  }
  .table-checkbox-grid tbody th {
    font-size: 0.8125rem;
  }
  .table-checkbox-grid thead th {
    width: 100%;
  }
  .table-checkbox-grid thead td {
    position: relative;
    width: 2.25rem;
    height: 5.625rem;
  }
  .table-checkbox-grid thead td a {
    position: relative;
    display: block;
    -webkit-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
            transform: rotate(90deg);
    top: -1.5rem;
  }
}

.summaries {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.summaries > * {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  max-width: 50%;
  padding: 1.125rem;
}

@media (max-width: 991px) {
  .summaries > * {
    max-width: inherit;
    width: 100%;
    padding: 1.125rem 0;
  }
}

.table-summary {
  width: 100%;
  max-width: 100%;
  table-layout: fixed;
}

.table-summary th,
.table-summary td {
  text-align: center;
  padding: 0.75rem 0.75rem;
}

.table-summary th:first-child,
.table-summary td:first-child {
  text-align: left;
}

.table-summary thead th,
.table-summary thead td {
  width: 60%;
  padding: 0.5625rem 0.75rem;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.75rem;
  line-height: 1.3333333333;
  border-bottom: 0.0625rem solid #ddd;
}

.table-summary thead th .table-summary-cell-main,
.table-summary thead td .table-summary-cell-main {
  width: 100%;
  min-width: 7.5rem;
}

.table-summary-title {
  font-size: 1rem;
  font-weight: 600;
}

.toaster, .InformMessages .InformMessage {
  position: relative;
  display: block;
  width: 448px;
  padding: 1.125rem 3.375rem 1.125rem 1.125rem;
  margin-top: 0.140625rem;
  font-weight: 400;
  font-size: 0.8125rem;
  line-height: 1.125rem;
  color: #fff;
  background: #333;
  border: 0.0625rem solid #000;
  opacity: .9;
  box-shadow: inset 0 0 1px 0 rgba(255, 255, 255, 0.72);
  border-radius: 0.125rem;
}

@media (max-width: 767px) {
  .toaster, .InformMessages .InformMessage {
    width: 100%;
  }
}

.toaster a, .InformMessages .InformMessage a {
  color: #fff;
  text-decoration: underline;
}

.toaster:hover, .InformMessages .InformMessage:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.toaster-close, .InformMessages .InformMessage .Close {
  position: absolute;
  top: 1rem;
  right: 1.125rem;
  cursor: pointer;
}

.toaster-close::before, .InformMessages .InformMessage .Close::before {
  display: inline-block;
  content: url(data:image/svg+xml,%3Csvg%20width%3D%2224px%22%20height%3D%2224px%22%20viewBox%3D%220%200%2017%2017%22%20version%3D%221.1%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20xmlns%3Axlink%3D%22http%3A//www.w3.org/1999/xlink%22%3E%0A%20%20%20%20%3Cg%20id%3D%22Styles%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%20opacity%3D%220.7%22%3E%0A%20%20%20%20%20%20%20%20%3Cg%20id%3D%22Close_X%22%20fill%3D%22%23ffffff%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cpath%20d%3D%22M8.25%2C7.40427996%20L4.02169351%2C3.17597347%20C3.7877566%2C2.94203656%203.40921888%2C2.9421406%203.17567974%2C3.17567974%20C2.94051241%2C3.41084707%202.94227211%2C3.78799215%203.17597347%2C4.02169351%20L7.40427996%2C8.25%20L3.17597347%2C12.4783065%20C2.94203656%2C12.7122434%202.9421406%2C13.0907811%203.17567974%2C13.3243203%20C3.41084707%2C13.5594876%203.78799215%2C13.5577279%204.02169351%2C13.3240265%20L8.25%2C9.09572004%20L12.4783065%2C13.3240265%20C12.7122434%2C13.5579634%2013.0907811%2C13.5578594%2013.3243203%2C13.3243203%20C13.5594876%2C13.0891529%2013.5577279%2C12.7120079%2013.3240265%2C12.4783065%20L9.09572004%2C8.25%20L13.3240265%2C4.02169351%20C13.5579634%2C3.7877566%2013.5578594%2C3.40921888%2013.3243203%2C3.17567974%20C13.0891529%2C2.94051241%2012.7120079%2C2.94227211%2012.4783065%2C3.17597347%20L8.25%2C7.40427996%20L8.25%2C7.40427996%20Z%22%20id%3D%22Combined-Shape%22%3E%3C/path%3E%0A%20%20%20%20%20%20%20%20%3C/g%3E%0A%20%20%20%20%3C/g%3E%0A%3C/svg%3E);
}

.toasters, .InformMessages {
  position: fixed;
  bottom: 1.5rem;
  left: 1.5rem;
  z-index: 1050;
}

@media (max-width: 767px) {
  .toasters, .InformMessages {
    left: 0.75rem;
    right: 0.75rem;
    bottom: 0.75rem;
  }
}

.toolbar {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding-top: 0.5625rem;
  padding-bottom: 1.125rem;
  padding-right: 0;
}

.toolbar > * {
  margin-top: 0.5625rem;
  padding-right: 1.125rem;
  -webkit-flex-shrink: 2;
      -ms-flex-negative: 2;
          flex-shrink: 2;
}

.toolbar .toolbar-buttons {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
          flex-wrap: nowrap;
}

.toolbar:empty {
  padding-top: 0;
  border-bottom: 0;
}

.toolbar .toolbar-main {
  -webkit-flex-basis: 30.875rem;
      -ms-flex-preferred-size: 30.875rem;
          flex-basis: 30.875rem;
  -webkit-flex-shrink: 1;
      -ms-flex-negative: 1;
          flex-shrink: 1;
}

.pager-count {
  line-height: 1;
  font-weight: 600;
  padding-right: 0.5625rem;
  white-space: nowrap;
}

.pager-wrap {
  margin-left: auto;
}

.pager {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
          flex-wrap: nowrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.video-section-heading {
  display: block;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
  padding: 1.125rem 0;
  font-weight: 600;
}

.Section-Tutorials .label-selector {
  margin-left: 0;
  margin-right: 0;
}

.Section-Tutorials .hero .label-selector-item {
  -webkit-box-flex: 0;
  -webkit-flex: 0 0 100%;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

.Section-Tutorials .tagline {
  font-size: 0.75rem;
  font-weight: 600;
}

.Section-Tutorials .vanilla-logo {
  width: 6.75rem;
  height: 4.5rem;
}

.Section-Tutorials .video.label-selector-item a {
  color: #fff;
}

.Section-Tutorials .video.label-selector-item .video-title {
  font-size: 0.75rem;
  text-align: left;
}

.Section-Tutorials .video.label-selector-item .overlay {
  background-color: rgba(0, 0, 0, 0.75);
}

.Section-Tutorials .video.label-selector-item.current .overlay {
  height: 100%;
}

.Section-Tutorials .video.label-selector-item svg {
  width: 40%;
}

.Section-Tutorials .video-section .info, .Section-Tutorials .video-section .FeedItem .FeedDescription, .FeedItem .Section-Tutorials .video-section .FeedDescription, .Section-Tutorials .video-section .FeedItem .Date, .FeedItem .Section-Tutorials .video-section .Date {
  padding-top: 1.125rem;
}

.Section-Tutorials .video-section:first-child .video-section-heading {
  border-top: 0.0625rem solid #f6f9fb;
}

.content-cell {
  word-break: break-word;
}

.category-url-code {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.radiolist-label[for="Form_Garden-dot-EmojiSet"] {
  display: none;
}

.CheckboxCell {
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}

.CheckboxCell .icheckbox {
  margin-right: 0;
}

.main .content .control-panel {
  display: none;
}

@media (max-width: 991px) {
  .main .content .control-panel {
    display: block;
  }
}

table h2, table .analytics-widget-header .title, .analytics-widget-header table .title,
form h2,
form .analytics-widget-header .title, .analytics-widget-header
form .title {
  margin-top: 0;
}

.label-selector .theme-description {
  display: none;
}

body {
  -webkit-transition: all 250ms;
          transition: all 250ms;
}

ul {
  margin-bottom: 0;
}

.main {
  background-color: #fff;
  border-left: 0.0625rem solid #e7e8e9;
  border-right: 0.0625rem solid #e7e8e9;
}

.nestable-collapse ~ .nestable-content > .plank-title {
  padding-left: 2.4375rem;
}

h1, .h1,
h2,
.analytics-widget-header .title, .h2, .hero .hero-title,
h3, .h3,
h4, .h4,
h5, .h5,
h6, .h6 {
  font-family: inherit;
  font-weight: 600;
  line-height: 1.1;
  color: inherit;
}

h1, .h1 {
  font-size: 2rem;
}

h2, .analytics-widget-header .title, .h2, .hero .hero-title {
  font-size: 1.125rem;
}

h3, .h3 {
  font-size: 1rem;
}

h4, .h4 {
  font-size: 1rem;
}

h5, .h5 {
  font-size: 0.875rem;
}

h6, .h6 {
  font-size: 0.875rem;
}

h1, .h1,
h2,
.analytics-widget-header .title, .h2, .hero .hero-title,
h3, .h3,
h4, .h4,
h5, .h5,
h6, .h6 {
  margin-top: 1.125rem;
  margin-bottom: 0.5625rem;
}

p {
  margin-bottom: 0.5625rem;
}

pre {
  margin-bottom: 0;
  white-space: pre-wrap;
}

.full-border {
  display: block;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
}

.content > h1 {
  display: block;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
  font-size: 1.125rem;
  margin-bottom: 0;
  padding-bottom: 1.125rem;
}

.header-block {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  min-height: 3rem;
}

.header-block h1 {
  margin: 0;
  padding: 0.5625rem 0;
  font-size: 1.125rem;
}

.title-block {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.btn-return {
  position: relative;
  padding-left: 0;
  margin-right: 0.5625rem;
}

.btn-return svg {
  position: relative;
  top: 4px;
  width: 1.125rem;
}

.btn-return:hover {
  color: #0291db;
}

.header-menu {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-bottom: 0.0625rem solid #e7e8e9;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.header-menu .header-menu-item {
  padding: 0.71875rem 0;
  margin: 0 2.25rem;
  font-size: 1.125rem;
  line-height: 1;
  color: #949aa2;
  text-decoration: none;
  border-top: 0.1875rem solid transparent;
  border-bottom: 0.1875rem solid transparent;
  cursor: pointer;
}

.header-menu .header-menu-item.active, .header-menu .file-upload input:focus ~ .header-menu-item.file-upload-choose, .file-upload .header-menu input:focus ~ .header-menu-item.file-upload-choose, .header-menu
.file-upload input:focus ~ .header-menu-item.file-upload-browse,
.file-upload .header-menu input:focus ~ .header-menu-item.file-upload-browse, .header-menu
.file-upload input:hover ~ .header-menu-item.file-upload-choose,
.file-upload .header-menu input:hover ~ .header-menu-item.file-upload-choose, .header-menu
.file-upload input:hover ~ .header-menu-item.file-upload-browse,
.file-upload .header-menu input:hover ~ .header-menu-item.file-upload-browse, .header-menu select.header-menu-item:focus, .header-menu .header-menu-item.drop-enabled {
  border-bottom-color: #0291db;
  color: #0291db;
}

.header-menu .header-menu-item:hover {
  color: #0291db;
}

.subheading {
  display: block;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-top: 0.0625rem solid #e7e8e9;
  margin-top: -0.0625rem;
  padding-top: 1.125rem;
  padding-bottom: 0.5625rem;
  margin-bottom: 0;
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 600;
  border-top: 0.0625rem solid #e7e8e9;
}

.subheading-block {
  display: block;
  margin-left: -1.125rem;
  margin-right: -1.125rem;
  padding-left: 1.125rem;
  padding-right: 1.125rem;
  border-top: 0.0625rem solid #e7e8e9;
  margin-top: -0.0625rem;
  padding-top: 1.125rem;
  padding-bottom: 0.5625rem;
  border-top: 0.0625rem solid #e7e8e9;
}

.subheading-block .subheading-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 600;
}

.flag {
  display: inline-block;
  padding-left: .125rem;
  padding-right: .75rem;
  line-height: 0;
  font-size: 0.6875rem;
  color: #fff;
  border-style: solid;
  border-color: #f5296d;
  border-right-color: transparent;
  border-width: 0.5625rem;
  text-transform: uppercase;
}

.badge {
  display: inline-block;
  vertical-align: middle;
  padding: 0 0.28125rem;
  margin-left: 0.28125rem;
  font-size: 0.6875rem;
  background-color: rgba(0, 0, 0, 0.1875);
  color: #fff;
  border-radius: .25rem;
}

.btn-group .badge {
  margin-top: -0.125rem;
}

.badge.badge-outline {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.3275);
  border: 0.0625rem solid rgba(0, 0, 0, 0.25);
}

.badge-primary {
  background-color: #0291db;
}

.badge-primary.badge-outline {
  color: #0291db;
  border-color: #0291db;
}

.badge-secondary {
  background-color: #f5296d;
}

.badge-secondary.badge-outline {
  color: #f5296d;
  border-color: #f5296d;
}

.badge-success {
  background-color: #6bc573;
}

.badge-success.badge-outline {
  color: #6bc573;
  border-color: #6bc573;
}

.badge-info {
  background-color: #a1b8c6;
}

.badge-info.badge-outline {
  color: #a1b8c6;
  border-color: #a1b8c6;
}

.badge-warning {
  background-color: #faae42;
}

.badge-warning.badge-outline {
  color: #faae42;
  border-color: #faae42;
}

.badge-danger {
  background-color: #fa2e1f;
}

.badge-danger.badge-outline {
  color: #fa2e1f;
  border-color: #fa2e1f;
}

.text-primary {
  color: #0291db;
}

.text-secondary {
  color: #f5296d;
}

.text-success {
  color: #6bc573;
}

.text-info {
  color: #a1b8c6;
}

.text-warning {
  color: #faae42;
}

.text-danger {
  color: #fa2e1f;
}

.spacer {
  display: inline-block;
  padding: 0 .25rem;
}

.reverse-link {
  color: #555a62;
}

.reverse-link:hover {
  color: #0291db;
}

.italic {
  font-style: italic;
}

strong,
.strong {
  font-weight: 600;
}

.Info,
.Info2,
.info,
.FeedItem .FeedDescription,
.FeedItem .Date {
  display: block;
  font-size: 0.75rem;
  line-height: 1.3333333333;
  color: #949aa2;
}

.list-reset,
.list-reset ul,
.list-reset ol,
.list-reset ul,
.list-reset ul ul,
.list-reset ul ol,
.list-reset ol,
.list-reset ol ul,
.list-reset ol ol {
  padding-left: 0;
}

.list-reset li,
.list-reset ul li,
.list-reset ol li {
  list-style: none;
}

.truncate, .control-panel .control-panel-list-item-label, .table-checkbox-grid thead th {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.Section-Error,
.Section-Utility {
  padding: 100px;
  margin: 0;
  background: url("images/eyes.png") 30px 30px no-repeat #fff;
}

@media (max-width: 991px) {
  .Section-Error,
  .Section-Utility {
    padding: 10%;
  }
}

.Section-Error {
  background: url("images/error.png") 30px 30px no-repeat #fff;
}

.SplashInfo {
  max-width: 800px;
  color: #555a62;
  font-size: 0.875rem;
}

.SplashInfo h1 {
  font-size: 2rem;
  font-weight: 700;
}

.SplashInfo h2, .SplashInfo .analytics-widget-header .title, .analytics-widget-header .SplashInfo .title {
  font-size: 1.125rem;
  font-weight: 700;
}

.Section-Dashboard.Message.index {
  /* Note: The MessageModule (in /applications/dashboard/modules) wraps all messages
    that it renders in a div with this DismissMessage class. */
  /* Messages */
}

.Section-Dashboard.Message.index .DismissMessage a.Dismiss {
  font-family: arial;
  position: absolute;
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
  color: #777;
  top: -1px;
  right: -1px;
  padding: 1px 3px;
  text-decoration: none;
}

.Section-Dashboard.Message.index .DismissMessage a.Dismiss:hover {
  border: none;
  background: #333;
  color: #fff;
}

.Section-Dashboard.Message.index .DismissMessage strong,
.Section-Dashboard.Message.index .DismissMessage b {
  font-weight: bold;
}

.Section-Dashboard.Message.index .DismissMessage {
  font-size: 13px;
  text-align: left;
  position: relative;
  color: #000;
  border: none;
  border-radius: 1px;
  margin: 10px 0;
  padding: 10px;
}

.Section-Dashboard.Message.index .DismissMessage.Info {
  background: #f3f4f8;
  border: 1px solid #ddd;
}

.Section-Dashboard.Message.index .DismissMessage.Warning {
  background: #ffebe9;
  border: 1px solid #ffccc9;
}

.Section-Dashboard.Message.index .DismissMessage.Box {
  background: #fff8ce;
  border: 1px solid #c5bea4;
  box-shadow: none;
  margin: 0 0 10px;
  /* Make sure that .Box definitions don't override the base DismissMessage margin! */
}

.Section-Dashboard.Message.index .CasualMessage {
  background: #cfecff;
  border: 1px solid #abdafb;
  color: #1e79a7;
}

.Section-Dashboard.Message.index .InfoMessage {
  background: #f6f6f6;
  border: 1px solid #ddd;
}

.Section-Dashboard.Message.index .AlertMessage {
  background: #fff8ce;
  border: 1px solid #deddaf;
}

.Section-Dashboard.Message.index .WarningMessage {
  background: #ffebe9;
  border: 1px solid #ffccc9;
}

.Section-Dashboard.Message.index div.Message {
  line-height: 1.7;
  font-size: 100%;
  word-wrap: break-word;
  margin-top: 5px;
}

.Section-Dashboard.Message.index div.Message h1, .Section-Dashboard.Message.index div.Message h2, .Section-Dashboard.Message.index div.Message .analytics-widget-header .title, .analytics-widget-header .Section-Dashboard.Message.index div.Message .title, .Section-Dashboard.Message.index div.Message h3, .Section-Dashboard.Message.index div.Message p, .Section-Dashboard.Message.index div.Message .P {
  margin: 10px 0;
}

.Section-Dashboard.Message.index div.Message small {
  font-size: 11px;
  color: #777;
}

.Section-Dashboard.Message.index div.Message img.LeftAlign,
.Section-Dashboard.Message.index img.LeftAlign {
  float: left;
  margin: 0 10px 5px 0;
  max-width: 300px;
}

.Section-Dashboard.Message.index div.Message dt {
  font-weight: bold;
  margin: 10px 0 4px;
}

.Section-Dashboard.Message.index div.Message dd {
  margin-left: 30px;
}

.Section-Dashboard.Message.index div.Message dd > p {
  margin-top: 4px;
}

.Section-Dashboard.Message.index div.Message li {
  margin: 5px 0;
}

.Section-Dashboard.Message.index div.Message strong, .Section-Dashboard.Message.index div.Message b {
  font-weight: bold;
}

.Section-Dashboard.Message.index div.Message em,
.Section-Dashboard.Message.index div.Message i {
  font-style: oblique;
}

.Section-Dashboard.Message.index div.Message ul,
.Section-Dashboard.Message.index div.Message ol {
  margin: 1em 0 1em 3em;
}

.Section-Dashboard.Message.index div.Message ol li {
  list-style: decimal !important;
}

.Section-Dashboard.Message.index div.Message ul li {
  list-style: disc !important;
}

.Section-Dashboard.Message.index div.Message img {
  max-width: 100%;
}

.Feed {
  padding-top: 0.5625rem;
}

.FeedItem {
  position: relative;
  padding: 0.75rem 0;
  border-bottom: 0.0625rem solid #f4f6fb;
}

.FeedItem:hover {
  background-color: #f9fdfe;
}

.FeedItem:first-child {
  border-top: 0.0625rem solid #ddd;
}

.FeedItem h2, .FeedItem .analytics-widget-header .title, .analytics-widget-header .FeedItem .title {
  padding: 0;
  margin-top: -0.1875rem;
  margin-left: 0;
  margin-right: 0;
  border-top: 0;
  border-bottom: 0;
  font-weight: 400;
  font-size: 0.8125rem;
  text-overflow: ellipsis;
  margin-bottom: 0.125rem;
  line-height: 1.3333333333;
}

.FeedItem h2 a, .FeedItem .analytics-widget-header .title a, .analytics-widget-header .FeedItem .title a {
  color: #555a62;
}

.FeedItem h2 a:hover, .FeedItem .analytics-widget-header .title a:hover, .analytics-widget-header .FeedItem .title a:hover {
  color: #0291db;
}

.FeedItem .FeedDescription {
  margin-bottom: -0.125rem;
  font-weight: 300;
}

.FeedItem .FeedDescription strong {
  font-weight: 400;
}

.FeedItem .FeedDescription p {
  margin-bottom: 0;
  display: inline-block;
}

.FeedItem .FeedDescription img {
  display: none;
}

.FeedItem .FeedDescription .details p {
  margin-bottom: 0;
  display: initial;
  line-height: 1.4;
}

.FeedItem .FeedDescription .read-less {
  display: block;
  margin-top: 0.1875rem;
}

.FeedItem .FeedDescription,
.FeedItem h2,
.FeedItem .analytics-widget-header .title, .analytics-widget-header
.FeedItem .title {
  margin-right: 102px;
  margin-left: 0.75rem;
}

.FeedItem .Date {
  position: absolute;
  right: 0.75rem;
  width: 102px;
  line-height: 1;
  top: 1.375rem;
  text-align: right;
  font-weight: 300;
}

.ac_input {
  height: 2.25rem !important;
}

.ac_results {
  z-index: 1100;
}

.ac_results,
.ac_results ul,
.ac_results ol {
  padding-left: 0;
}

.ac_results li {
  list-style: none;
}

.Overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: rgba(0, 0, 0, 0.5);
}

.Popup .Footer {
  display: none !important;
}

.Popup .Body {
  overflow-y: hidden !important;
  max-height: inherit !important;
}

.InformMessages .InformMessage .Close span {
  display: none;
}

.InformMessages .InformMessage .InformSprite {
  display: none;
}

.InformMessages .Messages.Errors {
  background: 0;
  padding: 0;
  border: 0;
  margin: 0;
}

.InformMessages .Messages.Errors,
.InformMessages .Messages.Errors ul,
.InformMessages .Messages.Errors ol {
  padding-left: 0;
}

.InformMessages .Messages.Errors li {
  list-style: none;
}

.jcrop-holder {
  direction: ltr;
  text-align: left;
}

.jcrop-vline,
.jcrop-hline {
  background: #ffffff url("images/jcrop.gif");
  font-size: 0;
  position: absolute;
}

.jcrop-vline {
  height: 100%;
  width: 1px !important;
}

.jcrop-vline.right {
  right: 0;
}

.jcrop-hline {
  height: 1px !important;
  width: 100%;
}

.jcrop-hline.bottom {
  bottom: 0;
}

.jcrop-tracker {
  height: 100%;
  width: 100%;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -moz-user-select: none;
   -ms-user-select: none;
       user-select: none;
  -webkit-user-select: none;
}

.jcrop-handle {
  background-color: #333333;
  border: 1px #eeeeee solid;
  width: 7px;
  height: 7px;
  font-size: 1px;
}

.jcrop-handle.ord-n {
  left: 50%;
  margin-left: -4px;
  margin-top: -4px;
  top: 0;
}

.jcrop-handle.ord-s {
  bottom: 0;
  left: 50%;
  margin-bottom: -4px;
  margin-left: -4px;
}

.jcrop-handle.ord-e {
  margin-right: -4px;
  margin-top: -4px;
  right: 0;
  top: 50%;
}

.jcrop-handle.ord-w {
  left: 0;
  margin-left: -4px;
  margin-top: -4px;
  top: 50%;
}

.jcrop-handle.ord-nw {
  left: 0;
  margin-left: -4px;
  margin-top: -4px;
  top: 0;
}

.jcrop-handle.ord-ne {
  margin-right: -4px;
  margin-top: -4px;
  right: 0;
  top: 0;
}

.jcrop-handle.ord-se {
  bottom: 0;
  margin-bottom: -4px;
  margin-right: -4px;
  right: 0;
}

.jcrop-handle.ord-sw {
  bottom: 0;
  left: 0;
  margin-bottom: -4px;
  margin-left: -4px;
}

.jcrop-dragbar.ord-n,
.jcrop-dragbar.ord-s {
  height: 7px;
  width: 100%;
}

.jcrop-dragbar.ord-e,
.jcrop-dragbar.ord-w {
  height: 100%;
  width: 7px;
}

.jcrop-dragbar.ord-n {
  margin-top: -4px;
}

.jcrop-dragbar.ord-s {
  bottom: 0;
  margin-bottom: -4px;
}

.jcrop-dragbar.ord-e {
  margin-right: -4px;
  right: 0;
}

.jcrop-dragbar.ord-w {
  margin-left: -4px;
}

.jcrop-light .jcrop-vline,
.jcrop-light .jcrop-hline {
  background: #ffffff;
  filter: alpha(opacity=70) !important;
  opacity: 0.7 !important;
}

.jcrop-light .jcrop-handle {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  background-color: #000000;
  border-color: #ffffff;
  border-radius: 3px;
}

.jcrop-dark .jcrop-vline,
.jcrop-dark .jcrop-hline {
  background: #000000;
  filter: alpha(opacity=70) !important;
  opacity: 0.7 !important;
}

.jcrop-dark .jcrop-handle {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  background-color: #ffffff;
  border-color: #000000;
  border-radius: 3px;
}

.solid-line .jcrop-vline,
.solid-line .jcrop-hline {
  background: #ffffff;
}

.jcrop-holder img,
img.jcrop-preview {
  max-width: none;
}

.padded, .label-wrap img, div.Messages.Errors {
  margin-top: 1.125rem;
  margin-bottom: 1.125rem;
}

.padded-top {
  margin-top: 1.125rem;
}

.padded-bottom {
  margin-bottom: 1.125rem;
}

.padded-left {
  margin-left: 1.125rem;
}

.padded-right {
  margin-right: 1.125rem;
}

.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.flex > * {
  margin-right: 0.28125rem;
}

.flex > *:last-child {
  margin-right: 0;
}

.flex-wrap {
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}

.shrink {
  -webkit-flex-shrink: 1;
      -ms-flex-negative: 1;
          flex-shrink: 1;
}

.grow {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.no-shrink {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}

.no-grow {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.Hidden,
.hidden {
  display: none;
}

.progress {
  background: url("images/progress.gif") center center no-repeat;
}

.foggy {
  position: relative;
  -webkit-filter: blur(2px);
}

.foggy input {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.foggy::before {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  opacity: .5;
  background-color: #fff;
  z-index: 1;
  content: '';
}
/*# sourceMappingURL=admin.css.map */