.dropdown-menu {
    position: absolute;
    z-index: 9999999;
    display: none;
}

.dropdown-menu UL {
    min-width: 160px;
    list-style: none;
    background: #FFF;
    border: solid 1px #DDD;
    border: solid 1px rgba(0, 0, 0, .2);
    border-radius: 6px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
    overflow: visible;
    padding: 4px 0;
    margin: 0;
}

.dropdown-menu.has-tip {
    margin-top: 8px;
}

.dropdown-menu.has-tip:before {
    position: absolute;
    top: -6px;
    left: 9px;
    content: '';
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #CCC;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    display: inline-block;
}

.dropdown-menu.has-tip.anchor-right:before {
    left: auto;
    right: 9px;
}

.dropdown-menu.has-tip:after {
    position: absolute;
    top: -5px;
    left: 10px;
    content: '';
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #FFF;
    display: inline-block;
}

.dropdown-menu.has-tip.anchor-right:after {
    left: auto;
    right: 10px;
}

.dropdown-menu.has-scroll UL {
    max-height: 358px;
    overflow: auto;
}

.dropdown-menu LI {
    list-style: none;
    padding: 0;
    margin: 0;
    line-height: 18px;
}

.dropdown-menu LI > A,
.dropdown-menu LABEL {
    display: block;
    color: #555;
    text-decoration: none;
    line-height: 18px;
    padding: 3px 15px;
    white-space: nowrap;
}

.dropdown-menu LI > A:hover,
.dropdown-menu LABEL:hover {
    background-color: #08C;
    color: #FFF;
    cursor: pointer;
}

.dropdown-menu .divider {
    font-size: 1px;
    border-top: solid 1px #E5E5E5;
    padding: 0;
    margin: 10px 1px;
}

/* Icon Examples - icons courtesy of http://p.yusukekamiyamane.com/ */
.dropdown-menu.has-icons LI > A {
    padding-left: 30px;
    background-position: 8px center;
    background-repeat: no-repeat;
}

.dropdown-menu .undo A {
    background-image: url(icons/arrow-curve-180-left.png);
}

.dropdown-menu .redo A {
    background-image: url(icons/arrow-curve.png);
}

.dropdown-menu .cut A {
    background-image: url(icons/scissors.png);
}

.dropdown-menu .copy A {
    background-image: url(icons/document-copy.png);
}

.dropdown-menu .paste A {
    background-image: url(icons/clipboard.png);
}

.dropdown-menu .delete A {
    background-image: url(icons/cross-script.png);
}
