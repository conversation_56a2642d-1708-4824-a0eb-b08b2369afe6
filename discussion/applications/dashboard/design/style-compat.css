@charset "UTF-8";
/*!
 * <AUTHOR> <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/* -------------------------------------------------------------- *    Utility Variables
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Globals
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    States
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Password Strength Colors
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Third Party Colors
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Typography
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Animation
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Icons
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    User Photo
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Layout
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Components
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Widgets
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Form Elements
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Overlays (Menus, flyouts and Modals)
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Buttons
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *      Meta
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *      Thumbnails
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Separators
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    SelectBox
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    DropDown
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Header
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Compact Me Box
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Footer
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    user content
\* -------------------------------------------------------------- */
/* -------------------------------------------------------------- *    Etc
\* -------------------------------------------------------------- */
/*!
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/* ------------------------------- *    Third Party Colors
\* ------------------------------- */
/*!
 * <AUTHOR> Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
@-webkit-keyframes spinnerLoader {
  0% {
    -webkit-transform: rotate(73deg);
            transform: rotate(73deg);
  }
  100% {
    -webkit-transform: rotate(433deg);
            transform: rotate(433deg);
  }
}
@keyframes spinnerLoader {
  0% {
    -webkit-transform: rotate(73deg);
            transform: rotate(73deg);
  }
  100% {
    -webkit-transform: rotate(433deg);
            transform: rotate(433deg);
  }
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
em {
  font-style: italic;
}

.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.isCentered {
  text-align: center;
}

.isHidden {
  display: none !important;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/** Hides the image cropper on mobile themes. This image cropper is not mobile compatible. */
body.isMobile .box-crop .box-source {
  display: none;
}

body.isMobile .change-picture .change-picture-instructions {
  display: none;
}

@media (max-width: 600px) {
  .box-crop .box-source {
    display: none;
  }
  .change-picture .change-picture-instructions {
    display: none;
  }
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
button.Close {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
}

.Close-x {
  color: #000;
  font-family: arial;
  font-size: 22px;
  font-weight: bold;
  padding: 0;
}

.buttonIcon {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  cursor: pointer;
  background: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 36px;
  width: 36px;
  min-width: 36px;
  padding: 0;
  color: #74787e;
}

.buttonIcon:visited {
  color: #74787e;
}

.buttonIcon .icon {
  display: block;
}

.buttonIcon.isOpen {
  color: #0291db;
}

.buttonIcon:focus, .buttonIcon:hover {
  color: #0291db;
}

.icon-close {
  display: block;
  margin: auto;
  width: 12px;
  height: 12px;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.categoryList-heading + .DataTableWrap > .categoryList-genericHeading {
  display: none;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.Title-Icon {
  display: inline-block;
  margin-left: .15em;
  margin-right: .15em;
}

.Title-PrivateIcon {
  height: 0.8em;
  width: .8em;
  vertical-align: -.05;
  opacity: 0.4;
}

.Title-SecretIcon {
  height: 0.8em;
  width: 1.1em;
  vertical-align: -.1em;
  opacity: 0.4;
}

.authenticateUserCol {
  padding: 0 26px;
  margin: auto;
  width: 386px;
  max-width: 100%;
  box-sizing: border-box;
}

.authenticateUserCol .pageTitle {
  text-align: center;
  margin-bottom: 18px;
}

.authenticateUserCol .button.button-fullWidth {
  margin-bottom: 18px;
}

.authenticateUserCol .button.button-fullWidth.button-sso {
  margin-bottom: 12px;
}

.authenticateUserCol .icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  vertical-align: middle;
}

.authenticateUserCol .iconLarge {
  width: 32px;
  height: 32px;
}

.authenticateUser-photoCrop,
.authenticateUser-imageWrap {
  display: block;
  width: 100%;
  max-width: 117px;
}

.authenticateUser-photoCrop {
  overflow: hidden;
  border-radius: 50%;
}

.authenticateUser-imageWrap {
  position: relative;
  margin: auto;
}

.authenticateUser-icon {
  position: absolute;
  bottom: 0;
  right: 0;
  display: block;
  width: 42px;
  height: 42px;
  background-color: #fff;
  border-style: solid;
  border-width: 3px;
  border-color: #fff;
  border-radius: 50%;
}

.authenticateUser-photo {
  display: block;
  max-width: 100%;
  width: 100%;
  height: auto;
}

.authenticateUser-arrowDown {
  display: block;
  margin: 18px auto;
  opacity: 0.6;
}

.authenticateUser-name {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.authenticateUser-userName {
  font-size: 12px;
  opacity: 0.6;
  text-align: center;
}

.rememberMeAndForgot {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 12px;
}

.rememberMeAndForgot a {
  font-size: inherit;
  color: #0291db;
}

.rememberMeAndForgot-rememberMe {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding-right: 6px;
}

.authenticateUser-divider {
  text-align: center;
}

.authenticateUser-footer {
  text-align: center;
}

.authenticateUser-ssoLabel {
  display: inline;
  margin-left: 12px;
}

.button.Button.Primary.buttonCTA, .button.Button.button-sso {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-image: none;
  box-sizing: border-box;
  text-decoration: none !important;
}

.button.Button.button-sso {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

.button-ssoLabel {
  margin-left: 12px;
  margin-right: 12px;
  font-size: 14px;
  font-weight: normal;
  text-shadow: none;
}

.ssoMethod-icon {
  width: 24px;
  height: auto;
}

.ssoMethod-icon.ssoMethod-iconSpacer {
  display: block;
  visibility: hidden;
  height: 24px;
}

.button-ssoContents {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
}

.ssoMethods {
  margin-bottom: 18px;
}

.authenticateUser-paragraph {
  word-break: break-word;
}

.authenticateUser-paragraph.isError {
  color: #ff3933;
}

.followButton {
  white-space: nowrap;
  font-size: 10px;
  text-transform: uppercase;
  vertical-align: middle;
  font-weight: 400;
  padding: 0 5px;
  text-decoration: none;
}

.followButton.isFollowing:not(:hover) {
  opacity: 0.7;
}

.followButton-icon {
  width: 14px;
  height: 14px;
  margin-right: 5px;
  vertical-align: sub;
}

.selectBox {
  display: inline-block;
  position: relative;
  white-space: nowrap;
  vertical-align: middle;
}

.PageControls .selectBox {
  margin-right: 15px;
  float: left;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.selectBox-label {
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  margin-right: 5px;
}

.selectBox-main {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.selectBox-content {
  display: none;
  left: 100%;
  margin-left: -29px;
  margin-top: 10px;
}

.selectBox-toggle {
  position: relative;
  display: inline-block;
  vertical-align: inherit;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  white-space: nowrap;
  max-height: 100%;
}

.selectBox-toggle:active {
  text-decoration: none;
}

.selectBox-item {
  position: relative;
  display: block;
}

.selectBox-item .dropdown-menu-link.selectBox-link {
  position: relative;
  padding-left: 30px;
}

.selectBox-item.isActive .selectBox-link {
  pointer-events: none;
  cursor: default;
}

.selectBox-link {
  display: block;
  box-sizing: border-box;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-decoration: none !important;
  text-align: left;
}

.selectBox-selectedIcon {
  position: absolute;
  left: 5px;
  top: 0;
  bottom: 0;
  margin: auto 0;
  width: 18px;
  height: 18px;
}

.menu-separator {
  background: none;
  border-bottom: 1px solid #eeefef;
  margin: 6px 0;
}

.menu-separator hr {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.PageControls {
  position: relative;
}

body.Categories.isMobile .PageControls.Top {
  display: block;
}

.ButtonGroup.discussion-sort-filter-module {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.inputBlock {
  display: block;
  margin-bottom: 24px;
}

.inputBlock .checkbox {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.inputBlock.inputBlock-tighter {
  margin-top: -12px;
}

.inputBlock.hasError .inputText {
  border-color: #ff3933;
  background-color: #ffebeb;
  color: #ff3933;
}

.inputBlock ::-ms-clear {
  display: none;
}

.inputBlock-errors,
.inputBlock-labelAndDescription {
  display: block;
}

.inputBlock-labelText {
  display: block;
  font-weight: 600;
  margin-bottom: 6px;
  line-height: 1.428571429;
}

.InputBox.inputText {
  width: 100%;
}

.inputBlock-error {
  word-break: break-word;
  display: block;
  color: #ff3933;
  line-height: 1.428571429;
  font-size: 12px;
  margin-top: 3px;
}

.inputBlock-error:first-letter {
  text-transform: capitalize;
}

.inputBlock-error + .inputBlock-error {
  margin-top: 6px;
}

.inputBlock-inputWrap {
  display: block;
}

.inputBlock-labelNote {
  display: block;
  font-size: 12px;
  line-height: 1.428571429;
  opacity: .6;
}

.inputBlock-labelNote + .inputBlock-inputWrap {
  margin-top: 6px;
}

.inputBlock-inputText {
  box-sizing: border-box;
}

.checkbox-box {
  display: none;
}

label.checkbox input.checkbox-input {
  float: none;
  margin: 0;
}

.checkbox-label {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  margin-left: 6px;
  line-height: inherit;
}

.button.button-fullWidth {
  width: 100%;
  max-width: 100%;
}

::-moz-focus-inner {
  border: 0;
}

.passwordStrength-bar {
  position: relative;
  width: 100%;
}

.StrengthText.passwordStrength-gaugeLabel {
  margin-right: 0;
}

.passwordStrength.PasswordStrength {
  padding: 0;
}

.pageHeading {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}

.pageHeading .pageHeading-title {
  display: block;
  margin-top: 0;
  margin-bottom: 0;
}

.pageHeading-main {
  position: relative;
  display: block;
  max-width: 100%;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.pageHeading-actions {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
  max-width: 100%;
}

.stickyHeaderWrap {
  position: relative;
  display: block;
  width: 100%;
}

.stickyHeader {
  position: relative;
  width: 100%;
  left: 0;
  display: block;
  z-index: 100;
}

.stickyHeader.isAtTop {
  position: relative;
}

.stickyHeader.isScrollingDown {
  position: absolute;
}

/*!
 * <AUTHOR> Charron <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/*!
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/*!
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.accessibility-jumpTo {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.richEditor {
  color: #555a62;
  padding-left: 36px;
}

.richEditor blockquote {
  display: block;
  margin: 0;
}

.richEditor .richEditorParagraphMenu {
  left: 0;
}

.userContent {
  line-height: 1.5;
}

.userContent.Message {
  margin-top: 14px;
}

.userContent h1,
.userContent h2,
.userContent h3,
.userContent h4,
.userContent h5,
.userContent h6 {
  line-height: 1.25;
  color: #555a62;
}

.userContent > *:not(.emoji):not(:last-child):not(br) {
  margin-bottom: 14px;
}

.userContent > *:not(.emoji):first-child:not(br) {
  margin-top: -0.25em !important;
}

.userContent,
.userContent h1,
.userContent h2,
.userContent h3,
.userContent h4,
.userContent h5,
.userContent h6,
.userContent span,
.userContent div,
.userContent td,
.userContent th,
.userContent a,
.userContent p {
  word-break: break-word;
  text-overflow: ellipsis;
}

.userContent .CommentHeader {
  margin-bottom: 14px;
}

.userContent li,
.userContent li *:first-child {
  margin-top: 0;
}

.userContent li,
.userContent li *:last-child {
  margin-bottom: 0;
}

.userContent .metas {
  display: block;
  line-height: 1.5;
  color: #6f737a;
  width: -webkit-calc(100% + 12px);
  width: calc(100% + 12px);
  overflow: hidden;
  text-align: left;
  margin-left: -6px;
  margin-right: 6px;
}

.userContent .metas.isFlexed {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.userContent .meta {
  display: inline-block;
  margin: 0 6px;
  font-size: 12px;
  color: #6f737a;
}

.userContent .meta .meta {
  margin: 0;
}

.richEditor-menu {
  box-shadow: 0 1px 3px 0 rgba(85, 90, 98, 0.3);
  background-color: #fff;
  border-radius: 6px;
  border-color: #d6d7d9;
  border-style: solid;
  border-width: 1px;
}

.richEditor-button {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  cursor: pointer;
}

body.hasRichEditor .richEditor ~ .TextBoxWrapper,
body.hasRichEditor .CommentForm .PreviewButton,
body.hasRichEditor #Form_Preview {
  display: none !important;
}

@media (min-width: 700px) {
  body.hasRichEditor.Section-PostDiscussion #DiscussionForm select {
    max-width: 100%;
  }
  body.hasRichEditor.Section-PostDiscussion #DiscussionForm .FormWrapper {
    padding-left: 36px;
    padding-right: 36px;
  }
  body.hasRichEditor.Section-PostDiscussion #DiscussionForm .FormWrapper .richEditor {
    padding-left: 0;
    padding-right: 0;
  }
  body.hasRichEditor .CommentForm .CommentFormWrap .Form-HeaderWrap {
    display: none;
  }
  body.hasRichEditor .CommentForm .CommentFormWrap .FormWrapper {
    padding-right: 0;
    padding-left: 0;
  }
  body.hasRichEditor .CommentForm .richEditor {
    padding-left: 36px;
    padding-right: 36px;
  }
  body.hasRichEditor .CommentForm .richEditor .richEditor {
    padding-left: 0;
    padding-right: 0;
  }
  body.hasRichEditor .CommentForm .Buttons {
    padding-right: 36px;
  }
  body.hasRichEditor #EventAddEditForm .Form-HeaderWrap,
  body.hasRichEditor #GroupForm .Form-HeaderWrap,
  body.hasRichEditor #MessageForm .Form-HeaderWrap,
  body.hasRichEditor #ConversationForm .Form-HeaderWrap {
    display: none;
  }
  body.hasRichEditor #EventAddEditForm .FormWrapper,
  body.hasRichEditor #GroupForm .FormWrapper,
  body.hasRichEditor #MessageForm .FormWrapper,
  body.hasRichEditor #ConversationForm .FormWrapper {
    padding-left: 36px;
    padding-right: 36px;
  }
  body.hasRichEditor #EventAddEditForm .FormWrapper .richEditor,
  body.hasRichEditor #GroupForm .FormWrapper .richEditor,
  body.hasRichEditor #MessageForm .FormWrapper .richEditor,
  body.hasRichEditor #ConversationForm .FormWrapper .richEditor {
    padding-right: 0;
    padding-left: 0;
  }
  body.hasRichEditor.Profile .FormWrapper.FormWrapper-Condensed {
    padding-right: 36px;
    padding-left: 0;
  }
  body.hasRichEditor.Profile .FormWrapper.FormWrapper-Condensed .Activity .Buttons {
    padding-left: 36px;
  }
  body.hasRichEditor.Section-Conversation .CommentForm .Buttons {
    padding-right: 0;
  }
  body.hasRichEditor .Popup.hasRichEditor .Border {
    width: 100%;
    box-sizing: border-box;
  }
  body.hasRichEditor .Popup.hasRichEditor .Content {
    margin-top: 10px;
  }
  body.hasRichEditor .Popup.hasRichEditor .Content > h1 {
    padding-left: 36px;
  }
  body.hasRichEditor .Popup.hasRichEditor .FormWrapper {
    background: none;
    padding-top: 3px;
    padding-left: 36px;
    padding-right: 36px;
  }
  body.hasRichEditor .Popup.hasRichEditor .richEditor {
    padding-left: 0;
  }
}

.richEditor-text {
  overflow: visible;
  padding-top: 12px;
  padding-right: 12px;
  padding-bottom: 12px;
  padding-left: 12px;
  border-radius: 6px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.richEditor-text:focus {
  box-shadow: 0 0 0 1px rgba(2, 145, 219, 0.6);
}

body.isMobile .embedDialogue {
  position: static;
}

body.isMobile .u-richEditorHiddenOnMobile {
  display: none !important;
}

body.isMobile .richEditor {
  padding-left: 0;
}

body.isMobile .richEditor-menu.richEditorFlyout {
  left: 0;
  width: -webkit-calc(100% + 4px);
  width: calc(100% + 4px);
}

body.isMobile .richEditor-embedBarMobileBorder {
  display: block;
}

body.isMobile .FormWrapper {
  padding-left: 18px;
  padding-right: 18px;
}

body.isMobile .richEditorParagraphMenu {
  display: none !important;
}

body.isMobile .Popup.hasRichEditor .Border {
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 700px) {
  .embedDialogue {
    position: static;
  }
  .u-richEditorHiddenOnMobile {
    display: none !important;
  }
  .richEditor {
    padding-left: 0;
  }
  .richEditor-menu.richEditorFlyout {
    left: 0;
    width: -webkit-calc(100% + 4px);
    width: calc(100% + 4px);
  }
  .richEditor-embedBarMobileBorder {
    display: block;
  }
  .FormWrapper {
    padding-left: 18px;
    padding-right: 18px;
  }
  .richEditorParagraphMenu {
    display: none !important;
  }
  .Popup.hasRichEditor .Border {
    width: 100%;
    box-sizing: border-box;
  }
}

.richEditorFlyout {
  bottom: 100%;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedResponsive {
  text-align: center;
}

.embedResponsive-initialLink {
  text-align: start;
  padding: 14px;
}

.embedExternal {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  width: 640px;
  position: relative;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  box-sizing: border-box;
}

.embedExternal-content {
  position: relative;
  margin: auto;
  width: 640px;
  max-width: 100%;
  text-align: center;
  box-sizing: border-box;
}

.embedExternal-content iframe {
  display: block;
  box-sizing: border-box !important;
}

.embedExternal-content > * {
  margin: auto !important;
}

.embedExternal-ratio {
  position: relative;
  display: block;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}

.embedLinkLoader {
  text-align: left;
  display: block;
}

.embedLinkLoader-failIcon {
  display: inline-block;
  height: 1em;
  width: 1em;
  margin: 0;
  vertical-align: -0.14em;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.embedLinkLoader-loader {
  -webkit-transition-duration: opacity;
          transition-duration: opacity;
  -webkit-transition-timing-function: ease-out;
          transition-timing-function: ease-out;
  display: block;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  border-top: 3px solid #0291db;
  border-right: 3px solid rgba(2, 145, 219, 0.3);
  border-bottom: 3px solid rgba(2, 145, 219, 0.3);
  border-left: 3px solid rgba(2, 145, 219, 0.3);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-animation: spinnerLoader 0.7s infinite ease-in-out;
          animation: spinnerLoader 0.7s infinite ease-in-out;
  display: inline-block;
  vertical-align: -0.14em;
}

.embedLinkLoader-loader::after {
  border-radius: 50%;
  width: 1em;
  height: 1em;
}

.embedLinkLoader-link {
  display: inline-block;
  white-space: normal;
  padding: 2px 4px;
}

.richEditor .embedExternal {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.richEditor .embedExternal-content,
.richEditor .embedLinkLoader-link {
  cursor: pointer;
}

.richEditor .embedExternal-content:after,
.richEditor .embedLinkLoader-link:after {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  content: "";
  -webkit-transition: 0.2s ease box-shadow;
          transition: 0.2s ease box-shadow;
}

.richEditor .embedExternal-content:hover:after,
.richEditor .embedLinkLoader-link:hover:after {
  box-shadow: 0 0 0 2px rgba(2, 145, 219, 0.5) inset;
}

.richEditor .embedExternal-content:focus:after,
.richEditor .embedLinkLoader-link:focus:after {
  box-shadow: 0 0 0 2px #0291db inset;
}

.richEditor .embedExternal-content > *,
.richEditor .embedLinkLoader-link > * {
  pointer-events: none;
}

.embed-focusableElement {
  cursor: pointer;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedVideo .embedExternal-content {
  position: relative;
  display: block;
  width: 640px;
  max-width: 100%;
  margin: auto;
}

.embedVideo .embedExternal-content:focus .embedVideo-playIcon, .embedVideo .embedExternal-content:hover .embedVideo-playIcon {
  -webkit-transform: scale(1.1);
      -ms-transform: scale(1.1);
          transform: scale(1.1);
}

.richEditor .embedVideo .embedExternal-content:focus .embedVideo-playIcon, .richEditor .embedVideo .embedExternal-content:hover .embedVideo-playIcon {
  -webkit-transform: scale(1);
      -ms-transform: scale(1);
          transform: scale(1);
}

.embedVideo-iframe,
.embedVideo-thumbnail {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.embedVideo-thumbnail {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  height: auto;
  width: 100%;
  background-color: black;
}

@supports (object-fit: cover) {
  .embedVideo-thumbnail {
    object-fit: cover;
    object-position: center;
    height: 100%;
  }
}

.embedVideo-scrim {
  content: "";
  display: block;
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));
}

.embedVideo-ratio {
  position: relative;
  display: block;
  background-color: #000000;
  margin: 0;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}

.embedVideo-ratio.is21by9 {
  padding-top: 42.8571428571%;
}

.embedVideo-ratio.is16by9 {
  padding-top: 56.25%;
}

.embedVideo-ratio.is4by3 {
  padding-top: 75%;
}

.embedVideo-ratio.is1by1 {
  padding-top: 100%;
}

.embedVideo-playButton {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
  overflow: hidden;
}

.embedVideo-playIcon {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 80px;
  height: 80px;
  color: white;
}

.embedVideo-playIconPath {
  stroke: rgba(0, 0, 0, 0.1);
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedImage .embedExternal-content {
  width: auto;
  max-width: 640px;
  display: inline-block;
}

@media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
  .embedImage .embedExternal-content {
    width: 100%;
  }
}

.Message img .embedImage-img,
.embedImage-img {
  display: block;
  position: relative;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedText {
  font-size: 14px;
}

.embedText-content {
  display: block;
  background: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(85, 90, 98, 0.3);
}

.embedText-content, .embedText-content:hover, .embedText-content:focus {
  color: inherit;
  text-decoration: none !important;
}

.embedText-body {
  display: block;
  position: relative;
  overflow: hidden;
  text-align: left;
  pointer-events: initial !important;
}

.embedText-main {
  padding: 14px;
  box-sizing: border-box;
}

.embedText-header {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  font-size: 14px;
  line-height: 1.25;
}

.embedText-titleLink {
  margin: 0;
  color: inherit;
}

.embedText .embedText-titleLink,
.embedText .embedText-title {
  margin-top: 0;
  margin-bottom: 4px;
  display: block;
  width: 100%;
  padding: 0;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.25;
  color: #555a62;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.richEditor .embedText-titleLink {
  pointer-events: none;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedLink-image {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  bottom: 0;
  margin-top: auto;
  margin-bottom: auto;
  width: 225px;
}

@supports (object-fit: cover) {
  .embedLink-image {
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

.embedLink-image + .embedText-main {
  margin-left: auto;
  width: -webkit-calc(100% - 225px);
  width: calc(100% - 225px);
  border-left-style: solid;
  border-left-width: 1px;
  border-left-color: rgba(85, 90, 98, 0.1);
}

@media (max-width: 550px) {
  .embedLink-image {
    position: relative;
    width: 100%;
    padding-top: 0;
  }
  .embedLink-image + .embedText-main {
    width: 100%;
    border-right: none;
  }
}

.embedLink-source {
  font-size: 12px;
  color: #6f737a;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
}

.embedLink-excerpt {
  color: #555A62;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-width: 100%;
  font-size: 14px;
  margin-top: 4px;
  line-height: 1.4;
  max-height: 58.8px;
  white-space: normal;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedQuote {
  display: block;
  width: 100%;
}

.embedQuote-content {
  display: block;
  width: 100%;
}

.embedQuote-header {
  position: relative;
}

.embedQuote-userLink {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}

.embedQuote-userName {
  margin: 0 0 0 4px;
  padding: 0;
  color: #6f737a;
  font-weight: 700;
  font-size: 12px;
}

.embedQuote-userPhoto {
  display: block;
  width: 24px;
  height: 24px;
  border-radius: 6px;
}

.embedQuote-excerpt {
  white-space: normal;
}

.embedQuote-collapseButton {
  position: absolute;
  top: 0;
  right: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: none;
  border: none;
  cursor: pointer;
  margin-left: auto;
  height: 36px;
  width: 36px;
  opacity: 0.6;
  padding: 0;
}

.embedQuote-collapseButton:active {
  opacity: 1;
}

.embedQuote-collapseButton:focus {
  opacity: 1;
  outline: none;
}

.embedQuote-collapseButton:hover {
  opacity: 1;
  cursor: pointer;
}

.embedQuote-title {
  max-width: -webkit-calc(100% - 36px);
  max-width: calc(100% - 36px);
}

.embedQuote-chevronUp,
.embedQuote-chevronDown {
  width: 24px;
  height: 24px;
  display: inline-block;
  vertical-align: .2em;
}

.embedQuote-dateTime {
  font-size: 12px;
  color: #6f737a;
  margin-left: 8px;
}

.embedQuote-header {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  padding-top: 14px;
  padding-left: 14px;
  padding-right: 14px;
  padding-bottom: 0;
}

.embedQuote-body {
  display: block;
  margin: 0;
}

.embedQuote-metaLink {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedLoader-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  -webkit-flex-basis: 24px;
      -ms-flex-preferred-size: 24px;
          flex-basis: 24px;
}

.embedLoader {
  display: block;
  position: relative;
}

.embedLoader-box {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: #FFFFFF;
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.3);
  width: 135px;
  height: 135px;
  margin: auto;
  overflow: hidden;
}

.embedLoader-loader {
  -webkit-transition-duration: opacity;
          transition-duration: opacity;
  -webkit-transition-timing-function: ease-out;
          transition-timing-function: ease-out;
  display: block;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border-top: 3px solid #0291db;
  border-right: 3px solid rgba(2, 145, 219, 0.3);
  border-bottom: 3px solid rgba(2, 145, 219, 0.3);
  border-left: 3px solid rgba(2, 145, 219, 0.3);
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-animation: spinnerLoader 0.7s infinite ease-in-out;
          animation: spinnerLoader 0.7s infinite ease-in-out;
}

.embedLoader-loader::after {
  border-radius: 50%;
  width: 42px;
  height: 42px;
}

.embedLoader-error {
  box-shadow: 0 1px 3px 0 rgba(85, 90, 98, 0.3);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  list-style: none;
  margin: 0;
  padding: 15px;
  background: #ffebeb;
  -webkit-transition: 0.2s ease box-shadow;
          transition: 0.2s ease box-shadow;
  cursor: pointer;
}

.embedLoader-error:hover {
  box-shadow: 0 0 0 2px rgba(2, 145, 219, 0.5) inset;
}

.embedLoader-error:focus {
  box-shadow: 0 0 0 2px #0291db inset;
}

.embedLoader-error + .embedLoader-error {
  margin-top: 12px;
}

.embedLoader-error .embedLoader-warningIcon {
  -webkit-align-self: flex-start;
      -ms-flex-item-align: start;
          align-self: flex-start;
}

.embedLoader-error .closeButton {
  opacity: 0.7;
  width: 24px;
  height: 24px;
  -webkit-flex-basis: 24px;
      -ms-flex-preferred-size: 24px;
          flex-basis: 24px;
  -webkit-align-self: flex-start;
      -ms-flex-item-align: start;
          align-self: flex-start;
  margin: 0 0 0 auto;
  cursor: pointer;
  position: relative;
}

.embedLoader-error .closeButton, .embedLoader-error .closeButton:hover, .embedLoader-error .closeButton:focus {
  background: none !important;
  border: 0 !important;
  color: inherit !important;
  line-height: normal;
  overflow: hidden;
  padding: 0 !important;
  -webkit-appearance: button !important;
  /* for input */
  -webkit-user-select: none !important;
  /* for button */
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.embedLoader-error .closeButton:hover, .embedLoader-error .closeButton:focus {
  opacity: 1;
}

.embedLoader-error .closeButton .embedLoader-icon {
  position: absolute;
  top: 0;
  left: 0;
}

.userContent .embedLoader-errorMessage {
  max-width: -webkit-calc(100% - 72px);
  max-width: calc(100% - 72px);
  padding-left: 24px;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedGetty {
  position: relative;
  display: block;
  max-width: 100%;
  margin: auto;
}

.embedGetty .getty {
  display: block !important;
  background-color: white;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedGiphy {
  position: relative;
  display: block;
  max-width: 100%;
  margin: auto;
}

.embedGiphy .embedGiphy-iframe {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedInstagram {
  width: auto;
}

.instagram-media {
  margin-bottom: 0 !important;
  width: 100% !important;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedImgur .embedExternal-content {
  width: auto;
}

.embedImgur iframe {
  max-width: 100%;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedTwitter .embedExternal-content {
  border-radius: 4px;
  width: auto;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.twitter-tweet {
  margin: 0 auto !important;
  min-width: 100% !important;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedSoundcloud {
  position: relative;
  width: 640px;
}

/*!
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.embedInstagram {
  width: auto;
}

.instagram-media {
  margin-bottom: 0 !important;
  width: 100% !important;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
@-webkit-keyframes fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.collapsableContent {
  -webkit-transition: max-height 0.2s ease;
          transition: max-height 0.2s ease;
}

.collapsableContent *:last-child {
  margin-bottom: 0;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.atMentionList {
  position: absolute;
  width: 200px;
  -webkit-transform: translateY(6px);
      -ms-transform: translateY(6px);
          transform: translateY(6px);
}

.atMentionList-suggestion {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow-x: hidden;
  max-width: 100%;
  position: relative;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: 0;
  padding: 0;
  background: none;
  width: 100%;
  text-align: left;
}

.atMentionList-items.atMentionList-items {
  display: block;
  padding: 3px 0;
  overflow: auto;
  max-height: 270px;
}

.atMentionList-items.isHidden {
  display: none;
}

.atMentionList-item.atMentionList-item {
  margin-bottom: 0;
}

.atMentionList-item.isActive .atMentionList-suggestion {
  background-color: #e1f2fb;
}

.atMentionList-suggestion {
  width: 100%;
  cursor: pointer;
}

.atMentionList-user,
.atMentionList-noResults {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  line-height: 30px;
  padding-top: 3px;
  padding-right: 6px;
  padding-bottom: 3px;
  padding-left: 6px;
}

.atMentionList-photoWrap {
  margin-right: 10px;
}

.atMentionList-photo {
  width: 30px;
  height: 30px;
}

.atMentionList-userName {
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: -webkit-calc(100% - 40px);
  max-width: calc(100% - 40px);
}

.atMentionList-mark {
  padding: 0;
  font-weight: 600;
}

.atMentionList-photo {
  display: block;
}

.atMention {
  color: inherit;
  font-weight: 600;
  -webkit-user-select: all;
     -moz-user-select: all;
      -ms-user-select: all;
          user-select: all;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.blockquote {
  display: block;
  margin: 0;
  padding-top: 0;
  padding-left: 18px;
  padding-right: 0;
  padding-bottom: 0;
  padding: 3px 3px 3px 18px;
  border-left: solid #d8d9db 6px;
  box-sizing: border-box;
  width: 100%;
  vertical-align: middle;
  color: #888c91;
}

.blockquote-content > *:first-child {
  margin-top: 0;
}

.blockquote-content > *:last-child {
  margin-bottom: 0;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.richEditor-text ul,
.richEditor-text ol {
  margin: 1em 0 1em 3em;
}

.richEditor-text ol li {
  list-style: decimal !important;
}

.richEditor-text ul li {
  list-style: disc !important;
}

.richEditor-text li,
.richEditor-text.richEditor-text ul li {
  margin: 5px 0;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.accessibility-jumpTo {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.accessibility-jumpTo:focus {
  opacity: 1;
  outline: none;
  position: absolute;
  top: 50px;
  left: 0;
  text-align: left;
  background-color: white;
  color: black;
  display: block;
  font-size: 14px;
  clip: auto;
  margin: 0;
  height: auto;
  padding: 0 12px;
  width: 100%;
  z-index: 2;
  -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
          transform: translateY(-100%);
}

.accessibility-jumpTo:hover {
  opacity: 1;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.spoiler {
  border-radius: 6px;
  background-color: #f7f7f7;
}

.spoiler .spoiler-chevronUp {
  display: none;
}

.spoiler .spoiler-content {
  display: none;
}

.spoiler.isShowingSpoiler .spoiler-content {
  display: block;
}

.spoiler.isShowingSpoiler .spoiler-chevronUp {
  display: inline-block;
}

.spoiler.isShowingSpoiler .spoiler-chevronDown {
  display: none;
}

.spoiler-chevronUp,
.spoiler-chevronDown {
  width: 24px;
  height: 24px;
  display: inline-block;
  vertical-align: .2em;
}

.button-spoiler {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
  max-width: 100%;
  font-size: 16px;
  min-height: 48px;
  padding: 0 14px;
  outline: 0;
  cursor: pointer;
  text-align: center;
}

.button-spoiler {
  border: 0;
  border-radius: 0;
  color: #555a62;
  background-color: transparent;
  text-align: left;
}

.button-spoiler:active .spoiler-chevron {
  opacity: 1;
}

.button-spoiler:focus .spoiler-chevron {
  opacity: 1;
  outline: none;
}

.button-spoiler:hover .spoiler-chevron {
  opacity: 1;
  cursor: pointer;
}

.spoiler-warningMain {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  box-sizing: border-box;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0 24px;
  width: 100%;
  line-height: 1;
}

.spoiler-icon {
  position: relative;
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

.spoiler-chevron {
  position: absolute;
  display: block;
  top: 0;
  right: 14px;
  bottom: 0;
  max-height: 100%;
  max-width: 100%;
  margin: auto 0;
  width: 24px;
  height: 24px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  opacity: 0.6;
}

.spoiler-warningAfter,
.spoiler-warningBefore {
  line-height: 24px;
  margin: 0;
  padding: 0;
}

.spoiler-warningBefore {
  margin-right: 6px;
}

.spoiler-content {
  padding: 0 14px 14px 14px;
}

.spoiler-content *:first-child {
  margin-top: 0;
}

.spoiler-content *:last-child {
  margin-bottom: 0;
}

.spoiler-buttonContainer {
  white-space: normal;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.safeEmoji {
  display: inline-block;
  font-family: "Segoe UI Emoji", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  vertical-align: middle;
  text-align: center;
  height: 1em;
  max-width: 100%;
  line-height: 1em;
}

.nativeEmoji {
  font-family: "Segoe UI Emoji", sans-serif;
}

.fallBackEmoji {
  display: inline-block;
  height: 1em;
  width: 1em;
  margin: 0 .05em 0 .1em;
  vertical-align: -0.1em;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.emojiGroup {
  opacity: 0.6;
}

.emojiPicker {
  position: relative;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.userContent .code {
  position: relative;
  display: inline;
  vertical-align: middle;
  line-height: inherit;
  font-size: 0.85em;
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  max-width: 100%;
  margin: 0;
  color: #26282b;
  background-color: #f7f7f7;
  border: 0;
  overflow-x: auto;
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}

.userContent .codeInline {
  display: inline;
  white-space: normal;
  padding: 0.2em 0.4em;
  border-radius: 0;
}

.userContent .codeBlock {
  display: block;
  word-wrap: normal;
  line-height: 1.45;
  white-space: pre;
  padding: 14px;
  border-radius: 0;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.attachmentsIcons {
  display: block;
  position: relative;
}

.attachmentsIcons-items {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  width: -webkit-calc(100% + 6px);
  width: calc(100% + 6px);
  margin-top: -3px;
  margin-left: -3px;
  margin-right: 6px;
  overflow: hidden;
}

.attachmentsIcons-item {
  margin: 3px;
}

.attachmentIcon {
  display: block;
  width: 16px;
  height: 16px;
  box-shadow: 0 0 0 1px #eeefef;
}

.attachmentIcon-error {
  width: 16px;
  height: 14.39px;
}

/**
 * <AUTHOR> LaFlèche <<EMAIL>>
 * @copyright 2009-2019 Vanilla Forums Inc.
 * @license GPL-2.0-only
 */
.attachment {
  display: block;
  position: relative;
  text-decoration: none;
  color: inherit;
  box-shadow: 0 1px 3px 0 rgba(85, 90, 98, 0.3);
  width: 640px;
  max-width: 100%;
  margin: auto;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border-color: #dddee0;
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;
}

.attachment.hasError {
  background: #FFF3D4;
}

.attachment.hasError, .attachment.isLoading {
  cursor: pointer;
}

.attachment.hasError:hover, .attachment.isLoading:hover {
  box-shadow: 0 0 0 2px rgba(2, 145, 219, 0.5) inset;
}

.attachment.hasError:focus, .attachment.isLoading:focus {
  box-shadow: 0 0 0 2px #0291db inset;
}

.attachment-link, .attachment-link:hover, .attachment-link:active, .attachment-link:visited {
  text-decoration: none;
}

.attachment-box {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
          flex-wrap: nowrap;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 12px;
  border: solid transparent 2px;
  width: 100%;
}

.attachment-format {
  -webkit-flex-basis: 28px;
      -ms-flex-preferred-size: 28px;
          flex-basis: 28px;
  height: 16px;
  padding-right: 12px;
  -webkit-flex-shrink: 1;
      -ms-flex-negative: 1;
          flex-shrink: 1;
}

.attachment-main {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.attachment-title {
  font-size: 14px;
  color: #666b72;
  font-weight: 600;
  line-height: 16px;
}

.attachment-metas {
  margin-bottom: 0;
  line-height: 1.25;
}

.attachment-close {
  margin-top: -10px;
  margin-right: -10px;
  pointer-events: all;
}

.attachment-loadingProgress {
  position: absolute;
  bottom: 0;
  left: 0;
  -webkit-transition: width ease-out .2s;
          transition: width ease-out .2s;
  height: 3px;
  margin-bottom: -1px;
  width: 0;
  max-width: 100%;
  background-color: #0291db;
}

.attachment-loadingContent .attachment-format,
.attachment-loadingContent .attachment-main {
  opacity: .5;
}
/*# sourceMappingURL=style-compat.css.map */