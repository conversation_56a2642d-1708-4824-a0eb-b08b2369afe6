body {
    font-family: 'lucida grande', 'Lucida Sans Unicode', tahoma, sans-serif;
    background: #C7E6FB;
    margin: 0px;
    padding: 0px;
    text-align: center;
    color: #076C8E;
    text-shadow: 0 1px 0 #FFFFFF;
}

a,
a:link,
a:active,
a:visited {
    color: #2786C2;
    text-decoration: none;
}

a:hover {
    color: #2786C2 !important;
    text-decoration: underline;
}

div.Title {
    background: #E2F4FF none repeat scroll 0 0;
    border-top: 1px solid #A5D0E7;
    border-bottom: 1px solid #A5D0E7;
    margin: 50px 0;
    padding: 30px 0 4px;
}

div.Title h1 {
    text-align: left;
    width: 600px;
    margin: 0 auto;
}

div.Title img {
    top: 20px;
    position: absolute;
}

div.Title p {
    padding: 0 0 0 270px;
    margin: 0;
    font-size: 30px;
}

h1 {
    font-family: Arial, Helvetica, Verdana;
    color: #02455B;
    width: 568px;
    margin: 0 auto;
    padding: 0;
    font-size: 180%;
}

div.Form {
    text-align: center;
}

div.Form ul {
    width: 500px;
    margin: 0 auto;
    padding: 0;
}

div.Errors {
    background: #d00;
    padding: 20px 8px !important;
    margin: 0 0 10px;
    border-bottom: 1px solid #C0E7F5;
}

.Errors li {
    padding: 4px 0 !important;
    border: 0px !important;
    margin: 0px !important;
    color: #fff !important;
    font-size: 16px;
    line-height: 150%;
    text-shadow: #900 0 1px 0;
}

.Errors li label {
    color: #fff !important;
    text-shadow: #900 0 1px 0;
}

.Errors li pre,
.Errors li code {
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border: 1px solid #b00;
    background: #c00;
    margin: 10px 0 0;
    padding: 4px 8px;
    display: block;
    text-shadow: none;
    font-size: 13px;
    font-weight: normal;
    font-family: monospace;
}

.Errors li a {
    color: #ffff00;
    text-decoration: underline;
}

.Errors li a:hover {
    color: #ff0 !important;
    text-decoration: none;
}

.ImportProgress {
    padding-left: 40px;
    background: url('images/progress.gif') left center no-repeat;
}

.ImportProgress strong {
    background: #ff9;
    color: #000;
    padding: 3px 6px;
    margin: 0 4px;
    border-radius: 2px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
}

.Loading {
    height: 100px;
    background: url('images/progress.gif') center center no-repeat;
}

.Progress {
    padding: 10px 40px 10px 0px;
    background: url('images/progress.gif') center center no-repeat;
}

.Hidden {
    display: none;
}

/* Forms */
form {
    margin: 0 0 20px;
    text-align: right;
}

form ul {
    text-align: left;
    list-style: none;
    margin: 0px;
    padding: 10px;
}

form ul li {
    padding: 10px 0;
    font-size: 18px;
}

form ul li.Warning {
    padding-bottom: 0;
    border-bottom: 0;
    font-size: 17px;
}

form ul li.Warning div {
    font-size: 14px;
    line-height: 1.6;
    color: #000;
    text-shadow: none;
    padding: 16px 0 8px;
}

form ul li label {
    font-family: Arial, Helvetica, Verdana;
    font-weight: bold;
    display: block;
    padding: 8px 0 0;
    font-size: 110%;
    color: #02455B;
}

form ul li input.InputBox {
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    font-size: 110%;
    padding: 8px;
    width: 480px;
    border: 1px solid #ccc;
    color: #555;
}

form ul li input.InputBox:focus {
    color: #000;
    background: #FFFEDE;
    border: 1px solid #aaa;
}

form ul li.Last {
    padding: 12px 0 2px;
    border-bottom: 0;
}

div.Button {
    text-align: right;
    padding: 12px 0 30px;
    width: 496px;
    margin: 0 auto;
}

div.Button a,
input.Button {
    cursor: pointer;
    font-family: arial, helvetica, verdana;
    font-size: 25px;
    font-weight: bold;
    color: #02475A;
    text-shadow: 0 1px 0 #fff;
    margin: 0;
    padding: 3px 10px;
    background: #f8f8f8;
    border: 1px solid #999;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    box-shadow: 0px 0px 2px #999;
    -moz-box-shadow: 0px 0px 2px #999;
    -webkit-box-shadow: 0px 0px 2px #999;
}

div.Button a {
    padding: 4px 8px;
}

div.Button a:hover,
input.Button:hover {
    text-decoration: none;
    color: #111;
    border: 1px solid #666;
}

div.Button a:focus,
input.Button:focus {
    background: #eee;
}

/* readme.html */
div.Info {
    text-align: left;
    width: 568px;
    margin: 0 auto 70px;
    font-size: 80%;
    line-height: 1.6;
}

div.Info h1 {
    padding: 6px 0 0;
    margin: 0;
}

div.Info p {
    color: #000;
    padding: 3px 0 6px;
    margin: 0;
    text-shadow: none;
}

div.Info li {
    color: #000;
    padding: 1px 0;
    margin: 0;
    text-shadow: none;
}
