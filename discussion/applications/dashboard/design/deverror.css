body {
    background: #e7e7de;
    font-family: arial, tahoma, trebuchet ms, sans-serif;
    margin: 0px;
    padding: 0px;
    text-align: center;
    font-size: small;
}

#Frame {
    width: 980px;
    text-align: left;
    margin-left: auto;
    margin-right: auto;
}

#Content {
    background: #fff;
    padding: 0px 20px 20px 20px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    -moz-border-radius-topleft: 10px;
    -moz-border-radius-topright: 10px;
    -webkit-border-top-left-radius: 10px;
    -webkit-border-top-right-radius: 10px;
}

code {
    display: block;
    padding: 4px 0px 0px 4px;
    color: #ff0084;
    overflow: auto;
    white-space: pre;
}

.PreContainer {
    overflow: auto;
}

pre {
    margin: 0px;
    padding: 2px;
    background: #ffffd3;
}

pre.Odd {
    background: #ffffb9;
}

pre.Highlight {
    color: #ff0000;
    background: #ffff7f;
}

a,
a:link,
a:active {
    color: #0063dc;
    text-decoration: none;
}

a:visited {
    color: #ff0084;
}

a:hover {
    color: #ffffff !important;
    background: #0063dc !important;
}

p {
    padding: 4px 0px;
    margin: 0px;
}

h2 {
    margin: 0px;
    padding: 20px 0px 0px 0px;
}

#MoreInformation {
    background: #f3f3f3;
    padding: 0px 20px 20px 20px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    -moz-border-radius-bottomleft: 10px;
    -moz-border-radius-bottomright: 10px;
    -webkit-border-bottom-left-radius: 10px;
    -webkit-border-bottom-right-radius: 10px;
    margin-bottom: 20px;
}

h3 {
    margin: 0px;
    padding: 20px 0px 8px 0px;
    font-weight: normal;
    font-size: small;
}

h4 {
    margin: 0px;
    padding: 0px;
    font-weight: normal;
}

ul {
    margin: 0px;
    padding: 10px 20px 0px 20px;
}

ul li {
    line-height: 160%;
}
