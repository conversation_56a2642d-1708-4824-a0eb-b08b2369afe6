/*
Includes CSS for pages where vanilla elements are embedded into other
applications.
*/

/* Vanilla Credit */
.vanilla-credit {
    text-align: right;
    font-family: "lucida grande", tahoma, verdana, arial, sans-serif !important;
    font-size: 11px !important;
    font-weight: normal !important;
    text-decoration: none !important;
    display: block;
    line-height: 14px !important;
    padding: 6px 2px 0 !important;
}

.vanilla-anchor,
.vanilla-anchor:hover {
    font-size: 9px !important;
    text-decoration: none !important;
}

.vanilla-logo {
    line-height: 25px !important;
    text-indent: -100px !important;
    text-align: left !important;
    vertical-align: middle !important;
    display: inline-block !important;
    height: 22px !important;
    width: 46px !important;
    background: url('images/vanilla-logo-46px.png') 0 0 no-repeat;
    overflow: hidden !important;
    color: transparent !important;
    font-size: 1px !important;
}

@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (   min--moz-device-pixel-ratio: 2), only screen and (     -o-min-device-pixel-ratio: 2/1), only screen and (        min-device-pixel-ratio: 2), only screen and (                min-resolution: 192dpi), only screen and (                min-resolution: 2dppx) {
    .vanilla-logo {
        background: url('images/<EMAIL>') 0 -2px no-repeat;
        background-size: 46px;
    }
}
