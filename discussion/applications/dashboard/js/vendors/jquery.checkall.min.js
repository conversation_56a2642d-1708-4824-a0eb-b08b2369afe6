/** File generated -- do not modify
 *  JQUERY-CHECKALL
 *
 *  @version 0.0.3
 *  @website http://simivar.github.io/jquery-checkall/
 *  <AUTHOR>
 *  @license 
 */
!function(a){"use strict";function b(b,c){function d(){g.uniform&&(a.uniform.update(i),a.uniform.update(h)),g.icheck&&(i.iCheck("update"),h.iCheck("update"))}function e(){f=a(g.target+":checked").length,f>0?g.onAnyTargetChecked(f):g.onNoTargetChecked()}var f=0,g=a.extend({target:null,uniform:!1,icheck:!1,onAnyTargetChecked:function(a){},onNoTargetChecked:function(){},onTargetClick:function(a){},onElementClick:function(a){}},c);if(null===g.target&&"object"==typeof g.target)throw new Error("checkAll: element has no target specified.");if(g.uniform&&!jQuery().uniform)throw new Error("checkAll: setting 'uniform' set to 'true' yet can not locate uniformjs.");if(g.icheck&&!jQuery().iCheck)throw new Error("checkAll: setting 'icheck' set to 'true' yet can not locate iCheck.");var h=a(b),i=a(g.target);h.on("click ifToggled",function(b){i.check(h.check()),e(),g.onElementClick(a(b.target)),d()}),i.on("click ifToggled",function(b){e(),g.onTargetClick(a(b.target));var c=i.length;f===c?h.check(!0):h.check(!1),d()})}function c(b,c){var d=a(b);return"undefined"==typeof c?b.length>1?!1:d.prop("checked"):(d.prop("checked",c),d)}a.extend(a.fn,{checkall:function(a){return this.each(function(){b(this,a)})},check:function(a){return c(this,a)}})}(jQuery);