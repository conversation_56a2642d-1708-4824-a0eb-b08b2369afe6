<?php if (!defined('APPLICATION')) exit();

// Badges
$Configuration['Badges']['BadgesModule']['Target'] = 'AfterUserInfo';

// Conversations
$Configuration['Conversations']['Version'] = '2.4.201';

// Database
// $Configuration['Database']['Name'] = 'vanilla';
// $Configuration['Database']['Host'] = 'mysql';
// $Configuration['Database']['User'] = 'vanilla_user';
// $Configuration['Database']['Password'] = 'vanilla_password';
$Configuration['Database']['Name'] = 'vanilla_tipcutmove';
$Configuration['Database']['Host'] = 'tthto.h.filess.io';
$Configuration['Database']['Port'] = '3307';
$Configuration['Database']['User'] = 'vanilla_tipcutmove';
$Configuration['Database']['Password'] = 'ef47f20f4dd31b8f43ea9b91f141435cbb85aa9b';

$Configuration['Debug'] = false;

// EnabledApplications
$Configuration['EnabledApplications']['Conversations'] = 'conversations';
$Configuration['EnabledApplications']['Vanilla'] = 'vanilla';

// EnabledPlugins
$Configuration['EnabledPlugins']['recaptcha'] = true;
$Configuration['EnabledPlugins']['GettingStarted'] = 'GettingStarted';
$Configuration['EnabledPlugins']['stubcontent'] = true;
$Configuration['EnabledPlugins']['swagger-ui'] = true;
$Configuration['EnabledPlugins']['Quotes'] = false;
$Configuration['EnabledPlugins']['rich-editor'] = true;
$Configuration['EnabledPlugins']['editor'] = true;
$Configuration['EnabledPlugins']['Flagging'] = false;
$Configuration['EnabledPlugins']['jsconnect'] = true;
$Configuration['EnabledPlugins']['jsconnectAutoSignIn'] = false;
$Configuration['EnabledPlugins']['StopForumSpam'] = true;
$Configuration['EnabledPlugins']['EMailDiscussion'] = false;
$Configuration['EnabledPlugins']['Msknotification'] = true;
$Configuration['EnabledPlugins']['PostFilter'] = true;

// Feature
$Configuration['Feature']['NewFlyouts']['Enabled'] = false;

// Garden
$Configuration['Garden']['Title'] = 'Milk Street Cooking Forum';
$Configuration['Garden']['Cookie']['Salt'] = 'aUnLtYeasbcFZcar';
$Configuration['Garden']['Cookie']['Domain'] = '';
$Configuration['Garden']['RewriteUrls'] = true;
$Configuration['Garden']['Registration']['ConfirmEmail'] = '1';
$Configuration['Garden']['Registration']['Method'] = 'Connect';
$Configuration['Garden']['Registration']['InviteExpiration'] = '1 week';
$Configuration['Garden']['Registration']['InviteRoles'][3] = '0';
$Configuration['Garden']['Registration']['InviteRoles'][4] = '0';
$Configuration['Garden']['Registration']['InviteRoles'][8] = '0';
$Configuration['Garden']['Registration']['InviteRoles'][16] = '0';
$Configuration['Garden']['Registration']['InviteRoles'][32] = '0';
$Configuration['Garden']['Registration']['InviteRoles'][33] = '0';
$Configuration['Garden']['Registration']['InviteRoles'][34] = '0';
$Configuration['Garden']['Registration']['InviteRoles'][35] = '0';
$Configuration['Garden']['Registration']['InviteRoles'][36] = '0';
$Configuration['Garden']['Registration']['AutoConnect'] = '1';
$Configuration['Garden']['Email']['SupportName'] = 'Milk Street Forum';
$Configuration['Garden']['Email']['Format'] = 'html';
$Configuration['Garden']['Email']['SupportAddress'] = '<EMAIL>';
$Configuration['Garden']['Email']['UseSmtp'] = '1';
$Configuration['Garden']['Email']['SmtpHost'] = 'smtp.sendgrid.net';
$Configuration['Garden']['Email']['SmtpUser'] = 'apikey';
$Configuration['Garden']['Email']['SmtpPassword'] = '*********************************************************************';
$Configuration['Garden']['Email']['SmtpPort'] = '587';
$Configuration['Garden']['Email']['SmtpSecurity'] = 'tls';
$Configuration['Garden']['Email']['OmitToName'] = false;
$Configuration['Garden']['SystemUserID'] = '1';
$Configuration['Garden']['InputFormatter'] = 'Rich';
$Configuration['Garden']['Version'] = '2.8';
$Configuration['Garden']['CanProcessImages'] = true;
$Configuration['Garden']['MobileInputFormatter'] = 'Rich';
$Configuration['Garden']['Installed'] = true;
$Configuration['Garden']['AllowFileUploads'] = true;
$Configuration['Garden']['SignIn']['Popup'] = false;
$Configuration['Garden']['Theme'] = 'planetary';
$Configuration['Garden']['MobileTheme'] = 'planetary';
$Configuration['Garden']['CurrentTheme'] = 'planetary';
$Configuration['Garden']['ThemeOptions']['Styles']['Key'] = 'Default';
$Configuration['Garden']['ThemeOptions']['Styles']['Value'] = '%s_default';
$Configuration['Garden']['ThemeOptions']['Options']['hasHeroBanner'] = false;
$Configuration['Garden']['ThemeOptions']['Options']['hasFeatureSearchbox'] = false;
$Configuration['Garden']['ThemeOptions']['Options']['panelToLeft'] = false;
$Configuration['Garden']['Authenticator']['SignOutUrl'] = '/entry/signout/{Session_TransientKey}?Target=https://staging2.177milkstreet.com/logout';
$Configuration['Garden']['InstallationID'] = '7F83-6F270D18-A8DD9EE9';
$Configuration['Garden']['InstallationSecret'] = '8cf0c5de056f9bdab14711a7f379a84a4b0ea924';
$Configuration['Garden']['HomepageTitle'] = 'Milk Street Cooking Forum';
$Configuration['Garden']['Description'] = '';
$Configuration['Garden']['Logo'] = '';
$Configuration['Garden']['MobileLogo'] = '';
$Configuration['Garden']['FavIcon'] = '';
$Configuration['Garden']['TouchIcon'] = '';
$Configuration['Garden']['ShareImage'] = '';
$Configuration['Garden']['MobileAddressBarColor'] = '';
$Configuration['Garden']['EmailTemplate']['TextColor'] = '#2c3b4b';
$Configuration['Garden']['EmailTemplate']['BackgroundColor'] = '#ffffff';
$Configuration['Garden']['EmailTemplate']['ContainerBackgroundColor'] = '#ffffff';
$Configuration['Garden']['EmailTemplate']['ButtonTextColor'] = '#ffffff';
$Configuration['Garden']['EmailTemplate']['ButtonBackgroundColor'] = '#F97D36';
$Configuration['Garden']['EmailTemplate']['ImageMaxWidth'] = '564';
$Configuration['Garden']['EmailTemplate']['ImageMaxHeight'] = '341';
$Configuration['Garden']['EmailTemplate']['Image'] = '';
$Configuration['Garden']['Search']['Mode'] = 'like';
$Configuration['Garden']['EditContentTimeout'] = '604800';
$Configuration['Garden']['Format']['DisableUrlEmbeds'] = false;

// Plugins
$Configuration['Plugins']['GettingStarted']['Dashboard'] = '1';
$Configuration['Plugins']['GettingStarted']['Plugins'] = '1';
$Configuration['Plugins']['GettingStarted']['Registration'] = '1';
$Configuration['Plugins']['GettingStarted']['Profile'] = '1';
$Configuration['Plugins']['GettingStarted']['Discussion'] = '1';
$Configuration['Plugins']['editor']['ForceWysiwyg'] = false;
$Configuration['Plugins']['jsconnectAutoSignIn']['HideConnectButton'] = true;
$Configuration['Plugins']['jsconnectAutoSignIn']['HideSignIn'] = true;
$Configuration['Plugins']['StopForumSpam']['UserID'] = '7395';
$Configuration['Plugins']['Msknotification']['mskstuffEmail'] = '<EMAIL>';

// Recaptcha
$Configuration['Recaptcha']['PrivateKey'] = '6Lf3yI8UAAAAAGaxcI8c_kKkDITvhf7BUleA_P3B';
$Configuration['Recaptcha']['PublicKey'] = '6Lf3yI8UAAAAAM71mNTkFIBmgFqy7cCG0XdXmbbf';

// RichEditor
$Configuration['RichEditor']['Quotes']['Enable'] = true;
$Configuration['RichEditor']['Quote']['Enable'] = false;

// Routes
$Configuration['Routes']['YXBwbGUtdG91Y2gtaWNvbi5wbmc='] = array (
  0 => 'utility/showtouchicon',
  1 => 'Internal',
);
$Configuration['Routes']['DefaultController'] = array (
  0 => 'categories',
  1 => 'Internal',
);
$Configuration['Routes']['DefaultForumRoot'] = array (
  0 => 'discussions',
  1 => 'Internal',
);

// Vanilla
$Configuration['Vanilla']['Version'] = '2.4.201';
$Configuration['Vanilla']['Password']['SpamCount'] = 2;
$Configuration['Vanilla']['Password']['SpamTime'] = 1;
$Configuration['Vanilla']['Password']['SpamLock'] = 120;
$Configuration['Vanilla']['Categories']['MaxDisplayDepth'] = '3';
$Configuration['Vanilla']['Categories']['Layout'] = 'modern';
$Configuration['Vanilla']['Discussions']['PerPage'] = '30';
$Configuration['Vanilla']['Discussions']['Layout'] = 'modern';
$Configuration['Vanilla']['Comments']['PerPage'] = '30';
$Configuration['Vanilla']['Comment']['MaxLength'] = '8000';
$Configuration['Vanilla']['Comment']['MinLength'] = '';
$Configuration['Vanilla']['AdminCheckboxes']['Use'] = '1';

// Last edited by Whitney K. (Q&A ID 14247) (2600:4040:5545:2f00:d1fc:db1a:741a:550f) 2023-04-07 13:48:42
